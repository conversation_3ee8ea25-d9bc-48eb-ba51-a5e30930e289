import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/moment_data.dart';

class FactorialComparisonChart extends StatelessWidget {
  final List<MomentData> moments;
  final String selectedPeriod; // 'all', 'last_week', 'last_month', etc.
  final ValueChanged<String>? onPeriodChanged;

  const FactorialComparisonChart({
    super.key,
    required this.moments,
    this.selectedPeriod = 'all',
    this.onPeriodChanged,
  });

  List<MomentData> _filterMomentsByPeriod() {
    if (selectedPeriod == 'all') return moments;

    final now = DateTime.now();
    DateTime cutoffDate;

    switch (selectedPeriod) {
      case 'last_week':
        cutoffDate = now.subtract(const Duration(days: 7));
        break;
      case 'last_month':
        cutoffDate = now.subtract(const Duration(days: 30));
        break;
      case 'last_3_months':
        cutoffDate = now.subtract(const Duration(days: 90));
        break;
      default:
        return moments;
    }

    return moments.where((moment) => moment.timestamp.isAfter(cutoffDate)).toList();
  }

  double _getYAxisMax(List<MomentData> filteredMoments) {
    if (filteredMoments.isEmpty) return 10.0;

    final maxValue = filteredMoments
        .map((m) => m.factorialMultiplier)
        .reduce((a, b) => math.max(a, b));

    // Add some padding to the max value
    if (maxValue < 10) return 10.0;
    if (maxValue < 100) return (maxValue * 1.2).ceilToDouble();
    if (maxValue < 1000) return ((maxValue / 100).ceil() * 100).toDouble();
    return (maxValue * 1.1);
  }

  double _getYAxisMin(List<MomentData> filteredMoments) {
    if (filteredMoments.isEmpty) return 0.01;

    final minValue = filteredMoments
        .map((m) => m.factorialMultiplier)
        .reduce((a, b) => math.min(a, b));

    // Ensure we can see small values
    if (minValue < 0.01) return 0.001;
    if (minValue < 0.1) return 0.01;
    if (minValue < 1.0) return 0.1;
    return math.max(0.01, minValue * 0.8);
  }

  Color _getPointColor(MomentData moment) {
    final value = moment.factorialMultiplier;

    // Extreme improvement (≥100x)
    if (moment.isImprovementComparedToLife && value >= 100) {
      return Colors.green[800]!;
    }
    // Extreme worsening (≤0.01x)
    else if (!moment.isImprovementComparedToLife && value <= 0.01) {
      return Colors.red[800]!;
    }
    // Normal improvement
    else if (moment.isImprovementComparedToLife) {
      return Colors.green[600]!;
    }
    // Normal worsening
    else {
      return Colors.red[600]!;
    }
  }

  double _getPointSize(MomentData moment) {
    final value = moment.factorialMultiplier;

    // Larger points for extreme values
    if ((moment.isImprovementComparedToLife && value >= 100) ||
        (!moment.isImprovementComparedToLife && value <= 0.01)) {
      return 8.0;
    }
    return 5.0;
  }

  @override
  Widget build(BuildContext context) {
    final filteredMoments = _filterMomentsByPeriod();

    if (filteredMoments.isEmpty) {
      return Card(
        child: Container(
          height: 400,
          padding: const EdgeInsets.all(16),
          child: const Center(
            child: Text(
              'No moments to display for the selected period.',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ),
        ),
      );
    }

    final yMax = _getYAxisMax(filteredMoments);
    final yMin = _getYAxisMin(filteredMoments);

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with period selector
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Factorial Comparison Chart',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (onPeriodChanged != null)
                  DropdownButton<String>(
                    value: selectedPeriod,
                    onChanged: (value) => onPeriodChanged!(value!),
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('All Time')),
                      DropdownMenuItem(value: 'last_week', child: Text('Last Week')),
                      DropdownMenuItem(value: 'last_month', child: Text('Last Month')),
                      DropdownMenuItem(value: 'last_3_months', child: Text('Last 3 Months')),
                    ],
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Chart
            SizedBox(
              height: 300,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: true,
                    horizontalInterval: yMax > 100 ? yMax / 5 : 10,
                    verticalInterval: 1,
                    getDrawingHorizontalLine: (value) {
                      // Highlight the baseline (1x)
                      if ((value - 1.0).abs() < 0.1) {
                        return FlLine(
                          color: Colors.blue,
                          strokeWidth: 2,
                          dashArray: [5, 5],
                        );
                      }
                      return FlLine(
                        color: Colors.grey[300]!,
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        interval: 1,
                        getTitlesWidget: (value, meta) {
                          final index = value.toInt();
                          if (index >= 0 && index <= filteredMoments.length) {
                            String label;
                            Color color;

                            if (index == 0) {
                              label = 'Life';
                              color = Colors.blue;
                            } else {
                              label = '$index';
                              color = Colors.grey;
                            }

                            return Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Text(
                                label,
                                style: TextStyle(
                                  color: color,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        interval: yMax > 100 ? yMax / 5 : 10,
                        reservedSize: 60,
                        getTitlesWidget: (value, meta) {
                          if (value < 0.01) {
                            return Text('${(value * 1000).toStringAsFixed(0)}‰');
                          } else if (value < 1.0) {
                            return Text('${(value * 100).toStringAsFixed(0)}%');
                          } else if (value < 1000) {
                            return Text('${value.toStringAsFixed(0)}x');
                          } else {
                            return Text('${(value / 1000).toStringAsFixed(0)}K');
                          }
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(color: const Color(0xff37434d)),
                  ),
                  minX: 0,
                  maxX: filteredMoments.length.toDouble(), // Include life baseline + all moments
                  minY: yMin,
                  maxY: yMax,
                  lineBarsData: [
                    LineChartBarData(
                      spots: () {
                        // Sort moments by timestamp to ensure chronological order
                        final sortedMoments = List<MomentData>.from(filteredMoments);
                        sortedMoments.sort((a, b) => a.timestamp.compareTo(b.timestamp));

                        // Create spots with life baseline as first point and chronological moments
                        final spots = <FlSpot>[];

                        // Always add life baseline as first point
                        spots.add(const FlSpot(0, 1.0));

                        // Add moments in chronological order, all showing their actual factorial values
                        for (int i = 0; i < sortedMoments.length; i++) {
                          final moment = sortedMoments[i];
                          final xPosition = (i + 1).toDouble();
                          // All moments should show their actual recorded factorial multiplier
                          final yValue = moment.factorialMultiplier;
                          spots.add(FlSpot(xPosition, yValue));
                        }

                        return spots;
                      }(),
                      isCurved: true,
                      gradient: LinearGradient(
                        colors: [
                          Colors.blue[400]!,
                          Colors.blue[600]!,
                        ],
                      ),
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          // Sort moments by timestamp to match the spots order
                          final sortedMoments = List<MomentData>.from(filteredMoments);
                          sortedMoments.sort((a, b) => a.timestamp.compareTo(b.timestamp));

                          if (index == 0) {
                            // Life baseline point
                            return FlDotCirclePainter(
                              radius: 8.0,
                              color: Colors.blue[600]!,
                              strokeWidth: 2,
                              strokeColor: Colors.white,
                            );
                          } else if (index <= sortedMoments.length) {
                            // Moment points
                            final moment = sortedMoments[index - 1];
                            return FlDotCirclePainter(
                              radius: _getPointSize(moment),
                              color: _getPointColor(moment),
                              strokeWidth: 2,
                              strokeColor: Colors.white,
                            );
                          }
                          // Fallback
                          return FlDotCirclePainter(
                            radius: 5.0,
                            color: Colors.grey,
                            strokeWidth: 2,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                      belowBarData: BarAreaData(show: false),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Legend
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: [
                _buildLegendItem(Colors.green[600]!, 'Improvement'),
                _buildLegendItem(Colors.red[600]!, 'Worsening'),
                _buildLegendItem(Colors.green[800]!, 'Extreme Improvement (≥100x)'),
                _buildLegendItem(Colors.red[800]!, 'Extreme Worsening (≤0.01x)'),
                _buildLegendItem(Colors.blue, 'Life Baseline (1x)', isDashed: true),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(Color color, String label, {bool isDashed = false}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 3,
          decoration: BoxDecoration(
            color: isDashed ? null : color,
            border: isDashed ? Border.all(color: color) : null,
          ),
          child: isDashed
              ? CustomPaint(
                  painter: DashedLinePainter(color: color),
                )
              : null,
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }
}

class DashedLinePainter extends CustomPainter {
  final Color color;

  DashedLinePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2;

    const dashWidth = 3.0;
    const dashSpace = 2.0;
    double startX = 0;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(math.min(startX + dashWidth, size.width), size.height / 2),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
