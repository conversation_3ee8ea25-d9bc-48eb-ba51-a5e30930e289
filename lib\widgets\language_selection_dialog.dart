import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:estimat_keymoments/l10n/app_localizations.dart';
import '../providers/locale_provider.dart';

class LanguageSelectionDialog extends StatelessWidget {
  const LanguageSelectionDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final localeProvider = Provider.of<LocaleProvider>(context);

    return AlertDialog(
      title: Text(localizations.changeLanguageMenuTitle),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildLanguageOption(
            context,
            'English',
            '🇺🇸',
            const Locale('en'),
            localeProvider.locale.languageCode == 'en',
          ),
          const SizedBox(height: 8),
          _buildLanguageOption(
            context,
            'Español',
            '🇪🇸',
            const Locale('es'),
            localeProvider.locale.languageCode == 'es',
          ),
          const SizedBox(height: 8),
          _buildLanguageOption(
            context,
            'Português',
            '🇧🇷',
            const Locale('pt'),
            localeProvider.locale.languageCode == 'pt',
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(localizations.cancelButton),
        ),
      ],
    );
  }

  Widget _buildLanguageOption(
    BuildContext context,
    String languageName,
    String flag,
    Locale locale,
    bool isSelected,
  ) {
    final localizations = AppLocalizations.of(context)!;
    
    return InkWell(
      onTap: () async {
        final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
        await localeProvider.setLocale(locale);
        
        if (context.mounted) {
          Navigator.of(context).pop();
          
          // Show confirmation message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(localizations.languageChangedMessage),
              duration: const Duration(seconds: 2),
              backgroundColor: const Color(0xFF2A6BAA),
            ),
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF2A6BAA).withOpacity(0.1) : null,
          borderRadius: BorderRadius.circular(8),
          border: isSelected 
            ? Border.all(color: const Color(0xFF2A6BAA), width: 2)
            : Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            Text(
              flag,
              style: const TextStyle(fontSize: 24),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                languageName,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? const Color(0xFF2A6BAA) : Colors.black87,
                ),
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: Color(0xFF2A6BAA),
                size: 20,
              ),
          ],
        ),
      ),
    );
  }
}
