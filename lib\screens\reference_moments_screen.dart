import 'package:flutter/material.dart';
import 'package:estimat_keymoments/l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import '../models/moment_type.dart';
import '../providers/moments_provider.dart';
import '../widgets/app_menu.dart';
import '../widgets/moment_type_flow.dart';

class ReferenceMomentsScreen extends StatefulWidget {
  const ReferenceMomentsScreen({super.key});

  @override
  State<ReferenceMomentsScreen> createState() => _ReferenceMomentsScreenState();
}

class _ReferenceMomentsScreenState extends State<ReferenceMomentsScreen> with WidgetsBindingObserver {
  MomentType _selectedType = MomentType.improvesMotivation;
  final GlobalKey _momentTypeFlowKey = GlobalKey();
  bool _shouldResetOnReturn = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // This method is called when the app lifecycle changes
    // but we need a different approach for route changes
  }

  // This method is called when the route becomes active again
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Check if there's a current moment to edit
    final momentsProvider = Provider.of<MomentsProvider>(context, listen: false);
    final currentMoment = momentsProvider.currentMoment;

    if (currentMoment != null) {
      // Load the moment for editing
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _selectedType = currentMoment.type;
        });
        final state = _momentTypeFlowKey.currentState;
        if (state != null && state is MomentTypeFlowMixin) {
          (state as MomentTypeFlowMixin).loadMomentForEditing(currentMoment);
        }
        // Clear the current moment after loading
        momentsProvider.setCurrentMoment(null);
      });
    } else if (_shouldResetOnReturn) {
      // Reset form when returning to this screen if flag is set
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _resetFormIfNeeded();
        _shouldResetOnReturn = false; // Reset the flag
      });
    }
  }

  void _resetFormIfNeeded() {
    final state = _momentTypeFlowKey.currentState;
    if (state != null && state is MomentTypeFlowMixin) {
      (state as MomentTypeFlowMixin).resetForm();
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final momentsProvider = Provider.of<MomentsProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          localizations.recognizeYourMomentOrientationLevel,
          style: const TextStyle(fontSize: 16),
          overflow: TextOverflow.visible,
          softWrap: true,
          maxLines: 2,
        ),
        actions: const [
          AppMenu(),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: MomentTypeFlow(
          key: _momentTypeFlowKey,
          momentType: _selectedType,
          onComplete: (moment) {
            // Check if this is an existing moment by looking for the ID in the moments list
            if (momentsProvider.moments.any((m) => m.id == moment.id)) {
              // Update existing moment
              momentsProvider.updateMoment(moment);
            } else {
              // Add new moment
              momentsProvider.addMoment(moment);
            }
            // Navigate to life possibilities screen (Screen 2) with required arguments
            Navigator.pushNamed(
              context,
              '/improve_worse_comparison',
              arguments: {
                'momentType': moment.type,
                'currentMoment': moment,
              },
            );
          },
          onMomentTypeSelected: (selectedType) {
            setState(() {
              _selectedType = selectedType;
            });
          },
        ),
      ),
    );
  }

}
