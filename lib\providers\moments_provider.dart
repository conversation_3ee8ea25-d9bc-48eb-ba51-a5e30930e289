import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/moment_data.dart';
import '../models/moment_type.dart';

class MomentsProvider extends ChangeNotifier {
  List<MomentData> _moments = [];

  // Reference moments for each type
  MomentData? _improvesMotivationMoment;
  MomentData? _worsensMotivationMoment;
  MomentData? _improvesSatisfactionMoment;
  MomentData? _worsensSatisfactionMoment;

  // Current moment being edited
  MomentData? _currentMoment;

  // Getters
  List<MomentData> get moments => _moments;
  MomentData? get improvesMotivationMoment => _improvesMotivationMoment;
  MomentData? get worsensMotivationMoment => _worsensMotivationMoment;
  MomentData? get improvesSatisfactionMoment => _improvesSatisfactionMoment;
  MomentData? get worsensSatisfactionMoment => _worsensSatisfactionMoment;
  MomentData? get currentMoment => _currentMoment;

  MomentsProvider() {
    loadMoments();
  }

  Future<void> loadMoments() async {
    final prefs = await SharedPreferences.getInstance();
    final momentsJson = prefs.getStringList('moments') ?? [];

    _moments = momentsJson
        .map((json) => _momentFromJson(jsonDecode(json)))
        .toList();

    _updateReferenceMoments();
    notifyListeners();
  }

  Future<void> saveMoments() async {
    final prefs = await SharedPreferences.getInstance();
    final momentsJson = _moments
        .map((moment) => jsonEncode(_momentToJson(moment)))
        .toList();

    await prefs.setStringList('moments', momentsJson);
  }

  void addMoment(MomentData moment) {
    _moments.add(moment);
    _updateReferenceMoments();
    saveMoments();
    notifyListeners();
  }

  void updateMoment(MomentData moment) {
    final index = _moments.indexWhere((m) => m.id == moment.id);
    if (index != -1) {
      _moments[index] = moment;
      _updateReferenceMoments();
      saveMoments();
      notifyListeners();
    }
  }

  void deleteMoment(String id) {
    _moments.removeWhere((moment) => moment.id == id);
    _updateReferenceMoments();
    saveMoments();
    notifyListeners();
  }

  void setCurrentMoment(MomentData? moment) {
    _currentMoment = moment;
    notifyListeners();
  }

  void _updateReferenceMoments() {
    // Find the most recent moment of each type
    _improvesMotivationMoment = _findMostRecentMomentOfType(MomentType.improvesMotivation);
    _worsensMotivationMoment = _findMostRecentMomentOfType(MomentType.worsensMotivation);
    _improvesSatisfactionMoment = _findMostRecentMomentOfType(MomentType.improvesSatisfaction);
    _worsensSatisfactionMoment = _findMostRecentMomentOfType(MomentType.worsensSatisfaction);
  }

  MomentData? _findMostRecentMomentOfType(MomentType type) {
    final momentsOfType = _moments.where((moment) => moment.type == type).toList();
    if (momentsOfType.isEmpty) return null;

    momentsOfType.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return momentsOfType.first;
  }

  Map<String, dynamic> _momentToJson(MomentData moment) {
    return {
      'id': moment.id,
      'timestamp': moment.timestamp.toIso8601String(),
      'type': moment.type.index,
      'title': moment.title,
      'description': moment.description,
      'elementalPercentage': moment.elementalPercentage,
      'personalPercentage': moment.personalPercentage,
      'informationalPercentage': moment.informationalPercentage,
      'socialPercentage': moment.socialPercentage,
      'inwardPercentage': moment.inwardPercentage,
      'outwardPercentage': moment.outwardPercentage,
      'elementalPersonalEvidence': moment.elementalPersonalEvidence,
      'personalInformationalEvidence': moment.personalInformationalEvidence,
      'informationalSocialEvidence': moment.informationalSocialEvidence,
      'inwardOutwardEvidence': moment.inwardOutwardEvidence,
      'momentTypeEvidence': moment.momentTypeEvidence, // Renamed
      'improvesWorsensRatio': moment.improvesWorsensRatio,
      // New factorial-based fields
      'isImprovementComparedToLife': moment.isImprovementComparedToLife,
      'factorialSliderValue': moment.factorialSliderValue,
    };
  }

  MomentData _momentFromJson(Map<String, dynamic> json) {
    return MomentData(
      id: json['id'],
      timestamp: DateTime.parse(json['timestamp']),
      type: MomentType.values[json['type']],
      title: json['title'],
      description: json['description'],
      elementalPercentage: json['elementalPercentage'],
      personalPercentage: json['personalPercentage'],
      informationalPercentage: json['informationalPercentage'],
      socialPercentage: json['socialPercentage'],
      inwardPercentage: json['inwardPercentage'],
      outwardPercentage: json['outwardPercentage'],
      elementalPersonalEvidence: json['elementalPersonalEvidence'] ?? '',
      personalInformationalEvidence: json['personalInformationalEvidence'] ?? '',
      informationalSocialEvidence: json['informationalSocialEvidence'] ?? '',
      inwardOutwardEvidence: json['inwardOutwardEvidence'] ?? '',
      momentTypeEvidence: json['momentTypeEvidence'] ?? '', // Renamed
      improvesWorsensRatio: json['improvesWorsensRatio'] ?? 0.5,
      // New factorial-based fields with backward compatibility
      isImprovementComparedToLife: json['isImprovementComparedToLife'] ?? true,
      factorialSliderValue: json['factorialSliderValue'] ?? 1.0,
    );
  }
}
