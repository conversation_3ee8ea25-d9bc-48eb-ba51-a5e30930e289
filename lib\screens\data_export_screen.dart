import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../providers/moments_provider.dart';
import '../services/csv_export_service.dart';
import '../widgets/app_menu.dart';

class DataExportScreen extends StatefulWidget {
  const DataExportScreen({super.key});

  @override
  State<DataExportScreen> createState() => _DataExportScreenState();
}

class _DataExportScreenState extends State<DataExportScreen> {
  bool _isExporting = false;
  String? _exportedFilePath;
  String? _errorMessage;
  String? _csvData; // Store CSV data if file export fails

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.exportDataTitle),
        actions: const [
          AppMenu(),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizations.exportOptionsTitle,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: ListTile(
                leading: const Icon(Icons.file_download),
                title: Text(localizations.exportToCSVTitle),
                subtitle: Text(localizations.exportToCSVSubtitle),
                onTap: _isExporting ? null : _exportToCSV,
              ),
            ),
            const SizedBox(height: 24),
            if (_isExporting)
              const Center(
                child: CircularProgressIndicator(),
              )
            else if (_exportedFilePath != null && _csvData == null)
              Card(
                color: Colors.green[100],
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        localizations.exportSuccessfulTitle,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(localizations.fileSavedToLabel(_exportedFilePath!)),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          ElevatedButton.icon(
                            onPressed: () => _viewExportedFile(_exportedFilePath!),
                            icon: const Icon(Icons.visibility),
                            label: Text(localizations.viewFileButton),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              )
            else if (_csvData != null)
              Card(
                color: Colors.green[100],
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        localizations.exportSuccessfulTitle,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(localizations.csvDataExportedSuccessfully),
                      const SizedBox(height: 8),
                      Container(
                        height: 200,
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: SingleChildScrollView(
                          child: Text(_csvData!),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else if (_errorMessage != null)
              Card(
                color: Colors.red[100],
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        localizations.exportFailedTitle,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(_errorMessage!),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _exportToCSV() async {
    final localizations = AppLocalizations.of(context)!;

    setState(() {
      _isExporting = true;
      _exportedFilePath = null;
      _errorMessage = null;
    });

    try {
      final momentsProvider = Provider.of<MomentsProvider>(context, listen: false);
      final moments = momentsProvider.moments;

      if (moments.isEmpty) {
        setState(() {
          _isExporting = false;
          _errorMessage = localizations.noMomentsToExport;
        });
        return;
      }

      final csvExportService = CSVExportService();
      final result = await csvExportService.exportToCSV(moments);

      // Check if the result is a file path or CSV data
      if (result.startsWith('/') || result.contains('\\')) {
        // It's a file path
        setState(() {
          _isExporting = false;
          _exportedFilePath = result;
        });
      } else {
        // It's CSV data
        setState(() {
          _isExporting = false;
          _exportedFilePath = localizations.csvDataExportedSuccessfully;
          _csvData = result;
        });
      }
    } catch (e) {
      setState(() {
        _isExporting = false;
        _errorMessage = localizations.errorExportingData(e.toString());
      });
    }
  }

  Future<void> _viewExportedFile(String filePath) async {
    final localizations = AppLocalizations.of(context)!;

    try {
      final file = File(filePath);
      if (await file.exists()) {
        if (!mounted) return;
        Navigator.pushNamed(
          context,
          '/csv_viewer',
          arguments: filePath,
        );
      } else {
        if (!mounted) return;
        setState(() {
          _errorMessage = localizations.fileNotFound(filePath);
          _exportedFilePath = null;
        });
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _errorMessage = localizations.errorOpeningFile(e.toString());
        _exportedFilePath = null;
      });
    }
  }


}
