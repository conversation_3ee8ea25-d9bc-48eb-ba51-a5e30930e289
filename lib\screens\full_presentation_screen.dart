import 'package:flutter/material.dart';
import 'package:estimat_keymoments/l10n/app_localizations.dart';
import '../widgets/app_menu.dart';

class FullPresentationScreen extends StatefulWidget {
  const FullPresentationScreen({super.key});

  @override
  State<FullPresentationScreen> createState() => _FullPresentationScreenState();
}

class _FullPresentationScreenState extends State<FullPresentationScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final int _totalPages = 8;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.viewFullPresentationMenu),
        backgroundColor: const Color(0xFF2A6BAA),
        actions: const [
          AppMenu(),
        ],
      ),
      body: Column(
        children: [
          // Page indicator
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(_totalPages, (index) {
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4.0),
                  width: 8.0,
                  height: 8.0,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentPage == index ? Colors.blue : Colors.grey[300],
                  ),
                );
              }),
            ),
          ),
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (page) {
                setState(() {
                  _currentPage = page;
                });
              },
              children: [
                _buildIntroductionPage(context, localizations),
                _buildWhyMomentsPage(context, localizations),
                _buildMethodologyPage(context, localizations),
                _buildFourLevelsPage(context, localizations),
                _buildDirectionsPage(context, localizations),
                _buildLatentValuesPage(context, localizations),
                _buildApplicationPage(context, localizations),
                _buildConclusionPage(context, localizations),
              ],
            ),
          ),
          // Navigation buttons
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ElevatedButton(
                  onPressed: _currentPage > 0 ? _previousPage : null,
                  child: Text(localizations.previousButton),
                ),
                Text(localizations.pageCounter(_currentPage + 1, _totalPages)),
                ElevatedButton(
                  onPressed: _currentPage < _totalPages - 1 ? _nextPage : null,
                  child: Text(localizations.nextButton),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIntroductionPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '🎯',
      localizations.welcomeToEstimatKeyMoments,
      localizations.estimatKeyMomentsDescription,
      Colors.deepPurple.shade100,
    );
  }

  Widget _buildWhyMomentsPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '❓',
      localizations.whyFocusOnMomentsTitle,
      localizations.whyFocusOnMomentsContent,
      Colors.blue.shade100,
    );
  }

  Widget _buildMethodologyPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '🔬',
      localizations.fullPresentationMethodologyTitle,
      localizations.fullPresentationMethodologyContent,
      Colors.green.shade100,
    );
  }

  Widget _buildFourLevelsPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '🏗️',
      localizations.fullPresentationFourLevelsTitle,
      '**${localizations.elementalLabel}**: ${localizations.fullPresentationElementalDescription}\n'
      '**${localizations.personalLabel}**: ${localizations.fullPresentationPersonalDescription}\n'
      '**${localizations.informationalLabel}**: ${localizations.fullPresentationInformationalDescription}\n'
      '**${localizations.socialLabel}**: ${localizations.fullPresentationSocialDescription}\n\n'
      '${localizations.fullPresentationFourLevelsContent}',
      Colors.amber.shade100,
    );
  }

  Widget _buildDirectionsPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '🧭',
      localizations.fullPresentationDirectionalFocusTitle,
      '**${localizations.inwardLabel}**: ${localizations.inwardExplanation}\n\n'
      '**${localizations.outwardLabel}**: ${localizations.outwardExplanation}\n\n'
      '${localizations.fullPresentationDirectionalFocusContent}',
      Colors.purple.shade100,
    );
  }

  Widget _buildLatentValuesPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '💎',
      localizations.discoveringLatentValuesTitle,
      localizations.discoveringLatentValuesContent(
        localizations.guardianLabel,
        localizations.warriorLabel,
        localizations.versatileLabel,
        localizations.funLabel,
      ),
      Colors.red.shade100,
    );
  }

  Widget _buildApplicationPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '🎯',
      localizations.fullPresentationPracticalApplicationTitle,
      localizations.fullPresentationPracticalApplicationContent,
      Colors.teal.shade100,
    );
  }

  Widget _buildConclusionPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '🌟',
      localizations.fullPresentationConclusionTitle,
      localizations.fullPresentationConclusionContent,
      Colors.orange.shade100,
    );
  }

  Widget _buildPage(BuildContext context, String emoji, String title, String content, Color backgroundColor) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: backgroundColor,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            emoji,
            style: const TextStyle(fontSize: 80),
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Expanded(
            child: SingleChildScrollView(
              child: Text(
                content,
                style: const TextStyle(
                  fontSize: 18,
                  color: Colors.black87,
                  height: 1.6,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
