import 'dart:math' as math;
import 'package:flutter/material.dart';

class FactorialSlider extends StatelessWidget {
  final double value; // 0-100 slider value
  final ValueChanged<double> onChanged;
  final bool isImprovement; // true for improvement, false for worsening
  final String title;
  final double minValue; // Minimum slider value (default 1.0)
  final double? previousMomentValue; // Previous moment value for subtraction calculation

  const FactorialSlider({
    super.key,
    required this.value,
    required this.onChanged,
    required this.isImprovement,
    required this.title,
    this.minValue = 1.0,
    this.previousMomentValue,
  });

  // Calculate factorial value - always use factorial calculation
  static double _calculateFactorial(double sliderValue) {
    final clampedValue = sliderValue.clamp(1.0, 100.0); // Always 1x minimum for factorial

    // For values 1-20, calculate actual factorial
    if (clampedValue <= 20.0) {
      int intValue = clampedValue.round();
      double result = 1.0;
      for (int i = 1; i <= intValue; i++) {
        result *= i;
      }
      return result;
    }

    // For values > 20, use approximation to avoid overflow
    final n = clampedValue;
    return math.sqrt(2 * math.pi * n) * math.pow(n / math.e, n);
  }

  String _formatFactorialValue(double factorialValue) {
    if (factorialValue < 0.01) {
      return '${(factorialValue * 1000).toStringAsFixed(1)}‰'; // Per mille
    } else if (factorialValue < 1.0) {
      return '${(factorialValue * 100).toStringAsFixed(1)}%';
    } else if (factorialValue < 1000) {
      return '${factorialValue.toStringAsFixed(1)}x';
    } else if (factorialValue < 1000000) {
      return '${(factorialValue / 1000).toStringAsFixed(1)}K';
    } else if (factorialValue < 1000000000) {
      return '${(factorialValue / 1000000).toStringAsFixed(1)}M';
    } else {
      return '${(factorialValue / 1000000000).toStringAsFixed(1)}B';
    }
  }

  // Calculate the final result value (factorial for improvement, subtraction for worsening)
  double _calculateFinalValue() {
    final factorialValue = _calculateFactorial(value);

    if (isImprovement || previousMomentValue == null) {
      // For improvement or first moment, return factorial value directly
      return factorialValue;
    } else {
      // For "fewer possibilities", subtract factorial from previous moment
      // But never go below 1x (life baseline)
      final result = previousMomentValue! - factorialValue;
      return math.max(1.0, result);
    }
  }

  @override
  Widget build(BuildContext context) {
    final factorialValue = _calculateFactorial(value);
    final finalValue = _calculateFinalValue();

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Title (only show if not empty)
        if (title.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isImprovement ? Colors.green[50] : Colors.red[50],
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
                color: isImprovement ? Colors.green[600] : Colors.red[600],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 16),
        ],

        // Final result display
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          decoration: BoxDecoration(
            color: isImprovement ? Colors.green[50] : Colors.red[50],
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isImprovement ? Colors.green[300]! : Colors.red[300]!,
              width: 2,
            ),
          ),
          child: Column(
            children: [
              Text(
                _formatFactorialValue(finalValue),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 24,
                  color: isImprovement ? Colors.green[800] : Colors.red[800],
                ),
              ),
              if (!isImprovement && previousMomentValue != null) ...[
                const SizedBox(height: 4),
                Text(
                  '${_formatFactorialValue(previousMomentValue!)} - ${_formatFactorialValue(factorialValue)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.red[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ],
          ),
        ),
        const SizedBox(height: 24),

        // Vertical slider
        Container(
          height: 300,
          width: 80,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: isImprovement ? Alignment.bottomCenter : Alignment.bottomCenter, // Both start from bottom
              end: isImprovement ? Alignment.topCenter : Alignment.topCenter, // Both end at top
              colors: isImprovement
                ? [Colors.green[100]!, Colors.green[600]!] // Green: light to dark (up = more)
                : [Colors.red[600]!, Colors.red[100]!], // Red: dark to light (up = less bad)
            ),
            borderRadius: BorderRadius.circular(40),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: RotatedBox(
            quarterTurns: 3,
            child: Slider(
              value: value,
              onChanged: onChanged,
              min: minValue, // Use custom minimum value
              max: 100.0, // Both modes use full factorial range (1-100)
              divisions: (100 - minValue).round(), // Adjust divisions based on range
              activeColor: isImprovement ? Colors.green[700] : Colors.red[700],
              inactiveColor: isImprovement ? Colors.green[200] : Colors.red[200],
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Baseline reference
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Text(
            isImprovement
              ? 'Minimum: ${minValue.toStringAsFixed(1)}x (${minValue > 1.0 ? "must be positive" : "life baseline"})'
              : previousMomentValue != null
                ? 'Subtracts factorial from previous moment (${_formatFactorialValue(previousMomentValue!)}) but never below 1x'
                : 'Factorial subtraction mode (never below 1x life baseline)',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}
