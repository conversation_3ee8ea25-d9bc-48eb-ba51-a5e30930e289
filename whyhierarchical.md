import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Brain, BarChart3, Zap, Target, Eye, AlertTriangle } from 'lucide-react';

const HierarchicalOrganizationApp = () => {
  const [expandedSections, setExpandedSections] = useState({});
  const [expandedSubsections, setExpandedSubsections] = useState({});

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const toggleSubsection = (subsection) => {
    setExpandedSubsections(prev => ({
      ...prev,
      [subsection]: !prev[subsection]
    }));
  };

  const SectionHeader = ({ icon: Icon, title, section, isExpanded, color = "blue" }) => (
    <button
      onClick={() => toggleSection(section)}
      className={`w-full flex items-center justify-between p-4 bg-${color}-50 hover:bg-${color}-100 rounded-lg border border-${color}-200 transition-all duration-200 mb-2`}
    >
      <div className="flex items-center space-x-3">
        <Icon className={`w-6 h-6 text-${color}-600`} />
        <h2 className={`text-lg font-semibold text-${color}-800`}>{title}</h2>
      </div>
      {isExpanded ? 
        <ChevronDown className={`w-5 h-5 text-${color}-600`} /> : 
        <ChevronRight className={`w-5 h-5 text-${color}-600`} />
      }
    </button>
  );

  const SubsectionHeader = ({ title, subsection, isExpanded }) => (
    <button
      onClick={() => toggleSubsection(subsection)}
      className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-md border border-gray-200 transition-all duration-200 mb-2"
    >
      <h3 className="text-md font-medium text-gray-700">{title}</h3>
      {isExpanded ? 
        <ChevronDown className="w-4 h-4 text-gray-600" /> : 
        <ChevronRight className="w-4 h-4 text-gray-600" />
      }
    </button>
  );

  const ContentCard = ({ children }) => (
    <div className="bg-white p-4 rounded-md border border-gray-200 mb-3 shadow-sm">
      {children}
    </div>
  );

  const StatBox = ({ label, value, sublabel }) => (
    <div className="bg-blue-50 p-3 rounded-md border border-blue-200 text-center">
      <div className="text-2xl font-bold text-blue-800">{value}</div>
      <div className="text-sm text-blue-600">{label}</div>
      {sublabel && <div className="text-xs text-blue-500 mt-1">{sublabel}</div>}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8 bg-white p-6 rounded-xl shadow-sm border">
          <h1 className="text-3xl font-bold text-gray-800 mb-3">
            Why Hierarchical Organization of Life Moments Might Matter
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Convergent evidence suggests that hierarchical organization could better align with how our brains evolved to process information and make decisions.
          </p>
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              <strong>Important:</strong> If you're already experiencing consistent well-being and deep satisfaction, additional organization is likely unnecessary. This approach seems most relevant during transitions, complex decisions, or persistent dissatisfaction.
            </p>
          </div>
        </div>

        {/* Data Visualization and Cognitive Bias Reduction */}
        <SectionHeader
          icon={BarChart3}
          title="Data Visualization & Bias Reduction"
          section="visualization"
          isExpanded={expandedSections.visualization}
          color="green"
        />
        
        {expandedSections.visualization && (
          <div className="mb-6 space-y-4">
            <SubsectionHeader
              title="The Problem of Self-Perception Biases"
              subsection="cognitive-biases"
              isExpanded={expandedSubsections['cognitive-biases']}
            />
            
            {expandedSubsections['cognitive-biases'] && (
              <ContentCard>
                <p className="text-gray-700 mb-3">
                  <strong>Availability Bias (Tversky & Kahneman, 1973):</strong> We're likely to disproportionately remember recent or emotional events, which could distort our perception of life patterns.
                </p>
                <div className="grid grid-cols-2 gap-4 mb-3">
                  <StatBox label="Overestimation" value="300%" sublabel="of recent vs. actual patterns" />
                  <StatBox label="Decision Distortion" value="40-60%" sublabel="from memory-based choices" />
                </div>
                <p className="text-sm text-gray-600">
                  Hierarchical moment visualization might counteract this bias by providing a more objective representation of temporal patterns.
                </p>
              </ContentCard>
            )}

            <SubsectionHeader
              title="Advantages of Visual Proportions"
              subsection="visual-proportions"
              isExpanded={expandedSubsections['visual-proportions']}
            />
            
            {expandedSubsections['visual-proportions'] && (
              <ContentCard>
                <p className="text-gray-700 mb-3">
                  <strong>Cleveland & McGill (1984):</strong> Visual perception of proportions appears to be significantly more accurate than narrative memories for evaluating temporal distributions.
                </p>
                <div className="bg-blue-50 p-3 rounded-md mb-3">
                  <h4 className="font-medium text-blue-800 mb-2">Potential Personal Applications:</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Actual time distribution vs. perception</li>
                    <li>• Energy patterns and emotional states</li>
                    <li>• Frequency of different experience types</li>
                    <li>• Progress toward long-term objectives</li>
                  </ul>
                </div>
                <p className="text-sm text-gray-600">
                  These visualizations could reveal discrepancies between subjective perception and objective reality, facilitating more informed decisions.
                </p>
              </ContentCard>
            )}

            <SubsectionHeader
              title="Personal Statistics vs. Intuition"
              subsection="stats-vs-intuition"
              isExpanded={expandedSubsections['stats-vs-intuition']}
            />
            
            {expandedSubsections['stats-vs-intuition'] && (
              <ContentCard>
                <p className="text-gray-700 mb-3">
                  <strong>Personal Intuition Paradox:</strong> While we trust our intuition for personal decisions, we're likely to apply rigorous statistical analysis for professional or financial decisions.
                </p>
                <div className="grid grid-cols-3 gap-3 mb-3">
                  <StatBox label="Financial Decisions" value="85%" sublabel="use objective data" />
                  <StatBox label="Personal Decisions" value="15%" sublabel="use objective data" />
                  <StatBox label="Potential Improvement" value="40%" sublabel="with systematic organization" />
                </div>
                <p className="text-sm text-gray-600">
                  Hierarchical organization might allow applying analytical rigor to life decisions while maintaining emotional and intuitive flexibility.
                </p>
              </ContentCard>
            )}
          </div>
        )}

        {/* Experimental Evidence */}
        <SectionHeader
          icon={Target}
          title="Experimental Evidence"
          section="experimental" 
          isExpanded={expandedSections.experimental}
          color="blue"
        />
        
        {expandedSections.experimental && (
          <div className="mb-6 space-y-4">
            <SubsectionHeader
              title="Memory Hierarchy Experiments"
              subsection="memory-hierarchy"
              isExpanded={expandedSubsections['memory-hierarchy']}
            />
            
            {expandedSubsections['memory-hierarchy'] && (
              <ContentCard>
                <p className="text-gray-700 mb-3">
                  <strong>Bower et al. (1969):</strong> Hierarchical organization might improve recall by approximately 200% compared to random presentation.
                </p>
                <div className="grid grid-cols-2 gap-4 mb-3">
                  <StatBox label="Random Presentation" value="Baseline" sublabel="recall capacity" />
                  <StatBox label="Hierarchical Organization" value="+200%" sublabel="recall improvement" />
                </div>
                <p className="text-sm text-gray-600">
                  <strong>Likely implication:</strong> Your brain probably processes information hierarchically by nature. Fighting this structure possibly wastes cognitive resources.
                </p>
              </ContentCard>
            )}

            <SubsectionHeader
              title="Decision Fatigue Studies"
              subsection="decision-fatigue"
              isExpanded={expandedSubsections['decision-fatigue']}
            />
            
            {expandedSubsections['decision-fatigue'] && (
              <ContentCard>
                <p className="text-gray-700 mb-3">
                  <strong>Baumeister et al. (1998):</strong> After repeated unstructured decisions, decision quality likely declines significantly.
                </p>
                <div className="bg-red-50 p-3 rounded-md mb-3 border border-red-200">
                  <h4 className="font-medium text-red-800 mb-2">Evolutionary Perspective:</h4>
                  <p className="text-sm text-red-700">
                    Our ancestors probably faced limited daily decisions in structured social hierarchies. Modern chaos of unorganized choices might exceed our cognitive capacity.
                  </p>
                </div>
                <p className="text-sm text-gray-600">
                  Pre-organized hierarchical frameworks could maintain decision quality even under cognitive load.
                </p>
              </ContentCard>
            )}

            <SubsectionHeader
              title="Miller's Magical Number"
              subsection="miller-number"
              isExpanded={expandedSubsections['miller-number']}
            />
            
            {expandedSubsections['miller-number'] && (
              <ContentCard>
                <p className="text-gray-700 mb-3">
                  <strong>Miller (1956):</strong> Humans can possibly hold 7±2 unrelated items in working memory, but likely can process 7±2 categories, each containing 7±2 subcategories.
                </p>
                <div className="grid grid-cols-2 gap-4 mb-3">
                  <StatBox label="Individual Items" value="7±2" sublabel="working memory limit" />
                  <StatBox label="Hierarchical Capacity" value="49-81" sublabel="organized elements" />
                </div>
                <p className="text-sm text-gray-600">
                  This could create exponential processing capacity through hierarchy, freeing mental resources for pattern recognition and future planning.
                </p>
              </ContentCard>
            )}
          </div>
        )}

        {/* Evolutionary Psychology */}
        <SectionHeader
          icon={Brain}
          title="Evolutionary Psychology Perspective"
          section="evolutionary"
          isExpanded={expandedSections.evolutionary}
          color="purple"
        />
        
        {expandedSections.evolutionary && (
          <div className="mb-6 space-y-4">
            <SubsectionHeader
              title="Ancestral Environment Mismatch"
              subsection="ancestral-mismatch"
              isExpanded={expandedSubsections['ancestral-mismatch']}
            />
            
            {expandedSubsections['ancestral-mismatch'] && (
              <ContentCard>
                <p className="text-gray-700 mb-3">
                  Modern humans possibly face approximately 35,000 daily decisions, while our ancestors probably encountered 70-100 structured decisions in predictable social hierarchies.
                </p>
                <div className="grid grid-cols-2 gap-4 mb-3">
                  <StatBox label="Ancestral Decisions" value="~100" sublabel="structured/day" />
                  <StatBox label="Modern Decisions" value="35,000" sublabel="unstructured/day" />
                </div>
                <div className="bg-purple-50 p-3 rounded-md border border-purple-200">
                  <p className="text-sm text-purple-700">
                    <strong>Schwartz (2004):</strong> More than 8-10 unstructured options might decrease satisfaction by 25% and decision quality by 15%.
                  </p>
                </div>
              </ContentCard>
            )}

            <SubsectionHeader
              title="Social Hierarchy Hypothesis"
              subsection="social-hierarchy"
              isExpanded={expandedSubsections['social-hierarchy']}
            />
            
            {expandedSubsections['social-hierarchy'] && (
              <ContentCard>
                <p className="text-gray-700 mb-3">
                  <strong>Sapolsky (2017):</strong> Humans and other primates likely show lowest stress hormones when their social position is clearly defined, predictable, and internally controlled.
                </p>
                <div className="bg-purple-50 p-3 rounded-md mb-3 border border-purple-200">
                  <h4 className="font-medium text-purple-800 mb-2">Key Elements for Stress Reduction:</h4>
                  <ul className="text-sm text-purple-700 space-y-1">
                    <li>• <strong>Hierarchical clarity:</strong> Defined position</li>
                    <li>• <strong>Predictability:</strong> Consistent rules</li>
                    <li>• <strong>Internal control:</strong> Agency within structure</li>
                  </ul>
                </div>
                <p className="text-sm text-gray-600">
                  Hierarchical life organization might mimic successful ancestral social structures, probably reducing stress and improving decision-making.
                </p>
              </ContentCard>
            )}

            <SubsectionHeader
              title="Foraging Efficiency Model"
              subsection="foraging-efficiency"
              isExpanded={expandedSubsections['foraging-efficiency']}
            />
            
            {expandedSubsections['foraging-efficiency'] && (
              <ContentCard>
                <p className="text-gray-700 mb-3">
                  <strong>Stephens & Krebs (1986):</strong> Animals that organized foraging behavior hierarchically (territory → patches → specific resources) likely showed 40-60% better energy efficiency.
                </p>
                <div className="grid grid-cols-2 gap-4 mb-3">
                  <StatBox label="Energy Efficiency" value="+40-60%" sublabel="hierarchical organization" />
                  <StatBox label="Goal Achievement" value="+35%" sublabel="structured frameworks" />
                </div>
                <p className="text-sm text-gray-600">
                  <strong>Gigerenzer (2007):</strong> People using hierarchical decision frameworks possibly achieve goals 35% faster with 50% less effort.
                </p>
              </ContentCard>
            )}
          </div>
        )}

        {/* Information Theory */}
        <SectionHeader
          icon={Zap}
          title="Information Theory Perspective"
          section="information"
          isExpanded={expandedSections.information}
          color="orange"
        />
        
        {expandedSections.information && (
          <div className="mb-6 space-y-4">
            <SubsectionHeader
              title="The Compression Advantage"
              subsection="compression-advantage"
              isExpanded={expandedSubsections['compression-advantage']}
            />
            
            {expandedSubsections['compression-advantage'] && (
              <ContentCard>
                <p className="text-gray-700 mb-3">
                  <strong>Shannon (1948):</strong> Hierarchical organization probably achieves optimal data compression. Applied to life experiences, this could allow processing exponentially more information.
                </p>
                <div className="bg-orange-50 p-3 rounded-md mb-3 border border-orange-200">
                  <h4 className="font-medium text-orange-800 mb-2">Application to Moments:</h4>
                  <p className="text-sm text-orange-700">
                    Instead of remembering hundreds of disconnected experiences, hierarchical organization possibly allows compressing similar moments into categories, freeing mental resources for pattern recognition and future planning.
                  </p>
                </div>
              </ContentCard>
            )}

            <SubsectionHeader
              title="The Brain as Prediction Machine"
              subsection="prediction-machine"
              isExpanded={expandedSubsections['prediction-machine']}
            />
            
            {expandedSubsections['prediction-machine'] && (
              <ContentCard>
                <p className="text-gray-700 mb-3">
                  <strong>Clark (2013):</strong> The brain possibly operates as a "prediction machine," constantly generating models of future experiences based on past patterns.
                </p>
                <div className="grid grid-cols-2 gap-4 mb-3">
                  <StatBox label="Neural Reduction" value="30-40%" sublabel="predictable experiences" />
                  <StatBox label="Unpredicted Activity" value="3x more" sublabel="neural activity" />
                </div>
                <p className="text-sm text-gray-600">
                  Organized moment tracking probably creates better predictive models, reducing cognitive load and improving future decision-making accuracy.
                </p>
              </ContentCard>
            )}

            <SubsectionHeader
              title="Entropy Reduction Principle"
              subsection="entropy-reduction"
              isExpanded={expandedSubsections['entropy-reduction']}
            />
            
            {expandedSubsections['entropy-reduction'] && (
              <ContentCard>
                <p className="text-gray-700 mb-3">
                  <strong>Bialek et al. (2001):</strong> Neural networks using hierarchical processing possibly achieve superior efficiency in information transmission compared to flat structures.
                </p>
                <div className="bg-orange-50 p-3 rounded-md border border-orange-200">
                  <p className="text-sm text-orange-700">
                    <strong>Life application:</strong> Hierarchical moment organization probably allows extracting maximum insight from experiences while minimizing cognitive noise.
                  </p>
                </div>
              </ContentCard>
            )}
          </div>
        )}

        {/* Debunking Common Myths */}
        <SectionHeader
          icon={AlertTriangle}
          title="Debunking Common Myths"
          section="myths"
          isExpanded={expandedSections.myths}
          color="red"
        />
        
        {expandedSections.myths && (
          <div className="mb-6 space-y-4">
            <SubsectionHeader
              title="Myth: 'Organization kills creativity and spontaneity'"
              subsection="creativity-myth"
              isExpanded={expandedSubsections['creativity-myth']}
            />
            
            {expandedSubsections['creativity-myth'] && (
              <ContentCard>
                <div className="bg-red-50 p-3 rounded-md mb-3 border border-red-200">
                  <p className="text-sm text-red-700 font-medium mb-2">Counter-evidence:</p>
                  <ul className="text-sm text-red-600 space-y-1">
                    <li>• <strong>Stokes (2005):</strong> Creative professionals with organizational frameworks possibly produce more innovative work</li>
                    <li>• <strong>Schwartz (2004):</strong> Too many unstructured options likely decrease creative output</li>
                    <li>• Hierarchical organization probably reduces cognitive noise, freeing mental resources for creative thinking</li>
                  </ul>
                </div>
              </ContentCard>
            )}

            <SubsectionHeader
              title="Myth: 'Successful people don't need systems—they just wing it'"
              subsection="success-myth"
              isExpanded={expandedSubsections['success-myth']}
            />
            
            {expandedSubsections['success-myth'] && (
              <ContentCard>
                <div className="bg-red-50 p-3 rounded-md mb-3 border border-red-200">
                  <p className="text-sm text-red-700 font-medium mb-2">Counter-evidence:</p>
                  <ul className="text-sm text-red-600 space-y-1">
                    <li>• <strong>Ericsson (2016):</strong> Elite performers across all domains likely use highly structured practice and reflection systems</li>
                    <li>• High-performing individuals probably show superior organizational skills, not less structure</li>
                    <li>• Successful hunter-gatherer societies likely had complex hierarchical organization systems</li>
                  </ul>
                </div>
              </ContentCard>
            )}

            <SubsectionHeader
              title="Myth: 'Hierarchy is unnatural and oppressive'"
              subsection="hierarchy-myth"
              isExpanded={expandedSubsections['hierarchy-myth']}
            />
            
            {expandedSubsections['hierarchy-myth'] && (
              <ContentCard>
                <div className="bg-red-50 p-3 rounded-md mb-3 border border-red-200">
                  <p className="text-sm text-red-700 font-medium mb-2">Counter-evidence:</p>
                  <ul className="text-sm text-red-600 space-y-1">
                    <li>• All successful primate societies probably exhibit hierarchical organization with clear roles</li>
                    <li>• The human brain likely evolved hierarchical processing as its fundamental architecture</li>
                    <li>• Even egalitarian societies possibly maintain hierarchical organization for different domains</li>
                  </ul>
                </div>
              </ContentCard>
            )}

            <SubsectionHeader
              title="Myth: 'Simple is always better than complex'"
              subsection="simplicity-myth"
              isExpanded={expandedSubsections['simplicity-myth']}
            />
            
            {expandedSubsections['simplicity-myth'] && (
              <ContentCard>
                <div className="bg-red-50 p-3 rounded-md mb-3 border border-red-200">
                  <p className="text-sm text-red-700 font-medium mb-2">Counter-evidence:</p>
                  <ul className="text-sm text-red-600 space-y-1">
                    <li>• Appropriate complexity probably matches environmental demands</li>
                    <li>• Oversimplification possibly leads to system failure</li>
                    <li>• Well-structured complexity likely reduces cognitive load</li>
                    <li>• Hierarchical organization probably achieves optimal balance between simplicity and information richness</li>
                  </ul>
                </div>
              </ContentCard>
            )}

            <SubsectionHeader
              title="Myth: 'Organization is just for anxious or controlling people'"
              subsection="anxiety-myth"
              isExpanded={expandedSubsections['anxiety-myth']}
            />
            
            {expandedSubsections['anxiety-myth'] && (
              <ContentCard>
                <div className="bg-red-50 p-3 rounded-md border border-red-200">
                  <p className="text-sm text-red-700 font-medium mb-2">Counter-evidence:</p>
                  <ul className="text-sm text-red-600 space-y-1">
                    <li>• Hierarchical organization probably reduces anxiety rather than increasing it</li>
                    <li>• Successful organization systems likely benefit all personality types</li>
                    <li>• Proper structure probably enables spontaneous flow states rather than preventing them</li>
                  </ul>
                </div>
              </ContentCard>
            )}
          </div>
        )}

        {/* Conclusion */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-xl border border-blue-200 shadow-sm">
          <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
            <Eye className="w-6 h-6 text-blue-600 mr-3" />
            Scientific Convergence
          </h2>
          <p className="text-gray-700 mb-4">
            The convergent evidence from information theory, evolutionary psychology, and cognitive science might point to a clear conclusion: hierarchical organization of life moments probably isn't just helpful—it's possibly how our brains are designed to function optimally.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-800">Likely Advantages:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Enhanced cognitive efficiency</li>
                <li>• Improved predictive accuracy</li>
                <li>• Systemic stress reduction</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-800">Additional Benefits:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Optimal information compression</li>
                <li>• Alignment with ancestral patterns</li>
                <li>• Cognitive bias reduction</li>
              </ul>
            </div>
          </div>
          
          <div className="bg-white p-4 rounded-lg border border-blue-200">
            <p className="text-sm text-gray-700">
              <strong>The key question probably isn't</strong> whether to organize moments hierarchically—it's whether you'll do it consciously and systematically, or let your brain do it unconsciously and inefficiently.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HierarchicalOrganizationApp;