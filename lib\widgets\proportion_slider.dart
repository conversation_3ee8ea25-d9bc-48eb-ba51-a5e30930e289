import 'package:flutter/material.dart';

class ProportionSlider extends StatelessWidget {
  final String leftLabel;
  final String rightLabel;
  final double value;
  final ValueChanged<double> onChanged;
  final Color leftColor;
  final Color rightColor;

  const ProportionSlider({
    super.key,
    required this.leftLabel,
    required this.rightLabel,
    required this.value,
    required this.onChanged,
    this.leftColor = Colors.blue,
    this.rightColor = Colors.green,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Label boxes removed - now using clickable icons beside the chart
        Row(
          children: [
            Expanded(
              flex: (value * 100).round(),
              child: Container(
                height: 24,
                decoration: BoxDecoration(
                  color: leftColor,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    bottomLeft: Radius.circular(12),
                  ),
                ),
                child: Center(
                  child: Text(
                    '${(value * 100).round()}%',
                    style: TextStyle(
                      color: leftColor.computeLuminance() > 0.5 ? Colors.black : Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              flex: ((1 - value) * 100).round(),
              child: Container(
                height: 24,
                decoration: BoxDecoration(
                  color: rightColor,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Center(
                  child: Text(
                    '${((1 - value) * 100).round()}%',
                    style: TextStyle(
                      color: rightColor.computeLuminance() > 0.5 ? Colors.black : Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SliderTheme(
          data: SliderThemeData(
            activeTrackColor: leftColor,
            inactiveTrackColor: rightColor.withAlpha(150),
            thumbColor: Colors.white,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
            overlayColor: leftColor.withAlpha(50),
            trackHeight: 8,
          ),
          child: Slider(
            value: value,
            onChanged: onChanged,
            min: 0.0,
            max: 1.0,
            divisions: 100,
          ),
        ),
      ],
    );
  }
}
