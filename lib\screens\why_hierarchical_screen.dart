import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';
import '../widgets/app_menu.dart';

class WhyHierarchicalScreen extends StatefulWidget {
  const WhyHierarchicalScreen({super.key});

  @override
  State<WhyHierarchicalScreen> createState() => _WhyHierarchicalScreenState();
}

class _WhyHierarchicalScreenState extends State<WhyHierarchicalScreen> {
  Map<String, bool> expandedSections = {};
  Map<String, bool> expandedSubsections = {}; // Added for subsections

  void toggleSection(String sectionId) {
    setState(() {
      expandedSections[sectionId] = !(expandedSections[sectionId] ?? false);
      // Collapse all subsections when a main section is collapsed
      if (!(expandedSections[sectionId] ?? false)) {
        for (var key in expandedSubsections.keys) {
          if (key.startsWith(sectionId)) { // Assuming subsection IDs are prefixed by section ID
             expandedSubsections[key] = false;
          }
        }
      }
    });
  }

  void toggleSubsection(String subsectionId) {
    setState(() {
      expandedSubsections[subsectionId] = !(expandedSubsections[subsectionId] ?? false);
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.whyHierarchicalScreenTitle),
        backgroundColor: const Color(0xFF2A6BAA),
        foregroundColor: Colors.white,
        actions: const [
          AppMenu(),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF8FAFC),
              Color(0xFFF1F5F9),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start, // Align content to start
              children: [
                // Header
                _buildHeader(localizations),
                SizedBox(height: 32),

                // Sections
                ..._buildSections(localizations),

                SizedBox(height: 48),

                // Footer (Optional, can be removed if not needed)
                // _buildFooter(),
              ],
            ),
          ),
        )
      ),
    );
  }

  Widget _buildHeader(AppLocalizations localizations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center, // Center header content
      children: [
        ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: [Color(0xFF4F46E5), Color(0xFF7C3AED)],
          ).createShader(bounds),
          child: Text(
            'ESTIMAT',
            style: TextStyle(
              fontSize: 36,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        SizedBox(height: 8),
        Text(
          localizations.whyHierarchicalHeaderSubtitle,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 18,
            color: Colors.grey[600],
          ),
        ),
        SizedBox(height: 16),
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.yellow[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.yellow[200]!),
          ),
          child: Text(
            localizations.whyHierarchicalImportantNote,
            style: TextStyle(
              fontSize: 14,
              color: Colors.yellow[800],
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildSections(AppLocalizations localizations) {
    // This will be populated with sections based on whyhierarchical.md
    // Each section will contain subsections
    return [
      _buildSection(
        id: 'visualization',
        title: localizations.dataVisualizationBiasReductionTitle,
        icon: Icons.bar_chart, // Using a similar icon
        gradient: [Color(0xFF10B981), Color(0xFF059669)], // Green gradient
        subsections: [
          _buildSubsection(
            sectionId: 'visualization',
            subsectionId: 'cognitive-biases',
            title: localizations.selfPerceptionBiasesTitle,
            content: _buildCognitiveBiasesContent(localizations),
          ),
          _buildSubsection(
            sectionId: 'visualization',
            subsectionId: 'visual-proportions',
            title: localizations.visualProportionsTitle,
            content: _buildVisualProportionsContent(localizations),
          ),
          _buildSubsection(
            sectionId: 'visualization',
            subsectionId: 'stats-vs-intuition',
            title: localizations.statsVsIntuitionTitle,
            content: _buildStatsVsIntuitionContent(localizations),
          ),
        ],
      ),
      _buildSection(
        id: 'experimental',
        title: localizations.experimentalEvidenceTitle,
        icon: Icons.emoji_events, // Using a similar icon
        gradient: [Color(0xFF3B82F6), Color(0xFF6366F1)], // Blue gradient
        subsections: [
           _buildSubsection(
            sectionId: 'experimental',
            subsectionId: 'memory-hierarchy',
            title: localizations.memoryHierarchyTitle,
            content: _buildMemoryHierarchyContent(localizations),
          ),
           _buildSubsection(
            sectionId: 'experimental',
            subsectionId: 'decision-fatigue',
            title: localizations.decisionFatigueTitle,
            content: _buildDecisionFatigueContent(localizations),
          ),
           _buildSubsection(
            sectionId: 'experimental',
            subsectionId: 'miller-number',
            title: localizations.millerNumberTitle,
            content: _buildMillerNumberContent(localizations),
          ),
        ],
      ),
       _buildSection(
        id: 'evolutionary',
        title: localizations.evolutionaryPsychologyPerspectiveTitle,
        icon: Icons.psychology, // Using a similar icon
        gradient: [Color(0xFF8B5CF6), Color(0xFF6366F1)], // Purple gradient
        subsections: [
           _buildSubsection(
            sectionId: 'evolutionary',
            subsectionId: 'ancestral-mismatch',
            title: localizations.ancestralMismatchTitle,
            content: _buildAncestralMismatchContent(localizations),
          ),
           _buildSubsection(
            sectionId: 'evolutionary',
            subsectionId: 'social-hierarchy',
            title: localizations.socialHierarchyTitle,
            content: _buildSocialHierarchyContent(localizations),
          ),
           _buildSubsection(
            sectionId: 'evolutionary',
            subsectionId: 'foraging-efficiency',
            title: localizations.foragingEfficiencyTitle,
            content: _buildForagingEfficiencyContent(localizations),
          ),
        ],
      ),
       _buildSection(
        id: 'information',
        title: localizations.informationTheoryPerspectiveTitle,
        icon: Icons.flash_on, // Using a similar icon
        gradient: [Color(0xFFF97316), Color(0xFFEF4444)], // Orange gradient
        subsections: [
           _buildSubsection(
            sectionId: 'information',
            subsectionId: 'compression-advantage',
            title: localizations.compressionAdvantageTitle,
            content: _buildCompressionAdvantageContent(localizations),
          ),
           _buildSubsection(
            sectionId: 'information',
            subsectionId: 'prediction-machine',
            title: localizations.predictionMachineTitle,
            content: _buildPredictionMachineContent(localizations),
          ),
           _buildSubsection(
            sectionId: 'information',
            subsectionId: 'entropy-reduction',
            title: localizations.entropyReductionTitle,
            content: _buildEntropyReductionContent(localizations),
          ),
        ],
      ),
       _buildSection(
        id: 'myths',
        title: localizations.debunkingCommonMythsTitle,
        icon: Icons.warning, // Using a similar icon
        gradient: [Color(0xFFEF4444), Color(0xFFB91C1C)], // Red gradient
        subsections: [
           _buildSubsection(
            sectionId: 'myths',
            subsectionId: 'creativity-myth',
            title: localizations.creativityMythTitle,
            content: _buildCreativityMythContent(localizations),
          ),
           _buildSubsection(
            sectionId: 'myths',
            subsectionId: 'success-myth',
            title: localizations.successMythTitle,
            content: _buildSuccessMythContent(localizations),
          ),
           _buildSubsection(
            sectionId: 'myths',
            subsectionId: 'hierarchy-myth',
            title: localizations.hierarchyMythTitle,
            content: _buildHierarchyMythContent(localizations),
          ),
           _buildSubsection(
            sectionId: 'myths',
            subsectionId: 'simplicity-myth',
            title: localizations.simplicityMythTitle,
            content: _buildSimplicityMythContent(localizations),
          ),
           _buildSubsection(
            sectionId: 'myths',
            subsectionId: 'anxiety-myth',
            title: localizations.anxietyMythTitle,
            content: _buildAnxietyMythContent(localizations),
          ),
        ],
      ),
    ];
  }

  Widget _buildSection({
    required String id,
    required String title,
    required IconData icon,
    required List<Color> gradient,
    required List<Widget> subsections,
  }) {
    bool isExpanded = expandedSections[id] ?? false;

    return Padding(
      padding: EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () => toggleSection(id),
            child: Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: gradient),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: gradient[0].withOpacity(0.3),
                    blurRadius: 8,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(icon, color: Colors.white, size: 24),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.keyboard_arrow_down : Icons.keyboard_arrow_right,
                    color: Colors.white,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
          AnimatedCrossFade(
            firstChild: Container(),
            secondChild: Container(
              margin: EdgeInsets.only(top: 8),
              child: Column(
                children: subsections,
              ),
            ),
            crossFadeState: isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
            duration: Duration(milliseconds: 300),
          ),
        ],
      ),
    );
  }

  Widget _buildSubsection({
    required String sectionId,
    required String subsectionId,
    required String title,
    required Widget content,
  }) {
    String fullId = '${sectionId}_$subsectionId'; // Create a unique ID
    bool isExpanded = expandedSubsections[fullId] ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () => toggleSubsection(fullId),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[800],
                    ),
                  ),
                ),
                Icon(
                  isExpanded ? Icons.keyboard_arrow_down : Icons.keyboard_arrow_right,
                  color: Colors.grey[600],
                  size: 18,
                ),
              ],
            ),
          ),
        ),
        AnimatedCrossFade(
          firstChild: Container(),
          secondChild: Container(
            margin: EdgeInsets.only(top: 8),
            child: _buildContentCard(content), // Wrap subsection content in a card
          ),
          crossFadeState: isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
          duration: Duration(milliseconds: 300),
        ),
      ],
    );
  }


  Widget _buildContentCard(Widget child) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 6,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }

  Widget _buildStatBox(String value, String label, {String? sublabel, MaterialColor color = Colors.blue}) {
    return Expanded( // Use Expanded to make it flexible in a Row
      child: Container(
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color[200]!),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min, // Use min to wrap content
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color[800],
              ),
            ),
            Text(
              label,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12,
                color: color[600],
              ),
            ),
            if (sublabel != null)
              Text(
                sublabel,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 10,
                  color: color[500],
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Content building methods for each subsection
  Widget _buildCognitiveBiasesContent(AppLocalizations localizations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.availabilityBiasContent,
          style: TextStyle(color: Colors.grey[700], fontSize: 14, height: 1.5),
        ),
        SizedBox(height: 12),
        Row( // Use Row for StatBoxes
          children: [
            _buildStatBox('300%', localizations.overestimationLabel, sublabel: localizations.overestimationSublabel, color: Colors.green),
            SizedBox(width: 8),
            _buildStatBox('40-60%', localizations.decisionDistortionLabel, sublabel: localizations.decisionDistortionSublabel, color: Colors.green),
          ],
        ),
        SizedBox(height: 12),
        Text(
          localizations.hierarchicalVisualizationNote,
          style: TextStyle(color: Colors.grey[600], fontSize: 13),
        ),
      ],
    );
  }

  Widget _buildVisualProportionsContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.clevelandMcGillContent,
          style: TextStyle(color: Colors.grey[700], fontSize: 14, height: 1.5),
        ),
        SizedBox(height: 12),
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                localizations.potentialPersonalApplicationsTitle,
                style: TextStyle(fontWeight: FontWeight.w500, color: Colors.blue[800], fontSize: 14),
              ),
              SizedBox(height: 4),
              Text(
                localizations.personalApplicationsList,
                style: TextStyle(color: Colors.blue[700], fontSize: 13, height: 1.5),
              ),
            ],
          ),
        ),
        SizedBox(height: 12),
        Text(
          localizations.visualizationDiscrepanciesNote,
          style: TextStyle(color: Colors.grey[600], fontSize: 13),
        ),
      ],
    );
  }

  Widget _buildStatsVsIntuitionContent(AppLocalizations localizations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.personalIntuitionParadoxContent,
          style: TextStyle(color: Colors.grey[700], fontSize: 14, height: 1.5),
        ),
        SizedBox(height: 12),
        Row( // Use Row for StatBoxes
          children: [
            _buildStatBox('85%', localizations.financialDecisionsLabel, sublabel: localizations.financialDecisionsSublabel, color: Colors.green),
            SizedBox(width: 8),
            _buildStatBox('15%', localizations.personalDecisionsLabel, sublabel: localizations.personalDecisionsSublabel, color: Colors.green),
            SizedBox(width: 8),
            _buildStatBox('40%', localizations.potentialImprovementLabel, sublabel: localizations.potentialImprovementSublabel, color: Colors.green),
          ],
        ),
        SizedBox(height: 12),
        Text(
          localizations.hierarchicalAnalyticalNote,
          style: TextStyle(color: Colors.grey[600], fontSize: 13),
        ),
      ],
    );
  }

  Widget _buildMemoryHierarchyContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.memoryHierarchyContent,
          style: TextStyle(color: Colors.grey[700], fontSize: 14, height: 1.5),
        ),
        SizedBox(height: 12),
        Row( // Use Row for StatBoxes
          children: [
            _buildStatBox(localizations.baselineLabel, localizations.randomPresentationLabel, sublabel: localizations.randomPresentationSublabel, color: Colors.blue),
            SizedBox(width: 8),
            _buildStatBox('+200%', localizations.hierarchicalOrganizationLabel, sublabel: localizations.hierarchicalImprovementSublabel, color: Colors.blue),
          ],
        ),
        SizedBox(height: 12),
        Text(
          localizations.brainProcessesHierarchically,
          style: TextStyle(color: Colors.grey[600], fontSize: 13),
        ),
      ],
    );
  }

  Widget _buildDecisionFatigueContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.decisionFatigueContent,
          style: TextStyle(color: Colors.grey[700], fontSize: 14, height: 1.5),
        ),
        SizedBox(height: 12),
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                localizations.evolutionaryPerspectiveTitle,
                style: TextStyle(fontWeight: FontWeight.w500, color: Colors.red[800], fontSize: 14),
              ),
              SizedBox(height: 4),
              Text(
                localizations.ancestorsDecisionsContent,
                style: TextStyle(color: Colors.red[700], fontSize: 13, height: 1.5),
              ),
            ],
          ),
        ),
        SizedBox(height: 12),
        Text(
          localizations.preOrganizedStructures,
          style: TextStyle(color: Colors.grey[600], fontSize: 13),
        ),
      ],
    );
  }

  Widget _buildMillerNumberContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.millerNumberContent,
          style: TextStyle(color: Colors.grey[700], fontSize: 14, height: 1.5),
        ),
        SizedBox(height: 12),
        Row( // Use Row for StatBoxes
          children: [
            _buildStatBox('7±2', localizations.individualItemsLabel, sublabel: localizations.workingMemoryLimitSublabel, color: Colors.blue),
            SizedBox(width: 8),
            _buildStatBox('49-81', localizations.hierarchicalCapacityLabel, sublabel: localizations.organizedElementsSublabel, color: Colors.blue),
          ],
        ),
        SizedBox(height: 12),
        Text(
          localizations.exponentialProcessingCapacity,
          style: TextStyle(color: Colors.grey[600], fontSize: 13),
        ),
      ],
    );
  }

  Widget _buildAncestralMismatchContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.ancestralMismatchContent,
          style: TextStyle(color: Colors.grey[700], fontSize: 14, height: 1.5),
        ),
        SizedBox(height: 12),
        Row( // Use Row for StatBoxes
          children: [
            _buildStatBox('~100', localizations.ancestralDecisionsLabel, sublabel: localizations.structuredPerDaySublabel, color: Colors.purple),
            SizedBox(width: 8),
            _buildStatBox('35.000', localizations.modernDecisionsLabel, sublabel: localizations.unstructuredPerDaySublabel, color: Colors.purple),
          ],
        ),
        SizedBox(height: 12),
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.purple[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.purple[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                localizations.schwartzOptionsContent,
                style: TextStyle(color: Colors.purple[700], fontSize: 13, height: 1.5),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSocialHierarchyContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.socialHierarchyContent,
          style: TextStyle(color: Colors.grey[700], fontSize: 14, height: 1.5),
        ),
        SizedBox(height: 12),
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.purple[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.purple[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                localizations.stressReductionElementsTitle,
                style: TextStyle(fontWeight: FontWeight.w500, color: Colors.purple[800], fontSize: 14),
              ),
              SizedBox(height: 4),
              Text(
                localizations.stressReductionElementsList,
                style: TextStyle(color: Colors.purple[700], fontSize: 13, height: 1.5),
              ),
            ],
          ),
        ),
        SizedBox(height: 12),
        Text(
          localizations.hierarchicalOrganizationBenefits,
          style: TextStyle(color: Colors.grey[600], fontSize: 13),
        ),
      ],
    );
  }

  Widget _buildForagingEfficiencyContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.foragingEfficiencyContent,
          style: TextStyle(color: Colors.grey[700], fontSize: 14, height: 1.5),
        ),
        SizedBox(height: 12),
        Row( // Use Row for StatBoxes
          children: [
            _buildStatBox('+40-60%', localizations.energyEfficiencyLabel, sublabel: localizations.hierarchicalOrganizationSublabel, color: Colors.purple),
            SizedBox(width: 8),
            _buildStatBox('+35%', localizations.goalAchievementLabel, sublabel: localizations.structuredFrameworksSublabel, color: Colors.purple),
          ],
        ),
        SizedBox(height: 12),
        Text(
          localizations.gigerenzerFrameworksContent,
          style: TextStyle(color: Colors.grey[600], fontSize: 13),
        ),
      ],
    );
  }

  Widget _buildCompressionAdvantageContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.compressionAdvantageContent,
          style: TextStyle(color: Colors.grey[700], fontSize: 14, height: 1.5),
        ),
        SizedBox(height: 12),
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                localizations.applicationToMomentsTitle,
                style: TextStyle(fontWeight: FontWeight.w500, color: Colors.orange[800], fontSize: 14),
              ),
              SizedBox(height: 4),
              Text(
                localizations.compressionMomentsContent,
                style: TextStyle(color: Colors.orange[700], fontSize: 13, height: 1.5),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPredictionMachineContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.predictionMachineContent,
          style: TextStyle(color: Colors.grey[700], fontSize: 14, height: 1.5),
        ),
        SizedBox(height: 12),
        Row( // Use Row for StatBoxes
          children: [
            _buildStatBox('30-40%', localizations.neuralReductionLabel, sublabel: localizations.predictableExperiencesSublabel, color: Colors.orange),
            SizedBox(width: 8),
            _buildStatBox('3x more', localizations.unpredictedActivityLabel, sublabel: localizations.neuralActivitySublabel, color: Colors.orange),
          ],
        ),
        SizedBox(height: 12),
        Text(
          localizations.organizedMomentTracking,
          style: TextStyle(color: Colors.grey[600], fontSize: 13),
        ),
      ],
    );
  }

  Widget _buildEntropyReductionContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.entropyReductionContent,
          style: TextStyle(color: Colors.grey[700], fontSize: 14, height: 1.5),
        ),
        SizedBox(height: 12),
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                localizations.lifeApplicationEntropy,
                style: TextStyle(color: Colors.orange[700], fontSize: 13, height: 1.5),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCreativityMythContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                localizations.creativityMythCounterTitle,
                style: TextStyle(fontWeight: FontWeight.w500, color: Colors.red[700], fontSize: 14),
              ),
              SizedBox(height: 4),
              Text(
                localizations.creativityMythCounterContent,
                style: TextStyle(color: Colors.red[600], fontSize: 13, height: 1.5),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSuccessMythContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                localizations.creativityMythCounterTitle,
                style: TextStyle(fontWeight: FontWeight.w500, color: Colors.red[700], fontSize: 14),
              ),
              SizedBox(height: 4),
              Text(
                localizations.successMythCounterContent,
                style: TextStyle(color: Colors.red[600], fontSize: 13, height: 1.5),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHierarchyMythContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                localizations.creativityMythCounterTitle,
                style: TextStyle(fontWeight: FontWeight.w500, color: Colors.red[700], fontSize: 14),
              ),
              SizedBox(height: 4),
              Text(
                localizations.hierarchyMythCounterContent,
                style: TextStyle(color: Colors.red[600], fontSize: 13, height: 1.5),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSimplicityMythContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                localizations.creativityMythCounterTitle,
                style: TextStyle(fontWeight: FontWeight.w500, color: Colors.red[700], fontSize: 14),
              ),
              SizedBox(height: 4),
              Text(
                localizations.simplicityMythCounterContent,
                style: TextStyle(color: Colors.red[600], fontSize: 13, height: 1.5),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAnxietyMythContent(AppLocalizations localizations) {
     return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                localizations.creativityMythCounterTitle,
                style: TextStyle(fontWeight: FontWeight.w500, color: Colors.red[700], fontSize: 14),
              ),
              SizedBox(height: 4),
              Text(
                localizations.anxietyMythCounterContent,
                style: TextStyle(color: Colors.red[600], fontSize: 13, height: 1.5),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Footer is not present in whyhierarchical.md, so I'll omit it for now.
  // Widget _buildFooter() {
  //   return Container();
  // }
}

// Helper class for sections, similar to the original
class EmotionSection {
  final String id;
  final String title;
  final IconData icon;
  final List<Color> gradient;
  final Widget content; // Content for the section (which will be the subsections column)

  EmotionSection({
    required this.id,
    required this.title,
    required this.icon,
    required this.gradient,
    required this.content,
  });
}