import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:estimat_keymoments/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

import 'providers/locale_provider.dart';
import 'providers/moments_provider.dart';
import 'providers/user_preferences_provider.dart';

import 'screens/language_selection_screen.dart';
import 'screens/onboarding_presentation_screen.dart';
import 'screens/full_presentation_screen.dart';
import 'screens/reference_moments_screen.dart';
import 'screens/summary_screen.dart';
import 'screens/data_export_screen.dart';
import 'screens/csv_viewer_screen.dart';
import 'screens/level_process_focus_screen.dart';
import 'screens/latent_values_screen.dart';
import 'screens/improve_worse_comparison_screen.dart';
import 'screens/why_moments_screen.dart';
import 'screens/why_hierarchical_screen.dart'; // Import the new screen
import 'screens/support_screen.dart';
import 'screens/coffee_screen.dart';
import 'screens/user_instructions_screen.dart';
import 'screens/enhanced_onboarding_screen.dart';
import 'screens/startup_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  final localeProvider = LocaleProvider();
  await localeProvider.loadSavedLocale();

  final momentsProvider = MomentsProvider();
  await momentsProvider.loadMoments();

  final userPreferencesProvider = UserPreferencesProvider();
  await userPreferencesProvider.loadPreferences();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: localeProvider),
        ChangeNotifierProvider.value(value: momentsProvider),
        ChangeNotifierProvider.value(value: userPreferencesProvider),
      ],
      child: const EstimatApp(),
    ),
  );
}

class EstimatApp extends StatelessWidget {
  const EstimatApp({super.key});

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);

    return MaterialApp(
      title: 'Estimat KeyMoments',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
        fontFamily: 'EBGaramond',
      ),
      locale: localeProvider.locale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en'), // English
        Locale('es'), // Spanish
        Locale('pt'), // Portuguese
      ],
      initialRoute: '/',
      routes: {
        '/': (context) => const StartupScreen(),
        '/main': (context) => const ReferenceMomentsScreen(),
        '/language_selection': (context) => const LanguageSelectionScreen(),
        '/onboarding_presentation': (context) => const OnboardingPresentationScreen(),
        '/presentation': (context) => const FullPresentationScreen(),
        '/summary': (context) => const SummaryScreen(),
        '/data_export': (context) => const DataExportScreen(),
        '/level_process_focus': (context) => const LevelProcessFocusScreen(),
        '/latent_values': (context) => const LatentValuesScreen(),
        '/why_moments': (context) => const WhyMomentsScreen(),
        '/why_hierarchical': (context) => const WhyHierarchicalScreen(), // Add the new route
        '/user_instructions': (context) => const UserInstructionsScreen(),
        '/support': (context) => const SupportScreen(),
        '/coffee': (context) => const CoffeeScreen(),
      },
      onGenerateRoute: (settings) {
        if (settings.name == '/csv_viewer') {
          final filePath = settings.arguments as String;
          return MaterialPageRoute(
            builder: (context) => CSVViewerScreen(filePath: filePath),
          );
        } else if (settings.name == '/improve_worse_comparison') {
          final args = settings.arguments as Map<String, dynamic>?;
          if (args != null) {
            return MaterialPageRoute(
              builder: (context) => ImproveWorseComparisonScreen(
                momentType: args['momentType'],
                currentMoment: args['currentMoment'],
              ),
            );
          }
        }
        return null;
      },
      onGenerateTitle: (context) => AppLocalizations.of(context)!.appTitle,
    );
  }
}
