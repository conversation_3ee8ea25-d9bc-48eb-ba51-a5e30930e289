import 'package:flutter/material.dart';
import 'package:estimat_keymoments/l10n/app_localizations.dart';
import '../widgets/app_menu.dart';

class LevelProcessFocusScreen extends StatelessWidget {
  const LevelProcessFocusScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.levelsAndDirectionsScreenTitle),
        actions: const [
          AppMenu(),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizations.fourLevelsProcessTitle,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            _buildDimensionCard(
              context,
              localizations.elementalLabel,
              localizations.elementalExplanation,
              const Color(0xFF404040),
              Icons.bubble_chart,
            ),
            _buildDimensionCard(
              context,
              localizations.personalLabel,
              localizations.personalExplanation,
              const Color(0xFF595959),
              Icons.favorite,
            ),
            _buildDimensionCard(
              context,
              localizations.informationalLabel,
              localizations.informationalExplanation,
              const Color(0xFF808080),
              Icons.lightbulb,
            ),
            _buildDimensionCard(
              context,
              localizations.socialLabel,
              localizations.socialExplanation,
              const Color(0xFF9F9F9F),
              Icons.handshake,
            ),
            const SizedBox(height: 32),
            Text(
              localizations.hierarchicalStructureTitle,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    localizations.hierarchicalStructureDescription,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildHierarchyItem(
                    localizations.levelsEquationText,
                    1,
                  ),
                  _buildHierarchyItem(
                    localizations.elementalAllocationText,
                    2,
                  ),
                  _buildHierarchyItem(
                    localizations.personalAllocationText,
                    3,
                  ),
                  _buildHierarchyItem(
                    localizations.informationalAllocationText,
                    4,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            Text(
              localizations.directionsFocusTitle,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildOrientationCard(
                    context,
                    localizations,
                    localizations.inwardLabel,
                    localizations.inwardExplanation,
                    Colors.black,
                    Icons.psychology,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildOrientationCard(
                    context,
                    localizations,
                    localizations.outwardLabel,
                    localizations.outwardExplanation,
                    Colors.white,
                    Icons.public,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.purple[50],
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                localizations.directionsExplanationText,
                style: TextStyle(
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDimensionCard(
    BuildContext context,
    String title,
    String description,
    Color color,
    IconData icon,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color,
          child: Icon(
            icon,
            color: color == Colors.white ? Colors.black : Colors.white,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
          child: Text(description),
        ),
      ),
    );
  }

  Widget _buildOrientationCard(
    BuildContext context,
    AppLocalizations localizations,
    String title,
    String description,
    Color color,
    IconData icon,
  ) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            CircleAvatar(
              backgroundColor: color,
              radius: 30,
              child: title == localizations.inwardLabel
                  ? Transform.rotate(
                      angle: 3.14159, // Rotate 180 degrees (pi radians)
                      child: Icon(
                        Icons.wifi,
                        color: color == Colors.white ? Colors.black : Colors.white,
                        size: 30,
                      ),
                    )
                  : Icon(
                      Icons.wifi,
                      color: color == Colors.white ? Colors.black : Colors.white,
                      size: 30,
                    ),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              description,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHierarchyItem(String text, int number) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 12,
            backgroundColor: Colors.blue,
            child: Text(
              number.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(text),
          ),
        ],
      ),
    );
  }
}
