import 'moment_data.dart';

class CSVDataModel {
  final List<MomentData> moments;
  
  CSVDataModel({required this.moments});
  
  String toCSV() {
    final buffer = StringBuffer();
    
    // Add header
    buffer.writeln(MomentData.csvHeader().join(','));
    
    // Add rows
    for (final moment in moments) {
      buffer.writeln(moment.toCsvRow().join(','));
    }
    
    return buffer.toString();
  }
}
