import 'package:flutter/material.dart';

class ValueBarChart extends StatelessWidget {
  final Map<String, double> values;
  final Map<String, Color> colors;
  final String title;
  final bool isInward;
  final String Function(String)? nameMapper; // Function to map English names to localized names
  final String Function(String)? subtitleMapper; // Function to map English names to localized subtitles
  final String Function(String)? descriptionMapper; // Function to map English names to localized descriptions

  const ValueBarChart({
    super.key,
    required this.values,
    required this.colors,
    required this.title,
    required this.isInward,
    this.nameMapper, // Optional localization mapper
    this.subtitleMapper, // Optional subtitle mapper
    this.descriptionMapper, // Optional description mapper
  });

  @override
  Widget build(BuildContext context) {
    // Define the pairs organized by hierarchical levels (without showing level titles)
    final levelPairs = [
      // Social level
      ['Altruist', 'Collaborator'],
      // Informational level
      ['Strategist', 'Tactical'],
      // Personal level
      ['Versatile', 'Fun'],
      // Elemental level
      ['Guardian', 'Warrior'],
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display latent values in side-by-side pairs
        ...levelPairs.map((pair) {
          return Container(
            margin: const EdgeInsets.only(bottom: 24.0),
            child: Row(
              children: pair.map((valueName) {
                final value = values[valueName]?.abs() ?? 0.0;
                final color = colors[valueName] ?? Colors.grey;

                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              nameMapper?.call(valueName) ?? valueName, // Use localized name if mapper provided
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(width: 4),
                            GestureDetector(
                              onTap: () => _showValueDescription(context, valueName),
                              child: Icon(
                                Icons.info_outline,
                                size: 16,
                                color: color, // Use the same color as the latent value
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${(value * 100).toStringAsFixed(1)}%',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          width: double.infinity,
                          height: 16,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: value,
                            child: Container(
                              decoration: BoxDecoration(
                                color: color,
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          );
        }),
      ],
    );
  }







  void _showValueDescription(BuildContext context, String latentValue) {
    // Get localized content using the mappers
    final localizedTitle = nameMapper?.call(latentValue) ?? latentValue;
    final localizedSubtitle = subtitleMapper?.call(latentValue) ?? '';
    final localizedDescription = descriptionMapper?.call(latentValue) ?? _getValueDescription(latentValue);
    final valueColor = colors[latentValue] ?? Colors.grey;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: valueColor,
          title: Text(
            localizedSubtitle.isNotEmpty
              ? '$localizedSubtitle $localizedTitle'.toUpperCase()
              : localizedTitle.toUpperCase(),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: Colors.white,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Show the explanation/description below the dual title
                Text(
                  localizedDescription,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Fechar',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  String _getValueDescription(String value) {
    // This method should not be used anymore since we now use the descriptionMapper
    // from the parent widget that provides proper localized descriptions
    return "Description not available.";
  }
}
