enum MomentType {
  improvesMotivation,
  worsensMotivation,
  improvesSatisfaction,
  worsensSatisfaction
}

extension MomentTypeExtension on MomentType {
  bool get isImproves => this == MomentType.improvesMotivation || this == MomentType.improvesSatisfaction;
  
  bool get isMotivation => this == MomentType.improvesMotivation || this == MomentType.worsensMotivation;
  
  String get name {
    switch (this) {
      case MomentType.improvesMotivation:
        return 'Improves Motivation';
      case MomentType.worsensMotivation:
        return 'Worsens Motivation';
      case MomentType.improvesSatisfaction:
        return 'Improves Satisfaction';
      case MomentType.worsensSatisfaction:
        return 'Worsens Satisfaction';
    }
  }
}
