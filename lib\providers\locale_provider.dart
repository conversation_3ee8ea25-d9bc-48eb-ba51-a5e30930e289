import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleProvider extends ChangeNotifier {
  Locale _locale = const Locale('en');
  String _currentRoute = '/';
  
  Locale get locale => _locale;
  String get currentRoute => _currentRoute;
  
  LocaleProvider() {
    loadSavedLocale();
  }
  
  Future<void> loadSavedLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString('languageCode');

    if (languageCode != null) {
      // User has previously selected a language
      _locale = Locale(languageCode);
    } else {
      // First time - detect device language
      _locale = _detectDeviceLocale();
      // Save the detected locale
      await prefs.setString('languageCode', _locale.languageCode);
    }
    notifyListeners();
  }

  Locale _detectDeviceLocale() {
    // Get device locale from platform
    final deviceLocale = WidgetsBinding.instance.platformDispatcher.locale;

    // Check if device language is supported
    const supportedLanguages = ['en', 'es', 'pt'];

    if (supportedLanguages.contains(deviceLocale.languageCode)) {
      return Locale(deviceLocale.languageCode);
    }

    // Default to English if device language is not supported
    return const Locale('en');
  }
  
  Future<void> setLocale(Locale locale) async {
    if (_locale == locale) return;
    
    _locale = locale;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('languageCode', locale.languageCode);
    
    notifyListeners();
  }
  
  void setCurrentRoute(String route) {
    _currentRoute = route;
    notifyListeners();
  }
  
  Future<void> saveCurrentRoute() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('currentRoute', _currentRoute);
  }
  
  Future<String> getSavedRoute() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('currentRoute') ?? '/';
  }
}
