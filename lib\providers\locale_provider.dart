import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleProvider extends ChangeNotifier {
  Locale _locale = const Locale('en');
  String _currentRoute = '/';
  
  Locale get locale => _locale;
  String get currentRoute => _currentRoute;
  
  LocaleProvider() {
    loadSavedLocale();
  }
  
  Future<void> loadSavedLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString('languageCode');
    
    if (languageCode != null) {
      _locale = Locale(languageCode);
      notifyListeners();
    }
  }
  
  Future<void> setLocale(Locale locale) async {
    if (_locale == locale) return;
    
    _locale = locale;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('languageCode', locale.languageCode);
    
    notifyListeners();
  }
  
  void setCurrentRoute(String route) {
    _currentRoute = route;
    notifyListeners();
  }
  
  Future<void> saveCurrentRoute() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('currentRoute', _currentRoute);
  }
  
  Future<String> getSavedRoute() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('currentRoute') ?? '/';
  }
}
