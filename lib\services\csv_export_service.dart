import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart';
import '../models/csv_data_model.dart';
import '../models/moment_data.dart';

class CSVExportService {
  Future<String> exportToCSV(List<MomentData> moments) async {
    final csvData = CSVDataModel(moments: moments);
    final csvString = csvData.toCSV();
    final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
    final fileName = 'estimat_keymoments_$timestamp.csv';

    try {
      // For web platform, just return the CSV data
      if (kIsWeb) {
        return csvString;
      }

      // For mobile platforms, try to save to a file
      Directory directory;
      try {
        directory = await getTemporaryDirectory();
      } catch (e) {
        // If we can't get a temporary directory, return the CSV data
        return csvString;
      }

      final filePath = '${directory.path}/$fileName';
      final file = File(filePath);
      await file.writeAsString(csvString);

      return filePath;
    } catch (e) {
      // If anything fails, return the CSV data
      return csvString;
    }
  }
}
