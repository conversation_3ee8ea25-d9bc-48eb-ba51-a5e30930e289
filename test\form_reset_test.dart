import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:estimat_keymoments/providers/moments_provider.dart';
import 'package:estimat_keymoments/providers/locale_provider.dart';
import 'package:estimat_keymoments/widgets/moment_type_flow.dart';
import 'package:estimat_keymoments/models/moment_type.dart';

void main() {
  group('Form Reset Tests', () {
    testWidgets('MomentTypeFlow reset functionality works', (WidgetTester tester) async {
      // Create a simple test widget with just the MomentTypeFlow
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiProvider(
              providers: [
                ChangeNotifierProvider(create: (_) => MomentsProvider()),
                ChangeNotifierProvider(create: (_) => LocaleProvider()),
              ],
              child: MomentTypeFlow(
                momentType: MomentType.improvesMotivation,
                onComplete: (moment) {
                  // Mock completion callback
                },
                onMomentTypeSelected: (type) {
                  // Mock type selection callback
                },
              ),
            ),
          ),
        ),
      );

      // Wait for the widget to load
      await tester.pumpAndSettle();

      // Find the title input field
      final titleFields = find.byType(TextFormField);
      expect(titleFields, findsWidgets);

      final titleField = titleFields.first;

      // Enter some text in the title field
      await tester.enterText(titleField, 'Test Title');
      await tester.pumpAndSettle();

      // Verify the text was entered
      expect(find.text('Test Title'), findsOneWidget);

      // Find the MomentTypeFlow widget
      final momentTypeFlow = find.byType(MomentTypeFlow);
      expect(momentTypeFlow, findsOneWidget);

      // Get the state and test reset functionality
      final state = tester.state(momentTypeFlow);
      expect(state, isA<MomentTypeFlowMixin>());

      // Call reset method
      (state as MomentTypeFlowMixin).resetForm();
      await tester.pumpAndSettle();

      // Verify the form was reset - the title field should be empty
      expect(find.text('Test Title'), findsNothing);
    });

    testWidgets('Reset functionality clears all form fields', (WidgetTester tester) async {
      // Test that reset clears all form state, not just text fields
      expect(true, isTrue); // Placeholder for more comprehensive test
    });
  });
}
