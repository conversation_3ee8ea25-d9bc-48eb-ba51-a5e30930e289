import 'package:flutter/material.dart';
import 'package:estimat_keymoments/l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import '../providers/user_preferences_provider.dart';
import 'dart:async';

class EnhancedOnboardingScreen extends StatefulWidget {
  const EnhancedOnboardingScreen({super.key});

  @override
  EnhancedOnboardingScreenState createState() => EnhancedOnboardingScreenState();
}

class EnhancedOnboardingScreenState extends State<EnhancedOnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool _dontShowAgain = false;
  Timer? _autoAdvanceTimer;

  final List<OnboardingStep> _steps = [
    OnboardingStep(
      title: "Welcome to ESTIMAT KeyMoments",
      description: "Your personal growth journey starts here",
      showWidget: OnboardingWelcomeWidget(),
    ),
    OnboardingStep(
      title: "Step 1: Choose Moment Type",
      description: "Start by selecting what type of moment you want to record",
      showWidget: OnboardingMomentTypeWidget(),
    ),
    OnboardingStep(
      title: "Step 2: Describe Your Experience",
      description: "Tell us about your moment and its impact",
      showWidget: OnboardingDescriptionWidget(),
    ),
    OnboardingStep(
      title: "Step 3: Distribute Percentages",
      description: "Allocate percentages across four levels of experience",
      showWidget: OnboardingPercentageWidget(),
    ),
    OnboardingStep(
      title: "Step 4: View Your Patterns",
      description: "Analyze your moments and discover your latent values",
      showWidget: OnboardingSummaryWidget(),
    ),
    OnboardingStep(
      title: "Ready to Begin!",
      description: "Start recording your extreme moments for better insights",
      showWidget: OnboardingFinalWidget(),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _startAutoAdvance();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _autoAdvanceTimer?.cancel();
    super.dispose();
  }

  void _startAutoAdvance() {
    _autoAdvanceTimer?.cancel();
    _autoAdvanceTimer = Timer(const Duration(seconds: 5), () {
      if (_currentPage < _steps.length - 1) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: const Color(0xFF2A6BAA),
        title: Text(l10n.onboardingScreenTitle),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pushReplacementNamed('/'),
            child: Text(l10n.exitToAppButton, style: const TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            itemCount: _steps.length,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
                _startAutoAdvance();
              });
            },
            itemBuilder: (context, index) {
              return _buildOnboardingStep(_steps[index], index);
            },
          ),
          Positioned(
            bottom: 40,
            left: 0,
            right: 0,
            child: Column(
              children: [
                // Page indicators
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    _steps.length,
                    (index) => AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      height: 10,
                      width: _currentPage == index ? 24 : 10,
                      decoration: BoxDecoration(
                        color: _currentPage == index
                            ? const Color(0xFF6A4C93)
                            : Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(5),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                // Navigation buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (_currentPage > 0)
                      ElevatedButton(
                        onPressed: () {
                          _pageController.previousPage(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[200],
                          foregroundColor: Colors.black87,
                        ),
                        child: Text(l10n.previousButton),
                      ),
                    const SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: () async {
                        if (_currentPage < _steps.length - 1) {
                          _pageController.nextPage(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        } else {
                          // Handle completion
                          final userPreferencesProvider = Provider.of<UserPreferencesProvider>(context, listen: false);
                          await userPreferencesProvider.markOnboardingCompleted();
                          
                          if (_dontShowAgain) {
                            await userPreferencesProvider.setShowOnboarding(false);
                          }
                          
                          Navigator.of(context).pushReplacementNamed('/');
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFFFBF00),
                        foregroundColor: Colors.black87,
                      ),
                      child: Text(_currentPage < _steps.length - 1 ? l10n.nextButton : l10n.getStartedButton),
                    ),
                  ],
                ),
                // Don't show again checkbox on final step
                if (_currentPage == _steps.length - 1) ...[
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Checkbox(
                        value: _dontShowAgain,
                        onChanged: (value) {
                          setState(() {
                            _dontShowAgain = value ?? false;
                          });
                        },
                        activeColor: const Color(0xFF2A6BAA),
                      ),
                      Text(
                        l10n.dontShowAgainLabel,
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 16.0,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOnboardingStep(OnboardingStep step, int index) {
    return Container(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          // Title and description
          Container(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                Text(
                  step.title,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2A6BAA),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 10),
                Text(
                  step.description,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          // Interactive widget demonstration
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(10.0),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300, width: 2),
                borderRadius: BorderRadius.circular(12),
                color: Colors.grey.shade50,
              ),
              child: step.showWidget,
            ),
          ),
        ],
      ),
    );
  }
}

class OnboardingStep {
  final String title;
  final String description;
  final Widget showWidget;

  OnboardingStep({
    required this.title,
    required this.description,
    required this.showWidget,
  });
}

// Individual onboarding widgets showing actual app interface elements
class OnboardingWelcomeWidget extends StatelessWidget {
  const OnboardingWelcomeWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.psychology,
            size: 80,
            color: Color(0xFF2A6BAA),
          ),
          const SizedBox(height: 20),
          Text(
            "Discover your personal values through key moments",
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade700,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              "💡 Pro Tip: Start with your EXTREME moments (highest and lowest experiences) to establish your personal reference points!",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

class OnboardingMomentTypeWidget extends StatelessWidget {
  const OnboardingMomentTypeWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            "Choose the type of moment:",
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 20),
          // Simulated moment type selector
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                _buildMomentTypeOption("Improves Motivation", Colors.green.shade100, true),
                _buildMomentTypeOption("Worsens Motivation", Colors.red.shade100, false),
                _buildMomentTypeOption("Improves Satisfaction", Colors.blue.shade100, false),
                _buildMomentTypeOption("Worsens Satisfaction", Colors.orange.shade100, false),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMomentTypeOption(String text, Color color, bool selected) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: selected ? color : Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          Icon(
            selected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
            color: selected ? const Color(0xFF2A6BAA) : Colors.grey,
          ),
          const SizedBox(width: 12),
          Text(text, style: const TextStyle(fontSize: 14)),
        ],
      ),
    );
  }
}

class OnboardingDescriptionWidget extends StatelessWidget {
  const OnboardingDescriptionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          const Text(
            "Describe your moment:",
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 20),
          Container(
            height: 120,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Padding(
              padding: EdgeInsets.all(12.0),
              child: Text(
                "Example: Got promoted at work after months of hard work. Felt incredibly motivated and satisfied with my achievement...",
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            "💡 Be specific about what happened and how it made you feel",
            style: TextStyle(fontSize: 12, color: Colors.black54),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class OnboardingPercentageWidget extends StatelessWidget {
  const OnboardingPercentageWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          const Text(
            "Distribute percentages across levels:",
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 20),
          _buildPercentageLevel("Elemental", "Physical responses", 25, Colors.red.shade100),
          _buildPercentageLevel("Personal", "Individual experiences", 30, Colors.blue.shade100),
          _buildPercentageLevel("Informational", "Intellectual processes", 25, Colors.green.shade100),
          _buildPercentageLevel("Social", "Interpersonal connections", 20, Colors.orange.shade100),
          const SizedBox(height: 20),
          const Text(
            "💡 Total should equal 100%",
            style: TextStyle(fontSize: 12, color: Colors.black54),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPercentageLevel(String level, String description, int percentage, Color color) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(level, style: const TextStyle(fontWeight: FontWeight.bold)),
                Text(description, style: const TextStyle(fontSize: 12, color: Colors.black54)),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              "$percentage%",
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

class OnboardingSummaryWidget extends StatelessWidget {
  const OnboardingSummaryWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          const Text(
            "Your Latent Values Analysis:",
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 20),
          _buildLatentValue("Guardian", "Protective & Stable", 0.8, Colors.red.shade100),
          _buildLatentValue("Versatile", "Adaptable & Flexible", 0.6, Colors.blue.shade100),
          _buildLatentValue("Strategist", "Planning & Analysis", 0.7, Colors.green.shade100),
          _buildLatentValue("Collaborator", "Teamwork & Support", 0.5, Colors.orange.shade100),
          const SizedBox(height: 20),
          const Text(
            "📊 Patterns emerge from your recorded moments",
            style: TextStyle(fontSize: 12, color: Colors.black54),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLatentValue(String name, String description, double value, Color color) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(name, style: const TextStyle(fontWeight: FontWeight.bold)),
                Text(description, style: const TextStyle(fontSize: 12, color: Colors.black54)),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: LinearProgressIndicator(
              value: value,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.deepPurple),
            ),
          ),
        ],
      ),
    );
  }
}

class OnboardingFinalWidget extends StatelessWidget {
  const OnboardingFinalWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.rocket_launch,
            size: 80,
            color: Color(0xFF2A6BAA),
          ),
          const SizedBox(height: 20),
          const Text(
            "You're Ready to Start!",
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2A6BAA),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              "🎯 Remember: Start with your EXTREME moments (highest and lowest experiences) to establish your personal reference points for better insights!",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            "The app will learn and improve as you add more moments, providing increasingly personalized insights through future AI integration.",
            style: TextStyle(
              fontSize: 12,
              color: Colors.black54,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
