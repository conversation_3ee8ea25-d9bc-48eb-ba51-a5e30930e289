import 'dart:math';
import 'package:flutter/material.dart';

class Proportion<PERSON><PERSON><PERSON>hart extends StatelessWidget {
  final double inwardPercentage;
  final double outwardPercentage;

  const ProportionPieChart({
    super.key,
    required this.inwardPercentage,
    required this.outwardPercentage,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 200,
      height: 200,
      child: CustomPaint(
        painter: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
          inwardPercentage: inwardPercentage / 100,
          outwardPercentage: outwardPercentage / 100,
        ),
      ),
    );
  }
}

class PieChartPainter extends CustomPainter {
  final double inwardPercentage;
  final double outwardPercentage;

  PieChartPainter({
    required this.inwardPercentage,
    required this.outwardPercentage,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2;

    // Define colors - black for inward (left side) and white for outward (right side)
    final inwardColor = Colors.black;
    final outwardColor = Colors.white;

    // Draw outward portion (white background)
    final outwardPaint = Paint()
      ..color = outwardColor
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius, outwardPaint);

    // Draw inward portion as a pie slice (black) - starting from right side going counter-clockwise to left
    if (inwardPercentage > 0) {
      final inwardPaint = Paint()
        ..color = inwardColor
        ..style = PaintingStyle.fill;

      final startAngle = -pi / 2; // Start from the top (270 degrees)
      final sweepAngle = -2 * pi * inwardPercentage; // Negative sweep to go counter-clockwise

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        inwardPaint,
      );
    }

  }

  @override
  bool shouldRepaint(covariant PieChartPainter oldDelegate) {
    return oldDelegate.inwardPercentage != inwardPercentage ||
        oldDelegate.outwardPercentage != outwardPercentage;
  }
}
