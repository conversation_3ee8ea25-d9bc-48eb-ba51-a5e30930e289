import 'package:flutter/material.dart';
import 'package:estimat_keymoments/l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import '../providers/user_preferences_provider.dart';
import 'dart:async';

class OnboardingScene {
  final String emoji;
  final String messageKey;

  OnboardingScene({required this.emoji, required this.messageKey});
}

class OnboardingPresentationScreen extends StatefulWidget {
  const OnboardingPresentationScreen({super.key});

  @override
  OnboardingPresentationScreenState createState() => OnboardingPresentationScreenState();
}

class OnboardingPresentationScreenState extends State<OnboardingPresentationScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  List<OnboardingScene> _scenes = [];
  Timer? _secondPartTimer;
  bool _dontShowAgain = false;

  @override
  void initState() {
    super.initState();
    _startSecondPartTimer(); // Start timer for the initial page
  }

  @override
  void dispose() {
    _pageController.dispose();
    _secondPartTimer?.cancel(); // Cancel timer in dispose
    super.dispose();
  }

  void _startSecondPartTimer() {
    _secondPartTimer?.cancel(); // Cancel any existing timer
    _secondPartTimer = Timer(const Duration(milliseconds: 1600), () {
      // No need to call setState here, AnimatedOpacity handles the animation
    });
  }

  String _getSceneMessage(BuildContext context, int sceneIndex) {
    final l10n = AppLocalizations.of(context)!;

    switch (sceneIndex) {
      case 0:
        return l10n.onboardingScene1Message;
      case 1:
        return l10n.onboardingScene2Message;
      case 2:
        return l10n.onboardingScene3Message;
      case 3:
        return l10n.onboardingScene4Message;
      case 4:
        return l10n.onboardingFlowchartMessage;
      default:
        return '';
    }

  }

  String _getSceneMessagePart2(BuildContext context, int sceneIndex) {
    final l10n = AppLocalizations.of(context)!;

    switch (sceneIndex) {
      case 0:
        return l10n.onboardingScene1MessagePart2;
      case 1:
        return l10n.onboardingScene2MessagePart2;
      case 2:
        return l10n.onboardingScene3MessagePart2;
      case 3:
        return l10n.onboardingScene4MessagePart2;
      case 4:
        return l10n.onboardingFlowchartMessage;
      default:
        return '';
    }

  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    _scenes = [
      OnboardingScene(emoji: '🎵', messageKey: "onboardingScene1Message"),
      OnboardingScene(emoji: '🎨', messageKey: "onboardingScene2Message"),
      OnboardingScene(emoji: '🧠', messageKey: "onboardingScene3Message"),
      OnboardingScene(emoji: '💡', messageKey: "onboardingScene4Message"),
      OnboardingScene(emoji: '📋', messageKey: "onboardingFlowchartMessage"),
    ];

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: const Color(0xFF2A6BAA),
        title: Text(l10n.onboardingScreenTitle),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pushReplacementNamed('/'),
            child: Text(l10n.exitToAppButton, style: const TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            itemCount: _scenes.length,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
                _startSecondPartTimer(); // Restart timer on page change
              });
            },
            itemBuilder: (context, index) {
              return _buildAnimatedScene(_scenes[index], index);
            },
          ),
          Positioned(
            bottom: 40,
            left: 0,
            right: 0,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    _scenes.length,
                    (index) => AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      height: 10,
                      width: _currentPage == index ? 24 : 10,
                      decoration: BoxDecoration(
                        color: _currentPage == index
                            ? const Color(0xFF6A4C93)
                            : Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(5),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (_currentPage > 0)
                      ElevatedButton(
                        onPressed: () {
                          _pageController.previousPage(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[200],
                          foregroundColor: Colors.black87,
                        ),
                        child: Text(l10n.previousButton),
                      ),
                    const SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: () async {
                        if (_currentPage < _scenes.length - 1) {
                          _pageController.nextPage(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        } else {
                          // Handle completion of onboarding
                          final userPreferencesProvider = Provider.of<UserPreferencesProvider>(context, listen: false);
                          await userPreferencesProvider.markOnboardingCompleted();

                          if (_dontShowAgain) {
                            await userPreferencesProvider.setShowOnboarding(false);
                          }

                          Navigator.of(context).pushReplacementNamed('/');
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFFFBF00),
                        foregroundColor: Colors.black87,
                      ),
                      child: Text(_currentPage < _scenes.length - 1 ? l10n.nextButton : l10n.getStartedButton),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedScene(final OnboardingScene scene, final int index) {
    final String messageText = _getSceneMessage(context, index);
    final String secondPartText = _getSceneMessagePart2(context, index);

    if (index == 4) {
      return _buildFlowchartScene(scene, messageText);
    }

    final bool isLightBackground = index == 3 || index == 4;
    final Color textColor = isLightBackground ? Colors.black : Colors.white;
    final Color secondPartTextColor = isLightBackground ? Colors.black87 : Colors.white70;


    // Original animated scene content
    return Container(
      padding: const EdgeInsets.all(30.0),
      color: isLightBackground ? Colors.white : Colors.black,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 800),
            tween: Tween<double>(begin: 0.5, end: 1.0), // Made Tween non-const
            curve: Curves.elasticOut,
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Text(
                  scene.emoji,
                  style: TextStyle(fontSize: 100, color: textColor), // Increased emoji size for animation and set color
                ),
              );
            },
          ),
          const SizedBox(height: 50),
          Text(
            messageText,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: textColor,
              fontSize: 24.0,
              height: 1.5,
              fontWeight: FontWeight.w300,
            ),
          ),
          if (secondPartText.isNotEmpty)
            FutureBuilder(
              key: ValueKey(index), // Use index as key to restart future on page change
              future: Future.delayed(const Duration(milliseconds: 1600)),
              builder: (context, snapshot) {
                return AnimatedOpacity(
                  opacity: snapshot.connectionState == ConnectionState.done ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 500),
                  child: Column( // Use Column to keep the SizedBox and Text together
                    children: [
                      const SizedBox(height: 20),
                      Text(
                        secondPartText,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: secondPartTextColor,
                          fontSize: 20.0,
                          height: 1.5,
                          fontWeight: FontWeight.w300,
                          fontStyle: FontStyle.italic, // Added italic for part 2
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildFlowchartScene(final OnboardingScene scene, final String messageText) {
    final l10n = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.all(30.0),
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            scene.emoji,
            style: const TextStyle(
              fontSize: 48.0, // Standard emoji size for flowchart
              height: 1.2,
              color: Colors.black, // Ensure emoji is visible on white background
            ),
          ),
          const SizedBox(height: 30),
          Text(
            messageText,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 24.0,
              height: 1.5,
              fontWeight: FontWeight.w300,
            ),
          ),
          const SizedBox(height: 40),
          // Add "Don't show again" checkbox
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Checkbox(
                value: _dontShowAgain,
                onChanged: (value) {
                  setState(() {
                    _dontShowAgain = value ?? false;
                  });
                },
                activeColor: const Color(0xFF2A6BAA),
              ),
              Text(
                l10n.dontShowAgainLabel,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 16.0,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
