// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appTitle => 'Estimat KeyMoments';

  @override
  String get selectLanguageTitle => 'Seleccionar Idioma';

  @override
  String get englishLanguage => 'Inglés';

  @override
  String get portugueseLanguage => 'Portugués';

  @override
  String get spanishLanguage => 'Español';

  @override
  String get onboardingScreenTitle => 'Bienvenido a Estimat KeyMoments';

  @override
  String get onboardingScene1Message =>
      'El ruido puede simplificarse en notas y convertirse en música';

  @override
  String get onboardingScene1MessagePart2 => 'Con un poco de proporción...';

  @override
  String get onboardingScene2Message =>
      'Cualquier imagen puede ser replicada a través de luz, sombra y color, y transformada en arte';

  @override
  String get onboardingScene2MessagePart2 => 'Con un poco de proporción...';

  @override
  String get onboardingScene3Message =>
      'Tu sobrecarga cognitiva—¿podrías refinar lo que más valoras en ti mismo?';

  @override
  String get onboardingScene3MessagePart2 => 'Con un poco de ...';

  @override
  String get onboardingScene4Message =>
      'Con la teoría de la información, una IA puede reducir datos complejos al 5% y reconstruirlos con 95% de fidelidad funcional; estos son vectores latentes';

  @override
  String get onboardingScene4MessagePart2 =>
      'Tal vez podrías aplicar un poco de eso a tus valores latentes...';

  @override
  String get onboardingFlowchartMessage =>
      'Identifiquemos nuestros momentos de referencia. Rastreemos los flujos asociados con esos momentos. Evaluemos según su función. Descubramos los valores latentes que nos impulsan:';

  @override
  String get onboardingFlowchartTitle => 'Flujo del Proceso';

  @override
  String get nextButton => 'Siguiente';

  @override
  String get previousButton => 'Anterior';

  @override
  String get getStartedButton => 'Comenzar';

  @override
  String get exitToAppButton => 'Salir de la aplicación';

  @override
  String get motivationAppBarTitle => 'Momentos de Motivación';

  @override
  String get satisfactionAppBarTitle => 'Momentos de Satisfacción';

  @override
  String get selectMomentTypeTitle => 'Seleccionar Tipo de Momento';

  @override
  String get selectImprovementWorsening =>
      'Elige entre Mejora o Empeoramiento:';

  @override
  String get selectMotivationSatisfaction =>
      'Elige entre Motivación o Satisfacción:';

  @override
  String get improvesLabel => 'Mejora';

  @override
  String get worsensLabel => 'Empeora';

  @override
  String get motivationLabel => 'Motivación';

  @override
  String get satisfactionLabel => 'Satisfacción';

  @override
  String get elementalLabel => 'Elemental';

  @override
  String get personalLabel => 'Personal';

  @override
  String get informationalLabel => 'Informacional';

  @override
  String get socialLabel => 'Social';

  @override
  String get elementalExplanation =>
      'En función de los estímulos del entorno, damos una respuesta que fue modulada por la evolución. Más correlacionado con actividades físicas.';

  @override
  String get personalExplanation =>
      'Sumándose a la memoria que viene de nuestros ancestros, tenemos una memoria que se crea a partir de la interacción única entre un individuo y su entorno. Cuanto más dolor, más adaptativo para la supervivencia. Cuanto más placenteras, más motivador para la supervivencia. Más correlacionado con hobbies, gustos, autoconocimiento.';

  @override
  String get informationalExplanation =>
      'Al llegar al nivel intelectual, además de integrar memorias y experiencias con el entorno, logramos una capacidad única: relacionar recuerdos o memorias propias de un individuo entre sí y codificar la información abstracta (ejemplo: signos matemáticos o de lenguaje).';

  @override
  String get socialExplanation =>
      'Las relaciones sociales implican la conexión de información entre individuos, el intercambio de cultura y la comunicación. Podemos definir un límite entre nivel intelectual y nivel social cuando utilizamos nuestros procesos intelectuales para interactuar con otros seres capaces de combinar sus propias memorias.';

  @override
  String get inwardLabel => 'Interior';

  @override
  String get outwardLabel => 'Exterior';

  @override
  String get inwardExplanation =>
      'Reflexivo: Para almacenar, transformarte a ti mismo, predecir, empatizar.';

  @override
  String get outwardExplanation =>
      'Intuitivo: Para ejecutar, disfrutar, rastrear, colaborar.';

  @override
  String get titleInputHint => 'Ingresa un título de dos palabras';

  @override
  String get descriptionInputHint => 'Describe este momento';

  @override
  String get evidenceInputHint =>
      'Proporciona evidencia para esta distribución';

  @override
  String get describeDistributionHint =>
      'Describe por qué elegiste esta distribución...';

  @override
  String get firstMomentEvidenceHint =>
      'Explica por qué este momento crea más posibilidades que simplemente estar vivo. ¿Qué nuevas oportunidades, caminos o potencial abre?';

  @override
  String get comparisonEvidenceHint =>
      'Explica cuántas posibilidades crea este momento comparado con tu línea base de vida...';

  @override
  String get lifePossibilitiesFactorTitle => 'Factor de Posibilidades de Vida';

  @override
  String get howManyPossibilitiesTitle =>
      '¿Cuántas Posibilidades Crea Este Momento?';

  @override
  String get comparedToLifeBaseline =>
      'Comparado con tu línea base de vida, este momento crea:';

  @override
  String get morePossibilitiesButton => 'Más Posibilidades';

  @override
  String get fewerPossibilitiesButton => 'Menos Posibilidades';

  @override
  String get vsLifeBaseline => 'vs Línea Base de Vida';

  @override
  String get explainWhyFirstMomentTitle =>
      'Explica Por Qué Este Momento Crea Más Posibilidades Que La Vida';

  @override
  String get provideEvidenceTitle =>
      'Proporciona Evidencia para esta Evaluación de Posibilidades';

  @override
  String get continueButton => 'Continuar';

  @override
  String get lifePossibilitiesChart => 'Gráfico de Posibilidades de Vida';

  @override
  String get allTimeFilter => 'Todo el Tiempo';

  @override
  String get lastWeekFilter => 'Última Semana';

  @override
  String get lastMonthFilter => 'Último Mes';

  @override
  String get last3MonthsFilter => 'Últimos 3 Meses';

  @override
  String get currentPreviewLabel => 'Vista Previa Actual:';

  @override
  String get lifeLabel => 'Vida';

  @override
  String get previewLabel => 'Vista Previa';

  @override
  String get lifeBaselineLabel => 'Línea Base de Vida (1x)';

  @override
  String get morePossibilitiesLabel => 'Más Posibilidades';

  @override
  String get fewerPossibilitiesLabel => 'Menos Posibilidades';

  @override
  String get currentPreviewLegend => 'Vista Previa Actual';

  @override
  String get lifePossibilitiesExplanation =>
      'Compara este momento con tu línea base de vida (1x). ¿Cuántas veces más posibilidades crea este momento para ti? Puedes ir por encima o por debajo de momentos anteriores, pero nunca por debajo de la línea base de vida.\\n\\nEl cálculo factorial representa el crecimiento exponencial de posibilidades que los momentos significativos pueden crear en tu vida.';

  @override
  String get minimumLifeBaselineNote => 'Mínimo: 1.0x (línea base de vida)';

  @override
  String get guardianLabel => 'Guardián';

  @override
  String get warriorLabel => 'Guerrero';

  @override
  String get versatileLabel => 'Versátil';

  @override
  String get funLabel => 'Divertido';

  @override
  String get strategistLabel => 'Estratega';

  @override
  String get tacticalLabel => 'Táctico';

  @override
  String get altruistLabel => 'Altruista';

  @override
  String get collaboratorLabel => 'Colaborador';

  @override
  String get summaryTitle => 'Resumen';

  @override
  String get motivationSectionTitle => 'Análisis de Motivación';

  @override
  String get satisfactionSectionTitle => 'Análisis de Satisfacción';

  @override
  String get latentValuesSectionTitle => 'Valores Latentes';

  @override
  String get improvesMotivationLabel => 'Mejora Motivación';

  @override
  String get worsensMotivationLabel => 'Empeora Motivación';

  @override
  String get improvesSatisfactionLabel => 'Mejora Satisfacción';

  @override
  String get worsensSatisfactionLabel => 'Empeora Satisfacción';

  @override
  String get proportionLabel => 'Proporción';

  @override
  String get exportDataButton => 'Exportar Datos';

  @override
  String get viewOnboardingMenu => 'Introducción';

  @override
  String get viewFullPresentationMenu => 'Presentación Completa';

  @override
  String get viewLevelProcessFocusMenu => 'Niveles y Direcciones';

  @override
  String get viewLatentValuesMenu => 'Funciones Evolutivas y Valores';

  @override
  String get fromIACodeToHumanValuesMenu =>
      'Del código IA a los valores humanos';

  @override
  String get jacoMinestSupportMenu => 'Apoyo Jacominest';

  @override
  String get changeLanguageMenu => 'Cambiar Idioma';

  @override
  String get supportScreenTitle => 'Apoyo';

  @override
  String get supportMainMessage => 'Salvar una mente salva más que mil latas.';

  @override
  String get supportIntroText =>
      'Las chances pueden no estar mucho a favor, pero salvar una mente que puede estar perdida, desesperanzada o atrapada en ciclos destructivos puede generar retornos exponenciales a largo plazo.';

  @override
  String get supportEvidenceTitle => 'Evidencias';

  @override
  String get supportStatistic1 =>
      'Las personas que participan en programas educativos en prisión tienen 43% menos probabilidades de regresar.';

  @override
  String get supportStatistic1Source =>
      'RAND Corporation (2013). \"Evaluating the Effectiveness of Correctional Education\"';

  @override
  String get supportStatistic1Link =>
      'https://www.rand.org/pubs/research_reports/RR266.html';

  @override
  String get supportStatistic2 =>
      'Cada dólar invertido en educación penitenciaria puede ahorrar entre cuatro y cinco dólares en costos de re-encarcelamiento.';

  @override
  String get supportStatistic2Source =>
      'Davis, L.M. et al. (2013). \"How Effective Is Correctional Education, and Where Do We Go from Here?\"';

  @override
  String get supportStatistic2Link =>
      'https://bja.ojp.gov/sites/g/files/xyckuh186/files/Publications/RAND_Correctional-Education-Meta-Analysis.pdf';

  @override
  String get supportStatistic3 =>
      'El empleo después del alta es 13% más alto entre quienes participaron en programas educativos...';

  @override
  String get supportStatistic3Source =>
      'Bureau of Justice Statistics, U.S. Department of Justice';

  @override
  String get supportStatistic3Link => 'https://bjs.ojp.gov/topics/corrections';

  @override
  String get supportConsumptionTitle => 'Personas y Reciclaje';

  @override
  String get supportConsumptionSubtitle =>
      'Además que una persona a lo largo de su vida consume:';

  @override
  String get supportConsumption1 => 'Miles de kilos de alimentos y envases';

  @override
  String get supportConsumption2 => 'Toneladas de agua';

  @override
  String get supportConsumption3 =>
      'Energía eléctrica equivalente a años de consumo doméstico';

  @override
  String get supportConsumption4 =>
      'Materiales de construcción, ropa, tecnología...';

  @override
  String get expandToSeeMore => 'Toca para ver más';

  @override
  String get tapToCollapse => 'Toca para contraer';

  @override
  String get supportAppDescription =>
      'Esta app fue creada con años de dedicación para mejorar valores latentes usando conocimiento moderno y matemático.';

  @override
  String get supportThanksJacominest =>
      'Gracias asociación Jacominesp por apoyar y formas de valorar mejor los recursos del mundo, empezando por quienes más necesitan esperanza.';

  @override
  String get supportThanksWitness =>
      'Gracias por ser testigo de su lucha, María Rosa de Siqueira.';

  @override
  String get supportSourcesTitle => 'Fuentes:';

  @override
  String get supportSource1 =>
      'RAND Corporation (2013). \"Evaluating the Effectiveness of Correctional Education\"';

  @override
  String get supportSource2 =>
      'Davis, L.M. et al. (2013). \"How Effective Is Correctional Education, and Where Do We Go from Here?\"';

  @override
  String get supportSource3 =>
      'Bureau of Justice Statistics, U.S. Department of Justice';

  @override
  String get continueToWebsiteButton => 'Continuar al Sitio Web';

  @override
  String get coffeeScreenTitle => 'Cómprame un Café';

  @override
  String get coffeeMainMessage => 'Apoya el desarrollo de ESTIMAT';

  @override
  String get coffeeIntroText =>
      'Tu apoyo nos ayuda a continuar mejorando esta aplicación y desarrollando nuevas funciones para ayudar a las personas a entender mejor sus valores latentes y tomar decisiones más conscientes.';

  @override
  String get coffeeWhySupport => '¿Por qué apoyarnos?';

  @override
  String get coffeeReason1 =>
      'Mantener la aplicación gratuita y accesible para todos';

  @override
  String get coffeeReason2 =>
      'Financiar investigación y desarrollo de nuevas funciones';

  @override
  String get coffeeReason3 =>
      'Apoyar la investigación matemática y psicológica detrás de ESTIMAT';

  @override
  String get coffeeReason4 =>
      'Ayudarnos a llegar a más personas que necesitan mejores herramientas de toma de decisiones';

  @override
  String get coffeeDonationOptions => 'Elige tu nivel de apoyo';

  @override
  String get coffeeSmall => 'Café Pequeño';

  @override
  String get coffeeSmallDesc => 'Un simple gracias';

  @override
  String get coffeeMedium => 'Café Grande';

  @override
  String get coffeeMediumDesc => 'Apoyar desarrollo';

  @override
  String get coffeeLarge => 'Café y Pastel';

  @override
  String get coffeeLargeDesc => 'Impulsar nuestra investigación';

  @override
  String get coffeeCustom => 'Cantidad Personalizada';

  @override
  String get coffeeCustomDesc => 'Elige tu propia cantidad';

  @override
  String get contactTitle => 'Contacto y Comentarios';

  @override
  String get contactEmail => '<EMAIL>';

  @override
  String get contactEmailDesc =>
      'Envíanos tus comentarios, sugerencias o preguntas';

  @override
  String get contactThankYou => '¡Gracias por tu apoyo!';

  @override
  String get contactDevelopmentTeam => 'Equipo de Desarrollo';

  @override
  String get contactResearchTeam => 'Investigación y Fundación Matemática';

  @override
  String get buyNowButton => 'Comprar Ahora';

  @override
  String get sendEmailButton => 'Enviar Email';

  @override
  String get copyEmailButton => 'Copiar Email';

  @override
  String get emailCopiedMessage => '¡Email copiado al portapapeles!';

  @override
  String get coffeeMenuTitle => 'Apoyar Desarrollo';

  @override
  String get saveButton => 'Guardar';

  @override
  String get cancelButton => 'Cancelar';

  @override
  String get backButton => 'Atrás';

  @override
  String get whyMomentsScreenTitle => '¿Por qué Momentos?';

  @override
  String get whyMomentsMenuTitle => '¿Por qué Momentos?';

  @override
  String get whyHierarchicalMenuTitle => '¿Por qué Organización Jerárquica?';

  @override
  String get whyHierarchicalScreenTitle => '¿Por qué Organización Jerárquica?';

  @override
  String get levelsAndDirectionsScreenTitle => 'Niveles y Direcciones';

  @override
  String get skipForNowButton => 'Omitir por ahora';

  @override
  String get noMomentsRecordedYet => 'Aún no se han registrado momentos.';

  @override
  String pageCounter(int current, int total) {
    return '$current / $total';
  }

  @override
  String get welcomeToEstimatKeyMoments => 'Bienvenido a Estimat KeyMoments';

  @override
  String get estimatKeyMomentsDescription =>
      'Una metodología integral para comprender y analizar los momentos clave que dan forma a tus decisiones de vida y crecimiento personal.';

  @override
  String get editThisMomentButton => 'Editar este momento';

  @override
  String get closeButton => 'Cerrar';

  @override
  String get insertNewMomentButton => 'Insertar un nuevo momento';

  @override
  String get viewAllMomentsButton => 'Ver Todos los Momentos';

  @override
  String get momentsHistoryTitle => 'Historial de Momentos';

  @override
  String momentSavedTitle(String momentType) {
    return '$momentType Guardado';
  }

  @override
  String get momentSavedMessage => 'Tu momento ha sido guardado exitosamente.';

  @override
  String get guardianDisplayName => 'GUARDIÁN';

  @override
  String get guardianDisplayNameSmall => 'Cuidador';

  @override
  String get warriorDisplayName => 'GUERRERO';

  @override
  String get warriorDisplayNameSmall => 'Liberador';

  @override
  String get versatileDisplayName => 'VERSÁTIL';

  @override
  String get versatileDisplayNameSmall => 'AutoObservador';

  @override
  String get funDisplayName => 'DIVERTIDO';

  @override
  String get funDisplayNameSmall => 'Entusiasta';

  @override
  String get strategistDisplayName => 'ESTRATEGA';

  @override
  String get strategistDisplayNameSmall => 'Analista';

  @override
  String get tacticalDisplayName => 'TÁCTICO';

  @override
  String get tacticalDisplayNameSmall => 'Sintetizador';

  @override
  String get altruistDisplayName => 'ALTRUISTA';

  @override
  String get altruistDisplayNameSmall => 'Empático';

  @override
  String get collaboratorDisplayName => 'COLABORADOR';

  @override
  String get collaboratorDisplayNameSmall => 'Diplomático';

  @override
  String get guardianDescription =>
      'Reconocer: Reconoces, consumes, ingieres.\\nAlmacenar: Descansas, almacenas y metabolizas.';

  @override
  String get warriorDescription =>
      'Descartar: Descartas, huyes, inhibes.\\nEjecutar: Luchas, atacas y te esfuerzas.';

  @override
  String get versatileDescription =>
      'Auto-Observar: Destacas información negativa, sientes dolor, observas errores.\\nAuto-Transformar: Reduces tus errores y adaptas estímulos.';

  @override
  String get funDescription =>
      'Auto-Motivar: Destacas información positiva, sientes placer.\\nAuto-Disfrutar: Realzo éxitos y contrasto actitudes e ideas.';

  @override
  String get strategistDescription =>
      'Analizar: Revisas tendencias, preguntas si algo podría ser falso, analizas.\\nPredecir: Predices lo que es más probable y creas hipótesis.';

  @override
  String get tacticalDescription =>
      'Simplificar: Generalizas, comparas la forma más fácil y rápida.\\nRastrear: Buscas, cazas y rastreas.';

  @override
  String get altruistDescription =>
      'Empatizar: Empatizas con lo que es importante para otros.\\nDeliberar: Consideras a ti mismo y las necesidades de tantas personas como sea posible, practicando altruismo eficiente.';

  @override
  String get collaboratorDescription =>
      'Negociar: Ayudas a entender, comunicar.\\nColaborar: Cooperas hacia objetivos compartidos.';

  @override
  String get descriptionNotAvailable => 'Descripción no disponible.';

  @override
  String get exportDataTitle => 'Exportar Datos';

  @override
  String get exportOptionsTitle => 'Opciones de Exportación';

  @override
  String get exportToCSVTitle => 'Exportar a CSV';

  @override
  String get exportToCSVSubtitle =>
      'Guarda los datos de tus momentos como un archivo CSV';

  @override
  String get exportSuccessfulTitle => '¡Exportación Exitosa!';

  @override
  String fileSavedToLabel(String filePath) {
    return 'Archivo guardado en: $filePath';
  }

  @override
  String get viewFileButton => 'Ver Archivo';

  @override
  String get csvDataExportedSuccessfully =>
      'Datos CSV exportados exitosamente:';

  @override
  String get exportFailedTitle => 'Falló la Exportación';

  @override
  String get noMomentsToExport => 'No hay momentos para exportar.';

  @override
  String errorExportingData(String error) {
    return 'Error al exportar datos: $error';
  }

  @override
  String fileNotFound(String filePath) {
    return 'Archivo no encontrado: $filePath';
  }

  @override
  String errorOpeningFile(String error) {
    return 'Error al abrir archivo: $error';
  }

  @override
  String factorialComparisonLabel(String comparisonType) {
    return 'Comparación Factorial: $comparisonType';
  }

  @override
  String get improvementLabel => 'Mejora';

  @override
  String get worseningLabel => 'Empeoramiento';

  @override
  String factorialSliderValueLabel(String value) {
    return 'Valor del Deslizador Factorial: $value';
  }

  @override
  String factorialMultiplierLabel(String value) {
    return 'Multiplicador Factorial: $value';
  }

  @override
  String get whyRegisterMomentsTitle =>
      '¿Por qué registrar momentos objetivamente?';

  @override
  String get whyRegisterMomentsContent =>
      'Tus picos y valles emocionales no son solo datos sueltos: son como las coordenadas de tu brújula interna. Registrar tu motivación (qué tan impulsado te sientes) y tu satisfacción (qué tan pleno te sientes) te da un mapa claro para planificar metas, rutinas o tareas que realmente sumen a tu vida.';

  @override
  String get whenNoticeMoreTitle => '¿Cuándo se nota más esto?';

  @override
  String get whenNoticeMoreContent =>
      'Cuando estás en un bajón emocional. En esos momentos, tu cerebro te engaña con pensamientos como:\n• \"Nunca me sale nada bien.\"\n• \"No hay nada que me motive.\"\n\nAunque esto no sea cierto, el sesgo de memoria—esa tendencia a recordar lo negativo o lo más reciente—hace que lo sientas así.\n\n📈 Llevar un registro objetivo de tus momentos altos te da pruebas reales de quién sos cuando estás bien, para que no te pierdas en la niebla cuando estás mal.';

  @override
  String get highMotivationSatisfactionTitle =>
      'Alta motivación y alta satisfacción';

  @override
  String get highMotivationSatisfactionContent =>
      'Pregunta rápida: ¿Qué fue lo más significativo que te pasó este mes?\n\nSi tuvieras un registro diario de tus emociones, ¿responderías lo mismo? Probablemente no. Y hay una razón:\n\n• Sesgo de memoria: Según Kahneman y Tversky (1979), nuestro cerebro tiende a darle más peso a lo reciente o lo intenso (esto se llama la regla del pico-final). Por eso, a veces olvidamos momentos valiosos que no fueron tan \"ruidosos\".\n• Solución: Anotar tus picos compensa esta trampa mental y te muestra el panorama completo.';

  @override
  String get evolutionaryViewTitle => 'Mirada evolutiva';

  @override
  String get evolutionaryViewContent =>
      'Las emociones positivas fuertes—like orgullo o plenitud—son señales de oportunidades: una relación que funciona, un logro personal o una decisión que te alinea con lo que querés. Nuestros antepasados sobrevivían mejor si recordaban estos momentos para repetirlos. Tu cerebro no está diseñado solo para hacerte feliz, sino para ayudarte a prosperar. Si sabés qué te motiva, podés buscarlo más seguido.';

  @override
  String get practiceEstimatTitle => 'Práctica con ESTIMAT:';

  @override
  String get practiceEstimatContent =>
      'Imaginá que anotás:\n\n✍️ \"Me sentí poderoso explicando mis emociones en un taller sin que me juzgaran.\"\n\n🔁 Al revisar varias entradas parecidas, ESTIMAT te muestra:\n• Alta motivación cuando expresás lo que sentís.\n• Alta satisfacción con escucha activa y validación.\n\n🧭 ¿Qué hacés con esto?\n• Buscá o creá más situaciones así (talleres, charlas con amigos comprensivos).\n• Usalo como ancla cuando te sentís perdido.';

  @override
  String get highMotivationLowSatisfactionTitle =>
      'Alta Motivación + Baja Satisfacción';

  @override
  String get highMotivationLowSatisfactionContent =>
      '¿Te pasó estar re entusiasmado por algo y después sentir un \'meh\'? Según Gilbert y Wilson, solemos sobreestimar la felicidad futura en un 40-60%.';

  @override
  String get lowMotivationHighSatisfactionTitle =>
      'Baja Motivación + Alta Satisfacción';

  @override
  String get lowMotivationHighSatisfactionContent =>
      '¿Alguna vez te arrastraste a hacer algo y terminaste sorprendiéndote con lo bien que te sentiste? La Universidad de British Columbia mostró que la gente subestima su disfrute del ejercicio en un 20-30%.';

  @override
  String get lowMotivationLowSatisfactionTitle =>
      'Baja Motivación + Baja Satisfacción';

  @override
  String get lowMotivationLowSatisfactionContent =>
      '¿Y esos días en que no hay ganas ni placer? Puede que esos bajones sean semilleros de tus mejores ideas. Los estudiantes que practicaban autoevaluación reflexiva mostraron un aumento del 20% en su rendimiento.';

  @override
  String get evolutiveFunctionsHeader => 'Funciones Evolutivas';

  @override
  String get valueDescriptionGuardian =>
      'Reconocer: Reconoces, consumes, ingieres.\nAlmacenar: Descansas, almacenas y metabolizas.';

  @override
  String get valueDescriptionWarrior =>
      'Descartar: Descartas, huyes, inhibes.\nEjecutar: Luchas, atacas y te esfuerzas.';

  @override
  String get valueDescriptionVersatile =>
      'Observarse: Destacas información negativa, sientes dolor, observas errores.\nTransformarse: Reduces tus errores y adaptas estímulos.';

  @override
  String get valueDescriptionFunny =>
      'Motivarse: Destacas información positiva, sientes placer.\nDisfrutarse: Mejora los éxitos y contrasta actitudes e ideas.';

  @override
  String get valueDescriptionStrategist =>
      'Analizar: Revisas tendencias, preguntas si algo podría ser falso, analizas.\nPredecir: Predices lo que es más probable y creas hipótesis.';

  @override
  String get valueDescriptionTactician =>
      'Simplificar: Generalizas, comparas la forma más fácil y rápida.\nRastrear: Buscas, cazas y rastreas.';

  @override
  String get valueDescriptionAltruistic =>
      'Empatizar: Empatizas con lo que es importante para los demás.\nDeliberar: Me considero a mí mismo y las necesidades de la mayor cantidad de personas posible, practicando el altruismo eficiente.';

  @override
  String get valueDescriptionCollaborator =>
      'Negociar: Ayudas a comprender, comunicar.\nColaborar: Cooperas hacia objetivos compartidos.';

  @override
  String get latentValuesTitle => 'Valores Latentes';

  @override
  String get lifePossibilitiesChartTitle => 'Gráfico de Posibilidades de Vida';

  @override
  String get periodLabel => 'Período:';

  @override
  String get last7DaysFilter => 'Últimos 7 Días';

  @override
  String get customRangeFilter => 'Rango Personalizado';

  @override
  String get customDateRangeFilterTitle =>
      'Filtro de Rango de Fechas Personalizado';

  @override
  String get startDateLabel => 'Fecha de Inicio:';

  @override
  String get endDateLabel => 'Fecha de Fin:';

  @override
  String get selectStartDateHint => 'Seleccionar fecha de inicio';

  @override
  String get selectEndDateHint => 'Seleccionar fecha de fin';

  @override
  String get resetButton => 'Restablecer';

  @override
  String get customRangeActiveLabel => 'Rango personalizado activo';

  @override
  String get dontShowAgainLabel => 'Don\'t show this again';

  @override
  String get userInstructionsMenuTitle => 'User Instructions';

  @override
  String get userInstructionsScreenTitle => 'How to Use ESTIMAT KeyMoments';

  @override
  String get instructionsWelcomeTitle =>
      'Welcome to Your Personal Growth Journey';

  @override
  String get instructionsWelcomeContent =>
      'ESTIMAT KeyMoments helps you understand your personal values and decision patterns by analyzing your most significant life experiences. This guide will show you how to use the app effectively.';

  @override
  String get instructionsStep1Title =>
      'Step 1: Start with Your Extreme Moments';

  @override
  String get instructionsStep1Content =>
      'Begin by identifying your highest and lowest experiences - these are your reference points that establish your personal limits and provide the foundation for better recommendations.\n\n• **Highest moments**: Times when you felt most motivated and satisfied\n• **Lowest moments**: Times when you felt least motivated and satisfied\n\nThese extremes help the app understand your personal range and provide more accurate insights.';

  @override
  String get instructionsStep2Title => 'Step 2: Navigate the App Interface';

  @override
  String get instructionsStep2Content =>
      '**Main Screen**: Record new moments by selecting the type (improves/worsens motivation or satisfaction)\n\n**Moment Details**: Describe your experience and distribute percentages across four levels:\n• Elemental (physical responses)\n• Personal (individual experiences)\n• Informational (intellectual processes)\n• Social (interpersonal connections)\n\n**Summary Screen**: View your patterns and latent values analysis\n\n**Menu**: Access presentations, support, and additional features';

  @override
  String get instructionsStep3Title => 'Step 3: Record Moments Consistently';

  @override
  String get instructionsStep3Content =>
      'For best results:\n\n• Record moments when they\'re fresh in your memory\n• Be honest about the impact on your motivation and satisfaction\n• Provide specific evidence for your percentage distributions\n• Include both positive and negative experiences\n• Aim to record at least one moment per week';

  @override
  String get instructionsStep4Title => 'Step 4: Understand Your Latent Values';

  @override
  String get instructionsStep4Content =>
      'The app identifies eight latent values based on your moment patterns:\n\n**Elemental Level**: Guardian, Warrior\n**Personal Level**: Versatile, Fun\n**Informational Level**: Strategist, Tactical\n**Social Level**: Altruist, Collaborator\n\nThese values represent your core motivational drivers and help you understand what truly matters to you.';

  @override
  String get instructionsFutureTitle => 'Future AI Integration';

  @override
  String get instructionsFutureContent =>
      'ESTIMAT will continuously improve with Large Language Model (LLM) integration to provide:\n\n• **Personalized insights** based on your unique patterns\n• **Contextual recommendations** for decision-making\n• **Predictive analysis** of how choices might affect your well-being\n• **Adaptive questioning** to help you explore your values more deeply\n\nThe more you use the app, the better it becomes at understanding and supporting your personal growth journey.';

  @override
  String get instructionsTipsTitle => 'Pro Tips for Success';

  @override
  String get instructionsTipsContent =>
      '• **Start with extremes**: Your highest and lowest moments provide the most valuable reference points\n• **Be specific**: Detailed descriptions lead to better insights\n• **Review regularly**: Check your summary screen weekly to spot patterns\n• **Trust the process**: Patterns become clearer with more data\n• **Use for decisions**: Apply your latent values insights to future choices';

  @override
  String showingMomentsLabel(int count, String plural) {
    return 'Mostrando $count momento$plural';
  }

  @override
  String get whyFocusOnMomentsTitle => '¿Por qué Enfocarse en Momentos?';

  @override
  String get whyFocusOnMomentsContent =>
      'Los momentos clave son los bloques de construcción de nuestro proceso de toma de decisiones. Al entender estos momentos, podemos:\n\n• Identificar patrones en nuestro comportamiento\n• Entender qué nos motiva realmente\n• Reconocer qué nos trae satisfacción\n• Tomar decisiones más conscientes\n• Desarrollar mejor autoconciencia';

  @override
  String get discoveringLatentValuesTitle => 'Descubriendo Valores Latentes';

  @override
  String discoveringLatentValuesContent(
    String guardianLabel,
    String warriorLabel,
    String versatileLabel,
    String funLabel,
  ) {
    return 'Tus valores latentes emergen de la combinación de:\n\n• **Porcentajes de nivel** (cómo distribuyes entre los cuatro niveles)\n• **Enfoque direccional** (orientación interna vs externa)\n• **Tipo de impacto** (mejora vs empeora)\n\nEstos valores revelan tus fortalezas centrales: $guardianLabel, $warriorLabel, $versatileLabel, $funLabel, y otros.';
  }

  @override
  String get understandingLatentValuesTitle => 'Comprendiendo Valores Latentes';

  @override
  String get understandingLatentValuesContent =>
      'Tus valores latentes emergen de cómo distribuyes tu enfoque entre los cuatro niveles jerárquicos y si tiendes hacia una orientación interna o externa. Cada combinación revela fortalezas centrales y tendencias naturales que guían tu toma de decisiones y satisfacción en la vida.';

  @override
  String get directionsFocusTitle => 'Enfoque de Direcciones';

  @override
  String get whyHierarchicalOrganizationTitle =>
      'Por qué la Organización Jerárquica de Momentos de Vida Podría Importar';

  @override
  String get dataVisualizationBiasReductionTitle =>
      'Visualización de Datos y Reducción de Sesgos';

  @override
  String get experimentalEvidenceTitle => 'Evidencia Experimental';

  @override
  String get evolutionaryPsychologyPerspectiveTitle =>
      'Perspectiva de Psicología Evolutiva';

  @override
  String get recognizeYourMomentOrientationLevel =>
      'Reconoce tu momento, tu orientación y nivel';

  @override
  String get recognizeYourMoment => 'Reconoce tu momento';

  @override
  String get organizeTheMomentAndLookForEvidence =>
      'Ahora veamos la distribución de información en el momento y busquemos evidencias.';

  @override
  String get orientationTitle => 'Orientación';

  @override
  String get orientationQuestion =>
      '¿Tu momento estuvo más enfocado en cambiar internamente o externamente?';

  @override
  String get threeLevelsTitle => 'Niveles';

  @override
  String get threeLevelsDescription =>
      'Análisis jerárquico de tu momento a través de los cuatro niveles de experiencia humana';

  @override
  String get pieStepElementalPersonalTitle =>
      '3 Niveles: Elemental vs Personal';

  @override
  String get pieStepElementalPersonalDescription =>
      'Para tu momento, ¿enfocaste más en tu estado corporal o en tus intereses/emociones personales?';

  @override
  String get pieStepPersonalInformationalTitle =>
      '3 Niveles: Personal vs Informacional';

  @override
  String get pieStepPersonalInformationalDescription =>
      'Para tu momento, ¿enfocaste más en tus intereses/emociones personales o en recopilar y procesar información?';

  @override
  String get pieStepInformationalSocialTitle =>
      '3 Niveles: Informacional vs Social';

  @override
  String get pieStepInformationalSocialDescription =>
      'Para tu momento, ¿enfocaste más en analizar información o en conectar con otros?';

  @override
  String get pieStepEvidenceQuestion =>
      '¿Qué evidencia o variables influyeron en tu elección?';

  @override
  String get whyMomentsHeaderSubtitle =>
      'Analiza tus momentos, conoce tus patrones';

  @override
  String get whyRegisterMomentsObjectivelyTitle =>
      '¿Por qué registrar momentos objetivamente?';

  @override
  String get whyRegisterMomentsObjectivelyContent =>
      'Tus picos y valles emocionales no son solo datos sueltos: son como las coordenadas de tu brújula interna. Registrar tu motivación (qué tan impulsado te sentís) y tu satisfacción (qué tan pleno te sentís) te da un mapa claro para planificar metas, rutinas o tareas que realmente sumen a tu vida.';

  @override
  String get whyRegisterMomentsObjectivelyHighlight =>
      '📈 Llevar un registro de tus picos de motivación y satisfacción sirve para compensar la distorsión emocional. Te permite tener evidencia de quién sos cuando estás bien, para no perderte de vista cuando estás mal.';

  @override
  String get highMotivationHighSatisfactionTitle =>
      'Alta Motivación + Alta Satisfacción';

  @override
  String get researchInfoBoxTitle => '🧠 Investigación';

  @override
  String get researchInfoBoxContent =>
      'Según Kahneman y Tversky (1979), nuestro cerebro tiende a darle más peso a lo reciente o lo intenso (regla del pico-final). Por eso, a veces olvidamos momentos valiosos que no fueron tan \"ruidosos\".';

  @override
  String get evolutionaryViewInfoBoxTitle => '🔬 Mirada Evolutiva';

  @override
  String get evolutionaryViewInfoBoxContent =>
      'Las emociones positivas fuertes señalan oportunidades adaptativas: relaciones exitosas, decisiones alineadas al propósito, logros sociales o personales.';

  @override
  String get practiceEstimatInfoBoxTitle => '✍️ Práctica con ESTIMAT';

  @override
  String get practiceEstimatInfoBoxContent =>
      'Al revisar varias entradas, ESTIMAT te muestra patrones y te ayuda a replicar esos momentos de alta motivación y satisfacción.';

  @override
  String get highMotivationLowSatisfactionIntro =>
      '¿Te pasó estar re entusiasmado por algo y después sentir un \"meh\"? 📱';

  @override
  String get impactBiasInfoBoxTitle => '📊 Impact Bias';

  @override
  String get impactBiasInfoBoxContent =>
      'Según Gilbert y Wilson, solemos sobreestimar la felicidad futura en un';

  @override
  String get practiceInfoBoxTitle => '🎯 Práctica';

  @override
  String get practiceInfoBoxContent =>
      '• Simulá: Antes de lanzarte, imaginá colores, olores, sonidos\n• Predicción vs. realidad: Anotá cuánto creés que vas a disfrutar\n• Ajustá: Si esperabas +3 y fue +1, repensá si vale la pena repetir';

  @override
  String get lowMotivationHighSatisfactionIntro =>
      '¿Alguna vez te arrastraste a hacer algo y terminaste sorprendiéndote con lo bien que te sentiste? ⛈️😊';

  @override
  String get pleasureUnderestimationInfoBoxTitle =>
      '🏃‍♂️ Subestimación del Placer';

  @override
  String get pleasureUnderestimationInfoBoxContent =>
      'La Universidad de British Columbia mostró que la gente subestima su disfrute del ejercicio en un';

  @override
  String get effortParadoxInfoBoxTitle => '🧬 Paradoja del Esfuerzo';

  @override
  String get effortParadoxInfoBoxContent =>
      'Cada gota de esfuerzo se canjea por un plus extra de satisfacción. Ese \"golpe de alegría\" es tu cerebro diciendo \"¡Bien hecho!\"';

  @override
  String get lowMotivationLowSatisfactionIntro =>
      '¿Y esos días en que no hay ganas ni placer? 😔 Puede que esos bajones sean semilleros de tus mejores ideas.';

  @override
  String get reflectionPowerInfoBoxTitle => '📈 Poder de la Reflexión';

  @override
  String get reflectionPowerInfoBoxContent =>
      'Los estudiantes que practicaban autoevaluación reflexiva mostraron un aumento del';

  @override
  String get practiceEstimatLowInfoBoxTitle => '🎯 Práctica con ESTIMAT';

  @override
  String get practiceEstimatLowInfoBoxContent =>
      '• Registrá tu bajón: Escribí sin filtros cómo te sentís\n• Revisá tu bitácora: ESTIMAT te mostrará patrones\n• Hacé algo pequeño: Un paseo, una canción, tres agradecimientos';

  @override
  String get generalOverviewTitle => 'El Panorama General';

  @override
  String get generalOverviewIntro =>
      'Registrar tus emociones no es solo un pasatiempo—es una herramienta para conocerte y sacarle jugo a tu cerebro.';

  @override
  String get memoryBiasStatTitle => '🧠 Sesgo de memoria';

  @override
  String get memoryBiasStatContent => 'Sobrevaloramos lo reciente o intenso';

  @override
  String get impactBiasStatTitle => '🎯 Impact bias';

  @override
  String get impactBiasStatContent => 'Sobreestimamos la felicidad futura';

  @override
  String get underestimationStatTitle => '💪 Subestimación';

  @override
  String get underestimationStatContent =>
      'Disfrutamos el ejercicio más de lo que creemos';

  @override
  String get recoveryStatTitle => '🔄 Recuperación';

  @override
  String get recoveryStatContent => 'Reflexionar te da más días motivados';

  @override
  String get generalOverviewConclusion =>
      '¿Qué pequeño paso podés dar hoy para entender mejor tus emociones? Probá anotar un pico y un valle—los resultados podrían sorprenderte. ✨';

  @override
  String get whyHierarchicalHeaderSubtitle =>
      'Por qué la Organización Jerárquica de Momentos de Vida Podría Importar';

  @override
  String get whyHierarchicalImportantNote =>
      'Importante: Si ya estás experimentando bienestar constante y satisfacción profunda, la organización adicional probablemente sea innecesaria. Este enfoque parece más relevante durante transiciones, decisiones complejas o insatisfacción persistente.';

  @override
  String get informationTheoryPerspectiveTitle =>
      'Perspectiva de Teoría de la Información';

  @override
  String get debunkingCommonMythsTitle => 'Desmintiendo Mitos Comunes';

  @override
  String get selfPerceptionBiasesTitle =>
      'El Problema de los Sesgos de Autopercepción';

  @override
  String get visualProportionsTitle => 'Ventajas de las Proporciones Visuales';

  @override
  String get statsVsIntuitionTitle => 'Estadísticas Personales vs. Intuición';

  @override
  String get memoryHierarchyTitle => 'Experimentos de Jerarquía de Memoria';

  @override
  String get decisionFatigueTitle => 'Estudios de Fatiga de Decisión';

  @override
  String get millerNumberTitle => 'El Número Mágico de Miller';

  @override
  String get availabilityBiasContent =>
      'Sesgo de Disponibilidad (Tversky & Kahneman, 1973): Es probable que recordemos desproporcionalmente eventos recientes o emocionales, lo que puede distorsionar nuestra percepción de los patrones de vida.';

  @override
  String get overestimationLabel => 'Sobreestimación';

  @override
  String get overestimationSublabel => 'de patrones recientes vs. reales';

  @override
  String get decisionDistortionLabel => 'Distorsión de Decisión';

  @override
  String get decisionDistortionSublabel => 'de decisiones basadas en memoria';

  @override
  String get hierarchicalVisualizationNote =>
      'La visualización jerárquica de momentos puede neutralizar este sesgo proporcionando una representación más objetiva de los patrones temporales.';

  @override
  String get clevelandMcGillContent =>
      'Cleveland & McGill (1984): La percepción visual de proporciones parece ser significativamente más precisa que las memorias narrativas para evaluar distribuciones temporales.';

  @override
  String get potentialPersonalApplicationsTitle =>
      'Aplicaciones Personales Potenciales:';

  @override
  String get personalApplicationsList =>
      '• Distribución real de tiempo vs. percepción\n• Patrones de energía y estados emocionales\n• Frecuencia de diferentes tipos de experiencia\n• Progreso hacia objetivos de largo plazo';

  @override
  String get visualizationDiscrepanciesNote =>
      'Estas visualizaciones pueden revelar discrepancias entre percepción subjetiva y realidad objetiva, facilitando decisiones más informadas.';

  @override
  String get personalIntuitionParadoxContent =>
      'Paradoja de la Intuición Personal: Aunque confiamos en nuestra intuición para decisiones personales, es probable que apliquemos análisis estadístico riguroso para decisiones profesionales o financieras.';

  @override
  String get financialDecisionsLabel => 'Decisiones Financieras';

  @override
  String get financialDecisionsSublabel => 'usan datos objetivos';

  @override
  String get personalDecisionsLabel => 'Decisiones Personales';

  @override
  String get personalDecisionsSublabel => 'usan datos objetivos';

  @override
  String get potentialImprovementLabel => 'Mejora Potencial';

  @override
  String get potentialImprovementSublabel => 'con organización sistemática';

  @override
  String get hierarchicalAnalyticalNote =>
      'La organización jerárquica puede permitir aplicar rigor analítico a las decisiones de vida manteniendo flexibilidad emocional e intuitiva.';

  @override
  String get socialLevelTitle => '4. Social';

  @override
  String get informationalLevelTitle => '3. Informacional';

  @override
  String get personalLevelTitle => '2. Personal';

  @override
  String get elementalLevelTitle => '1. Elemental';

  @override
  String get collaborateFunction => 'Colaborar';

  @override
  String get negotiateFunction => 'Negociar';

  @override
  String get collaboratorValue => 'COLABORADOR';

  @override
  String get diplomatSubtitle => 'Diplomático';

  @override
  String get deliberateFunction => 'Deliberar';

  @override
  String get empathizeFunction => 'Empatizar';

  @override
  String get altruisticValue => 'ALTRUISTA';

  @override
  String get empatheticSubtitle => 'Empático';

  @override
  String get predictFunction => 'Predecir';

  @override
  String get analyzeFunction => 'Analizar';

  @override
  String get strategistValue => 'ESTRATEGA';

  @override
  String get analystSubtitle => 'Analista';

  @override
  String get trackFunction => 'Rastrear';

  @override
  String get simplifyFunction => 'Simplificar';

  @override
  String get tacticianValue => 'TÁCTICO';

  @override
  String get synthesizerSubtitle => 'Sintetizador';

  @override
  String get selfEnjoyFunction => 'Auto-Disfrutar';

  @override
  String get selfMotivateFunction => 'Auto-Motivar';

  @override
  String get funnyValue => 'DIVERTIDO';

  @override
  String get enthusiasticSubtitle => 'Entusiasta';

  @override
  String get selfTransformFunction => 'Auto-Transformar';

  @override
  String get selfObserveFunction => 'Auto-Observar';

  @override
  String get versatileValue => 'VERSÁTIL';

  @override
  String get selfSeerSubtitle => 'Auto-Observador';

  @override
  String get executeFunction => 'Ejecutar';

  @override
  String get discardFunction => 'Descartar';

  @override
  String get warriorValue => 'GUERRERO';

  @override
  String get releaserSubtitle => 'Liberador';

  @override
  String get storeFunction => 'Almacenar';

  @override
  String get recognizeFunction => 'Reconocer';

  @override
  String get guardianValue => 'GUARDIÁN';

  @override
  String get nurturerSubtitle => 'Cuidador';

  @override
  String get memoryHierarchyContent =>
      'Bower et al. (1969): La organización jerárquica puede mejorar la recordación en aproximadamente 200% comparado con la presentación aleatoria.';

  @override
  String get baselineLabel => 'Línea Base';

  @override
  String get randomPresentationSublabel => 'capacidad de recordación';

  @override
  String get hierarchicalOrganizationLabel => 'Organización Jerárquica';

  @override
  String get hierarchicalImprovementSublabel => 'mejora en la recordación';

  @override
  String get brainProcessesHierarchically =>
      'Implicación probable: Tu cerebro probablemente procesa información jerárquicamente por naturaleza. Luchar contra esta estructura posiblemente desperdicia recursos cognitivos.';

  @override
  String get decisionFatigueContent =>
      'Baumeister et al. (1998): Después de decisiones no estructuradas repetidas, la calidad de las decisiones probablemente declina significativamente.';

  @override
  String get evolutionaryPerspectiveTitle => 'Perspectiva Evolutiva:';

  @override
  String get ancestorsDecisionsContent =>
      'Nuestros ancestros probablemente enfrentaban decisiones diarias limitadas en jerarquías sociales estructuradas. El caos moderno de opciones desorganizadas puede exceder nuestra capacidad cognitiva.';

  @override
  String get preOrganizedStructures =>
      'Estructuras jerárquicas pre-organizadas podrían mantener la calidad de las decisiones incluso bajo carga cognitiva.';

  @override
  String get millerNumberContent =>
      'Miller (1956): Los humanos pueden posiblemente mantener 7±2 elementos no relacionados en la memoria de trabajo, pero probablemente pueden procesar 7±2 categorías, cada una conteniendo 7±2 subcategorías.';

  @override
  String get individualItemsLabel => 'Elementos Individuales';

  @override
  String get workingMemoryLimitSublabel => 'límite de la memoria de trabajo';

  @override
  String get hierarchicalCapacityLabel => 'Capacidad Jerárquica';

  @override
  String get organizedElementsSublabel => 'elementos organizados';

  @override
  String get exponentialProcessingCapacity =>
      'Esto podría crear capacidad de procesamiento exponencial a través de la jerarquía, liberando recursos mentales para reconocimiento de patrones y planificación futura.';

  @override
  String get ancestralMismatchContent =>
      'Los humanos modernos posiblemente enfrentan aproximadamente 35,000 decisiones diarias, mientras que nuestros ancestros probablemente encontraban 70-100 decisiones estructuradas en jerarquías sociales predecibles.';

  @override
  String get ancestralDecisionsLabel => 'Decisiones Ancestrales';

  @override
  String get structuredPerDaySublabel => 'estructuradas/día';

  @override
  String get modernDecisionsLabel => 'Decisiones Modernas';

  @override
  String get unstructuredPerDaySublabel => 'no estructuradas/día';

  @override
  String get schwartzOptionsContent =>
      'Schwartz (2004): Más de 8-10 opciones no estructuradas pueden disminuir la satisfacción en 25% y la calidad de las decisiones en 15%.';

  @override
  String get foragingEfficiencyContent =>
      'Stephens & Krebs (1986): Los animales que organizaron el comportamiento de búsqueda jerárquicamente (territorio → parches → recursos específicos) probablemente mostraron 40-60% mejor eficiencia energética.';

  @override
  String get energyEfficiencyLabel => 'Eficiencia Energética';

  @override
  String get hierarchicalOrganizationSublabel => 'organización jerárquica';

  @override
  String get goalAchievementLabel => 'Logro de Objetivos';

  @override
  String get structuredFrameworksSublabel => 'marcos estructurados';

  @override
  String get gigerenzerFrameworksContent =>
      'Gigerenzer (2007): Las personas que usan marcos de decisión jerárquicos posiblemente logran objetivos 35% más rápido con 50% menos esfuerzo.';

  @override
  String get compressionAdvantageContent =>
      'Shannon (1948): La organización jerárquica probablemente logra compresión de datos óptima. Aplicado a experiencias de vida, esto podría permitir procesar exponencialmente más información.';

  @override
  String get applicationToMomentsTitle => 'Aplicación a Momentos:';

  @override
  String get compressionMomentsContent =>
      'En lugar de recordar cientos de experiencias desconectadas, la organización jerárquica posiblemente permite comprimir momentos similares en categorías, liberando recursos mentales para reconocimiento de patrones y planificación futura.';

  @override
  String get predictionMachineContent =>
      'Clark (2013): El cerebro posiblemente opera como una \"máquina de predicción\", generando constantemente modelos de experiencias futuras basados en patrones pasados.';

  @override
  String get neuralReductionLabel => 'Reducción Neural';

  @override
  String get predictableExperiencesSublabel => 'experiencias predecibles';

  @override
  String get unpredictedActivityLabel => 'Actividad Impredecible';

  @override
  String get neuralActivitySublabel => 'actividad neural';

  @override
  String get organizedMomentTracking =>
      'El seguimiento organizado de momentos probablemente crea mejores modelos predictivos, reduciendo la carga cognitiva y mejorando la precisión de la toma de decisiones futuras.';

  @override
  String get entropyReductionContent =>
      'Bialek et al. (2001): Las redes neuronales que usan procesamiento jerárquico posiblemente logran eficiencia superior en la transmisión de información comparado con estructuras planas.';

  @override
  String get lifeApplicationEntropy =>
      'Aplicación a la vida: La organización jerárquica de momentos probablemente permite extraer máximo conocimiento de las experiencias mientras minimiza el ruido cognitivo.';

  @override
  String get creativityMythCounterTitle => 'Contra-evidencia:';

  @override
  String get creativityMythCounterContent =>
      '• Stokes (2005): Los profesionales creativos con marcos organizacionales posiblemente producen trabajo más innovador\n• Schwartz (2004): Demasiadas opciones no estructuradas probablemente disminuyen la producción creativa\n• La organización jerárquica probablemente reduce el ruido cognitivo, liberando recursos mentales para el pensamiento creativo';

  @override
  String get successMythCounterContent =>
      '• Ericsson (2016): Los ejecutantes de élite en todos los dominios probablemente usan sistemas de práctica y reflexión altamente estructurados\n• Los individuos de alto rendimiento probablemente muestran habilidades organizacionales superiores, no menos estructura\n• Las sociedades cazadoras-recolectoras exitosas probablemente tenían sistemas de organización jerárquica complejos';

  @override
  String get hierarchyMythCounterContent =>
      '• Todas las sociedades de primates exitosas probablemente exhiben organización jerárquica con roles claros\n• El cerebro humano probablemente evolucionó el procesamiento jerárquico como su arquitectura fundamental\n• Incluso las sociedades igualitarias posiblemente mantienen organización jerárquica para diferentes dominios';

  @override
  String get simplicityMythCounterContent =>
      '• La complejidad apropiada probablemente coincide con las demandas ambientales\n• La sobresimplificación posiblemente lleva al fallo del sistema\n• La complejidad bien estructurada probablemente reduce la carga cognitiva\n• La organización jerárquica probablemente logra un equilibrio óptimo entre simplicidad y riqueza de información';

  @override
  String get anxietyMythCounterContent =>
      '• La incertidumbre no estructurada probablemente genera más ansiedad que la complejidad organizada\n• Los marcos claros posiblemente reducen la ansiedad de decisión\n• La organización jerárquica probablemente proporciona estructura predictiva que calma el sistema nervioso\n• Los estudios sugieren que la claridad organizacional reduce las hormonas del estrés';

  @override
  String get fullPresentationMethodologyTitle => 'La Metodología';

  @override
  String get fullPresentationMethodologyContent =>
      'Nuestro enfoque combina insights psicológicos con análisis práctico:\n\n1. **Identificación de Momentos**: Reconoce momentos clave en tu vida\n2. **Análisis Dimensional**: Categoriza a través de cuatro niveles\n3. **Enfoque Direccional**: Comprende orientación interna vs externa\n4. **Evaluación de Impacto**: Evalúa efectos de motivación y satisfacción\n5. **Reconocimiento de Patrones**: Descubre tus valores latentes';

  @override
  String get fullPresentationFourLevelsTitle =>
      'Cuatro Niveles de Experiencia Humana';

  @override
  String get fullPresentationFourLevelsContent =>
      'Estos niveles funcionan jerárquicamente, construyendo unos sobre otros para crear tu experiencia completa.';

  @override
  String get fullPresentationDirectionalFocusTitle => 'Enfoque Direccional';

  @override
  String get fullPresentationDirectionalFocusContent =>
      'Todo momento tiene componentes internos y externos, pero una dirección típicamente domina. Entender esto ayuda a revelar tus tendencias y preferencias naturales.';

  @override
  String get fullPresentationPracticalApplicationTitle => 'Aplicación Práctica';

  @override
  String get fullPresentationPracticalApplicationContent =>
      'Usa esta metodología para:\n\n• **Rastrear patrones** en tu toma de decisiones\n• **Identificar disparadores** para motivación y satisfacción\n• **Entender conflictos** entre diferentes aspectos de ti mismo\n• **Tomar mejores decisiones** alineadas con tus valores\n• **Desarrollar estrategias** para crecimiento personal';

  @override
  String get fullPresentationConclusionTitle => 'Tu Viaje Comienza';

  @override
  String get fullPresentationConclusionContent =>
      'Ahora que entiendes la metodología, puedes:\n\n• Comenzar a registrar tus momentos clave\n• Analizar tus patrones a lo largo del tiempo\n• Descubrir tus valores latentes únicos\n• Usar insights para desarrollo personal\n\nRecuerda: La autoconciencia es el primer paso hacia la vida intencional.';

  @override
  String get fullPresentationElementalDescription =>
      'Respuestas físicas e instintivas';

  @override
  String get fullPresentationPersonalDescription =>
      'Experiencias y emociones individuales';

  @override
  String get fullPresentationInformationalDescription =>
      'Procesos intelectuales y analíticos';

  @override
  String get fullPresentationSocialDescription =>
      'Conexiones interpersonales y culturales';

  @override
  String get ancestralMismatchTitle => 'Desajuste del Entorno Ancestral';

  @override
  String get socialHierarchyTitle => 'Hipótesis de Jerarquía Social';

  @override
  String get foragingEfficiencyTitle => 'Modelo de Eficiencia de Búsqueda';

  @override
  String get compressionAdvantageTitle => 'La Ventaja de la Compresión';

  @override
  String get predictionMachineTitle => 'El Cerebro como Máquina de Predicción';

  @override
  String get entropyReductionTitle => 'Principio de Reducción de Entropía';

  @override
  String get creativityMythTitle =>
      'Mito: \'La organización mata la creatividad y la espontaneidad\'';

  @override
  String get successMythTitle =>
      'Mito: \'Las personas exitosas no necesitan sistemas—simplemente improvisan\'';

  @override
  String get hierarchyMythTitle =>
      'Mito: \'La jerarquía es antinatural y opresiva\'';

  @override
  String get simplicityMythTitle =>
      'Mito: \'Lo simple siempre es mejor que lo complejo\'';

  @override
  String get anxietyMythTitle =>
      'Mito: \'La organización es solo para personas ansiosas o controladoras\'';

  @override
  String get socialHierarchyContent =>
      'Sapolsky (2017): Los humanos y otros primates probablemente muestran los niveles más bajos de hormonas del estrés cuando su posición social está claramente definida, es predecible y está controlada internamente.';

  @override
  String get stressReductionElementsTitle =>
      'Elementos Clave para la Reducción del Estrés:';

  @override
  String get stressReductionElementsList =>
      '• Claridad jerárquica: Posición definida\n• Predictibilidad: Reglas consistentes\n• Control interno: Agencia dentro de la estructura';

  @override
  String get randomPresentationLabel => 'Presentación Aleatoria';

  @override
  String get hierarchicalOrganizationBenefits =>
      'La organización jerárquica de la vida puede imitar estructuras sociales ancestrales exitosas, probablemente reduciendo el estrés y mejorando la toma de decisiones.';

  @override
  String get fourLevelsProcessTitle => 'Proceso de Cuatro Niveles';

  @override
  String get hierarchicalStructureTitle => 'Estructura Jerárquica';

  @override
  String get hierarchicalStructureDescription =>
      'Las cuatro dimensiones siguen una estructura jerárquica:';

  @override
  String get levelsEquationText =>
      'Elemental + Personal + Informacional + Social = 100%';

  @override
  String get elementalAllocationText =>
      'Cuando asignas un porcentaje al Elemental, el porcentaje restante se distribuye entre Personal, Informacional y Social.';

  @override
  String get personalAllocationText =>
      'Cuando asignas un porcentaje al Personal, el porcentaje restante se distribuye entre Informacional y Social.';

  @override
  String get informationalAllocationText =>
      'Cuando asignas un porcentaje al Informacional, el porcentaje restante va al Social.';

  @override
  String get directionsExplanationText =>
      'Interior + Exterior = 100%\n\nEstas dimensiones representan tu orientación o enfoque hacia cada momento.';

  @override
  String get pleaseEnterTitle => 'Por favor, ingresa un título';

  @override
  String get titleMustBeTwoWords =>
      'El título debe tener exactamente dos palabras';

  @override
  String get untitledMoment => 'Momento Sin Título';

  @override
  String get whyMomentsTitle => 'Por Qué Momentos';

  @override
  String get whyMomentsSubtitle => 'Analiza tus momentos, conoce tus patrones';

  @override
  String get whyHierarchicalTitle => 'Por Qué Organización Jerárquica';

  @override
  String get whyHierarchicalSubtitle =>
      'Por Qué la Organización Jerárquica de los Momentos de Vida Puede Importar';

  @override
  String get highMotivationHighSatisfactionContent =>
      'Según Kahneman y Tversky (1979), nuestro cerebro tiende a darle más peso a lo reciente o lo intenso (regla del pico-final). Por eso, a veces olvidamos momentos valiosos que no fueron tan \'ruidosos\'.';

  @override
  String get importantNote =>
      'Importante: Si ya estás experimentando bienestar consistente y satisfacción profunda, la organización adicional probablemente es innecesaria. Este enfoque parece más relevante durante transiciones, decisiones complejas o insatisfacción persistente.';

  @override
  String get dataVisualizationTitle =>
      'Visualización de Datos y Reducción de Sesgo';

  @override
  String get dataVisualizationContent =>
      'La visualización jerárquica de momentos podría contrarrestar el sesgo de disponibilidad proporcionando una representación más objetiva de los patrones temporales. La percepción visual de proporciones parece ser significativamente más precisa que las memorias narrativas.';

  @override
  String get experimentalEvidenceContent =>
      'La organización jerárquica podría mejorar el recuerdo en aproximadamente 200% comparado con la presentación aleatoria. Después de decisiones repetidas no estructuradas, la calidad de la decisión probablemente declina significativamente.';

  @override
  String get evolutionaryPsychologyTitle =>
      'Perspectiva de Psicología Evolutiva';

  @override
  String get evolutionaryPsychologyContent =>
      'Los humanos modernos posiblemente enfrentan aproximadamente 35,000 decisiones diarias, mientras que nuestros ancestros probablemente encontraban 70-100 decisiones estructuradas en jerarquías sociales predecibles.';

  @override
  String get informationTheoryTitle =>
      'Perspectiva de Teoría de la Información';

  @override
  String get informationTheoryContent =>
      'La organización jerárquica probablemente logra compresión de datos óptima. Aplicada a las experiencias de vida, esto podría permitir procesar exponencialmente más información.';

  @override
  String get scientificFooter =>
      'Basado en investigación científica • Diseñado para tu crecimiento personal';

  @override
  String get guardianValueDescription =>
      'Reconocer: Reconoces, consumes, ingieres.\\nAlmacenar: Descansas, almacenas y metabolizas.';

  @override
  String get warriorValueDescription =>
      'Descartar: Descartas, huyes, inhibes.\\nEjecutar: Luchas, atacas y te esfuerzas.';

  @override
  String get versatileValueDescription =>
      'Auto-Observar: Destacas información negativa, sientes dolor, observas errores.\\nAuto-Transformar: Reduces tus errores y adaptas estímulos.';

  @override
  String get funValueDescription =>
      'Auto-Motivar: Destacas información positiva, sientes placer.\\nAuto-Disfrutar: Realzas éxitos y contrastas actitudes e ideas.';

  @override
  String get strategistValueDescription =>
      'Analizar: Revisas tendencias, preguntas si algo podría ser falso, analizas.\\nPredecir: Predices lo que es más probable y creas hipótesis.';

  @override
  String get tacticalValueDescription =>
      'Simplificar: Generalizas, comparas la forma más fácil y rápida.\\nRastrear: Buscas, cazas y rastreas.';

  @override
  String get altruistValueDescription =>
      'Empatizar: Empatizas con lo que es importante para otros.\\nDeliberar: Consideras a ti mismo y las necesidades de tantas personas como sea posible, practicando altruismo eficiente.';

  @override
  String get collaboratorValueDescription =>
      'Negociar: Ayudas a entender, comunicar.\\nColaborar: Cooperas hacia objetivos compartidos.';

  @override
  String get functionCollaborate => 'Colaborar';

  @override
  String get functionNegotiate => 'Negociar';

  @override
  String get functionDeliberate => 'Deliberar';

  @override
  String get functionEmpathize => 'Empatizar';

  @override
  String get functionPredict => 'Predecir';

  @override
  String get functionAnalyze => 'Analizar';

  @override
  String get functionTrack => 'Rastrear';

  @override
  String get functionSimplify => 'Simplificar';

  @override
  String get functionSelfEnjoy => 'Auto-Disfrutar';

  @override
  String get functionSelfMotivate => 'Auto-Motivar';

  @override
  String get functionSelfTransform => 'Auto-Transformar';

  @override
  String get functionSelfObserve => 'Auto-Observar';

  @override
  String get functionExecute => 'Ejecutar';

  @override
  String get functionDiscard => 'Descartar';

  @override
  String get functionStore => 'Almacenar';

  @override
  String get functionRecognize => 'Reconocer';

  @override
  String get valueCollaborator => 'Colaborador';

  @override
  String get valueAltruistic => 'Altruista';

  @override
  String get valueStrategist => 'Estratega';

  @override
  String get valueTactician => 'Táctico';

  @override
  String get valueFunny => 'Divertido';

  @override
  String get valueVersatile => 'Versátil';

  @override
  String get valueWarrior => 'Guerrero';

  @override
  String get valueGuardian => 'Guardián';

  @override
  String get subtitleDiplomat => 'Diplomático';

  @override
  String get subtitleEmpathetic => 'Empático';

  @override
  String get subtitleAnalyst => 'Analista';

  @override
  String get subtitleSynthesizer => 'Sintetizador';

  @override
  String get subtitleEnthusiastic => 'Entusiasta';

  @override
  String get subtitleSelfSeer => 'Auto-Observador';

  @override
  String get subtitleReleaser => 'Liberador';

  @override
  String get subtitleNurturer => 'Cuidador';

  @override
  String get descriptionGuardian =>
      'Valor enfocado en preservar, proteger y mantener estabilidad. Representa la capacidad de reconocer y almacenar información importante para seguridad y continuidad.';

  @override
  String get descriptionWarrior =>
      'Valor enfocado en acción, ejecución y superación de obstáculos. Representa la capacidad de ejecutar decisiones y descartar lo que ya no sirve.';

  @override
  String get descriptionVersatile =>
      'Valor enfocado en adaptabilidad y auto-observación. Representa la capacidad de transformarse y observarse a sí mismo para crecimiento personal.';

  @override
  String get descriptionFunny =>
      'Valor enfocado en placer, motivación y energía positiva. Representa la capacidad de auto-disfrutar y auto-motivar para mantener el bienestar.';

  @override
  String get descriptionStrategist =>
      'Valor enfocado en planificación y análisis profundo. Representa la capacidad de predecir escenarios y analizar información compleja.';

  @override
  String get descriptionTactician =>
      'Valor enfocado en síntesis y seguimiento eficiente. Representa la capacidad de simplificar complejidades y rastrear progreso.';

  @override
  String get descriptionAltruistic =>
      'Valor enfocado en empatía y consideración por otros. Representa la capacidad de deliberar y empatizar para el bien común.';

  @override
  String get descriptionCollaborator =>
      'Valor enfocado en cooperación y negociación. Representa la capacidad de colaborar efectivamente y negociar soluciones mutuamente beneficiosas.';
}
