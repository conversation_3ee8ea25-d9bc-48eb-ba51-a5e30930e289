import 'dart:io';
import 'package:flutter/material.dart';

class CSVViewerScreen extends StatefulWidget {
  final String filePath;

  const CSVViewerScreen({
    super.key,
    required this.filePath,
  });

  @override
  State<CSVViewerScreen> createState() => _CSVViewerScreenState();
}

class _CSVViewerScreenState extends State<CSVViewerScreen> {
  List<List<String>> _csvData = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadCSVData();
  }

  Future<void> _loadCSVData() async {
    try {
      final file = File(widget.filePath);
      final contents = await file.readAsString();
      
      final lines = contents.split('\n');
      final data = <List<String>>[];
      
      for (final line in lines) {
        if (line.trim().isNotEmpty) {
          data.add(line.split(','));
        }
      }
      
      setState(() {
        _csvData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading CSV data: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('CSV Data'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : _buildCSVTable(),
    );
  }

  Widget _buildCSVTable() {
    if (_csvData.isEmpty) {
      return const Center(child: Text('No data found in CSV file.'));
    }
    
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          columns: _buildColumns(),
          rows: _buildRows(),
        ),
      ),
    );
  }

  List<DataColumn> _buildColumns() {
    if (_csvData.isEmpty) return [];
    
    final headers = _csvData[0];
    return headers.map((header) => DataColumn(
      label: Text(
        header,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
    )).toList();
  }

  List<DataRow> _buildRows() {
    if (_csvData.length <= 1) return [];
    
    final rows = _csvData.sublist(1);
    return rows.map((row) {
      final cells = <DataCell>[];
      
      // Ensure all rows have the same number of cells as the header
      final headerCount = _csvData[0].length;
      for (var i = 0; i < headerCount; i++) {
        final value = i < row.length ? row[i] : '';
        cells.add(DataCell(Text(value)));
      }
      
      return DataRow(cells: cells);
    }).toList();
  }
}
