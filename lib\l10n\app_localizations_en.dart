// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Estimat KeyMoments';

  @override
  String get selectLanguageTitle => 'Select Language';

  @override
  String get englishLanguage => 'English';

  @override
  String get portugueseLanguage => 'Portuguese';

  @override
  String get spanishLanguage => 'Spanish';

  @override
  String get onboardingScreenTitle => 'Welcome to Estimat KeyMoments';

  @override
  String get onboardingScene1Message =>
      'Noise can be simplified into notes and turned into music';

  @override
  String get onboardingScene1MessagePart2 => 'With a bit of proportion...';

  @override
  String get onboardingScene2Message =>
      'Any image can be replicated through light, shadow, and color, and transformed into art';

  @override
  String get onboardingScene2MessagePart2 => 'With a bit of proportion...';

  @override
  String get onboardingScene3Message =>
      'Your cognitive overload—could you refine what you value most in yourself?';

  @override
  String get onboardingScene3MessagePart2 => 'With a bit of ...';

  @override
  String get onboardingScene4Message =>
      'With information theory, an AI can reduce complex data to 5% and reconstruct it with 95% functional fidelity; these are latent vectors';

  @override
  String get onboardingScene4MessagePart2 =>
      'Maybe you could apply a bit of that to your latent values...';

  @override
  String get onboardingFlowchartMessage =>
      'Let\'s identify our reference moments. Track the flows associated with those moments. Evaluate according to their function. Discover the latent values that drive us:';

  @override
  String get onboardingFlowchartTitle => 'Process Flow';

  @override
  String get nextButton => 'Next';

  @override
  String get previousButton => 'Previous';

  @override
  String get getStartedButton => 'Get Started';

  @override
  String get exitToAppButton => 'Exit to App';

  @override
  String get motivationAppBarTitle => 'Motivation Moments';

  @override
  String get satisfactionAppBarTitle => 'Satisfaction Moments';

  @override
  String get selectMomentTypeTitle => 'Select Moment Type';

  @override
  String get selectImprovementWorsening =>
      'Choose between Improvement or Worsening:';

  @override
  String get selectMotivationSatisfaction =>
      'Choose between Motivation or Satisfaction:';

  @override
  String get improvesLabel => 'Improves';

  @override
  String get worsensLabel => 'Worsens';

  @override
  String get motivationLabel => 'Motivation';

  @override
  String get satisfactionLabel => 'Satisfaction';

  @override
  String get elementalLabel => 'Elemental';

  @override
  String get personalLabel => 'Personal';

  @override
  String get informationalLabel => 'Informational';

  @override
  String get socialLabel => 'Social';

  @override
  String get elementalExplanation =>
      'Based on environmental stimuli, we give a response that was modulated by evolution. More correlated with physical activities: Recognition, consumption, rest, storage, execute, fight, attack, and struggle.';

  @override
  String get personalExplanation =>
      'Adding to the memory that comes from our ancestors, we have a memory that is created from the unique interaction between an individual and their environment. ainful and pleasant. More correlated with: Error observation, adaptation, pain awareness, positive focus, pleasure, success enhancement';

  @override
  String get informationalExplanation =>
      'Upon reaching the intellectual level, in addition to integrating memories and experiences with the environment, we achieve a unique ability: to relate memories or an individual\'s own memories to each other and encode abstract information (example: Trend analysis, hypothesis creation, prediction, Generalization, efficiency, tracking).';

  @override
  String get socialExplanation =>
      'Social relationships involve the connection of information between individuals, the exchange of culture and communication. We can define a boundary between intellectual level and social level when we use our intellectual processes to interact with other beings capable of combining their own memories. To Empathy, consideration of others\' needs, Communication, cooperation, shared goals';

  @override
  String get inwardLabel => 'Inward';

  @override
  String get outwardLabel => 'Outward';

  @override
  String get inwardExplanation =>
      'Reflexive: To store, self-transform, predict, empathize.';

  @override
  String get outwardExplanation =>
      'Intuitive: To execute, enjoy, track, collaborate.';

  @override
  String get titleInputHint => 'Enter a two-word title';

  @override
  String get descriptionInputHint => 'Describe this moment';

  @override
  String get evidenceInputHint => 'Provide evidence for this distribution';

  @override
  String get describeDistributionHint =>
      'Describe why you chose this distribution...';

  @override
  String get firstMomentEvidenceHint =>
      'Explain why this moment creates more possibilities than just being alive. What new opportunities, paths, or potential does it open up?';

  @override
  String get comparisonEvidenceHint =>
      'Explain how many possibilities this moment creates compared to your life baseline...';

  @override
  String get lifePossibilitiesFactorTitle => 'Life Possibilities Factor';

  @override
  String get howManyPossibilitiesTitle =>
      'How Many Possibilities Does This Moment Create';

  @override
  String get comparedToLifeBaseline =>
      'Compared to your life baseline, this moment creates:';

  @override
  String get morePossibilitiesButton => 'More Possibilities';

  @override
  String get fewerPossibilitiesButton => 'Fewer Possibilities';

  @override
  String get vsLifeBaseline => 'vs Life Baseline';

  @override
  String get explainWhyFirstMomentTitle =>
      'Explain Why This Moment Creates More Possibilities Than Life';

  @override
  String get provideEvidenceTitle =>
      'Provide Evidence for this Possibilities Assessment';

  @override
  String get continueButton => 'Continue';

  @override
  String get lifePossibilitiesChart => 'Life Possibilities Chart';

  @override
  String get allTimeFilter => 'All Time';

  @override
  String get lastWeekFilter => 'Last Week';

  @override
  String get lastMonthFilter => 'Last Month';

  @override
  String get last3MonthsFilter => 'Last 3 Months';

  @override
  String get currentPreviewLabel => 'Current Preview:';

  @override
  String get lifeLabel => 'Life';

  @override
  String get previewLabel => 'Preview';

  @override
  String get lifeBaselineLabel => 'Life Baseline (1x)';

  @override
  String get morePossibilitiesLabel => 'More Possibilities';

  @override
  String get fewerPossibilitiesLabel => 'Fewer Possibilities';

  @override
  String get currentPreviewLegend => 'Current Preview';

  @override
  String get lifePossibilitiesExplanation =>
      'Compare this moment to your life baseline (1x). How many times more possibilities does this moment create for you? You can go above or below previous moments, but never below the life baseline.\\n\\nThe factorial calculation represents the exponential growth of possibilities that meaningful moments can create in your life.';

  @override
  String get minimumLifeBaselineNote => 'Minimum: 1.0x (life baseline)';

  @override
  String get guardianLabel => 'Guardian';

  @override
  String get warriorLabel => 'Warrior';

  @override
  String get versatileLabel => 'Versatile';

  @override
  String get funLabel => 'Fun';

  @override
  String get strategistLabel => 'Strategist';

  @override
  String get tacticalLabel => 'Tactical';

  @override
  String get altruistLabel => 'Altruist';

  @override
  String get collaboratorLabel => 'Collaborator';

  @override
  String get summaryTitle => 'Summary';

  @override
  String get motivationSectionTitle => 'Motivation Analysis';

  @override
  String get satisfactionSectionTitle => 'Satisfaction Analysis';

  @override
  String get latentValuesSectionTitle => 'Latent Values';

  @override
  String get improvesMotivationLabel => 'Improves Motivation';

  @override
  String get worsensMotivationLabel => 'Worsens Motivation';

  @override
  String get improvesSatisfactionLabel => 'Improves Satisfaction';

  @override
  String get worsensSatisfactionLabel => 'Worsens Satisfaction';

  @override
  String get proportionLabel => 'Proportion';

  @override
  String get exportDataButton => 'Export Data';

  @override
  String get viewOnboardingMenu => 'Onboarding';

  @override
  String get viewFullPresentationMenu => 'Full Presentation';

  @override
  String get viewLevelProcessFocusMenu => 'Levels and Directions';

  @override
  String get viewLatentValuesMenu => 'Evolutive Functions and Values';

  @override
  String get fromIACodeToHumanValuesMenu => 'From IA code to human values';

  @override
  String get jacoMinestSupportMenu => 'Jacominest Support';

  @override
  String get changeLanguageMenu => 'Change Language';

  @override
  String get supportScreenTitle => 'Support';

  @override
  String get supportMainMessage =>
      'Saving a mind saves more than a thousand cans.';

  @override
  String get supportIntroText =>
      'The odds may not be much in our favor, but saving a mind that may be lost, hopeless, or trapped in destructive cycles can generate exponential returns in the long term.';

  @override
  String get supportEvidenceTitle => 'Evidence';

  @override
  String get supportStatistic1 =>
      'People who participate in educational programs in prison are 43% less likely to return.';

  @override
  String get supportStatistic1Source =>
      'RAND Corporation (2013). \"Evaluating the Effectiveness of Correctional Education\"';

  @override
  String get supportStatistic1Link =>
      'https://www.rand.org/pubs/research_reports/RR266.html';

  @override
  String get supportStatistic2 =>
      'Every dollar invested in prison education can save four to five dollars in re-incarceration costs.';

  @override
  String get supportStatistic2Source =>
      'Davis, L.M. et al. (2013). \"How Effective Is Correctional Education, and Where Do We Go from Here?\"';

  @override
  String get supportStatistic2Link =>
      'https://bja.ojp.gov/sites/g/files/xyckuh186/files/Publications/RAND_Correctional-Education-Meta-Analysis.pdf';

  @override
  String get supportStatistic3 =>
      'Employment after release is 13% higher among those who participated in educational programs...';

  @override
  String get supportStatistic3Source =>
      'Bureau of Justice Statistics, U.S. Department of Justice';

  @override
  String get supportStatistic3Link => 'https://bjs.ojp.gov/topics/corrections';

  @override
  String get supportConsumptionTitle => 'People and Recycling';

  @override
  String get supportConsumptionSubtitle =>
      'Furthermore, a person throughout their lifetime consumes:';

  @override
  String get supportConsumption1 => 'Thousands of kilos of food and packaging';

  @override
  String get supportConsumption2 => 'Tons of water';

  @override
  String get supportConsumption3 =>
      'Electrical energy equivalent to years of household consumption';

  @override
  String get supportConsumption4 =>
      'Construction materials, clothing, technology...';

  @override
  String get expandToSeeMore => 'Tap to see more';

  @override
  String get tapToCollapse => 'Tap to collapse';

  @override
  String get supportAppDescription =>
      'This app was created with years of dedication to improve latent values using modern and mathematical knowledge.';

  @override
  String get supportThanksJacominest =>
      'Thanks to the Jacominesp association for supporting ways to better value the world\'s resources, starting with those who need hope the most.';

  @override
  String get supportThanksWitness =>
      'Thank you for being a witness to their struggle, María Rosa de Siqueira.';

  @override
  String get supportSourcesTitle => 'Sources:';

  @override
  String get supportSource1 =>
      'RAND Corporation (2013). \"Evaluating the Effectiveness of Correctional Education\"';

  @override
  String get supportSource2 =>
      'Davis, L.M. et al. (2013). \"How Effective Is Correctional Education, and Where Do We Go from Here?\"';

  @override
  String get supportSource3 =>
      'Bureau of Justice Statistics, U.S. Department of Justice';

  @override
  String get continueToWebsiteButton => 'Continue to Website';

  @override
  String get coffeeScreenTitle => 'Buy Me a Coffee';

  @override
  String get coffeeMainMessage => 'Support the development of ESTIMAT';

  @override
  String get coffeeIntroText =>
      'Your support helps us continue improving this app and developing new features to help people better understand their latent values and make more conscious decisions.';

  @override
  String get coffeeWhySupport => 'Why support us?';

  @override
  String get coffeeReason1 => 'Keep the app free and accessible to everyone';

  @override
  String get coffeeReason2 => 'Fund research and development of new features';

  @override
  String get coffeeReason3 =>
      'Support the mathematical and psychological research behind ESTIMAT';

  @override
  String get coffeeReason4 =>
      'Help us reach more people who need better decision-making tools';

  @override
  String get coffeeDonationOptions => 'Choose your support level';

  @override
  String get coffeeSmall => 'Small Coffee';

  @override
  String get coffeeSmallDesc => 'A simple thank you';

  @override
  String get coffeeMedium => 'Large Coffee';

  @override
  String get coffeeMediumDesc => 'Support development';

  @override
  String get coffeeLarge => 'Coffee & Pastry';

  @override
  String get coffeeLargeDesc => 'Boost our research';

  @override
  String get coffeeCustom => 'Custom Amount';

  @override
  String get coffeeCustomDesc => 'Choose your own amount';

  @override
  String get contactTitle => 'Contact & Feedback';

  @override
  String get contactEmail => '<EMAIL>';

  @override
  String get contactEmailDesc =>
      'Send us your feedback, suggestions, or questions';

  @override
  String get contactThankYou => 'Thank you for your support!';

  @override
  String get contactDevelopmentTeam => 'Development Team';

  @override
  String get contactResearchTeam => 'Research & Mathematical Foundation';

  @override
  String get buyNowButton => 'Buy Now';

  @override
  String get sendEmailButton => 'Send Email';

  @override
  String get copyEmailButton => 'Copy Email';

  @override
  String get emailCopiedMessage => 'Email copied to clipboard!';

  @override
  String get coffeeMenuTitle => 'Support Development';

  @override
  String get saveButton => 'Save';

  @override
  String get cancelButton => 'Cancel';

  @override
  String get backButton => 'Back';

  @override
  String get whyMomentsScreenTitle => 'Why Moments?';

  @override
  String get whyMomentsMenuTitle => 'Why Moments?';

  @override
  String get whyHierarchicalMenuTitle => 'Why Hierarchical Organization?';

  @override
  String get whyHierarchicalScreenTitle => 'Why Hierarchical Organization';

  @override
  String get levelsAndDirectionsScreenTitle => 'Levels and Directions';

  @override
  String get skipForNowButton => 'Skip for now';

  @override
  String get noMomentsRecordedYet => 'No moments recorded yet.';

  @override
  String pageCounter(int current, int total) {
    return '$current / $total';
  }

  @override
  String get welcomeToEstimatKeyMoments => 'Welcome to Estimat KeyMoments';

  @override
  String get estimatKeyMomentsDescription =>
      'A comprehensive methodology for understanding and analyzing the key moments that shape your life decisions and personal growth.';

  @override
  String get editThisMomentButton => 'Edit this moment';

  @override
  String get closeButton => 'Close';

  @override
  String get insertNewMomentButton => 'Insert a new moment';

  @override
  String get viewAllMomentsButton => 'View All Moments';

  @override
  String get momentsHistoryTitle => 'Moments History';

  @override
  String momentSavedTitle(String momentType) {
    return '$momentType Saved';
  }

  @override
  String get momentSavedMessage => 'Your moment has been saved successfully.';

  @override
  String get guardianDisplayName => 'GUARDIAN';

  @override
  String get guardianDisplayNameSmall => 'Nurturer';

  @override
  String get warriorDisplayName => 'WARRIOR';

  @override
  String get warriorDisplayNameSmall => 'Releaser';

  @override
  String get versatileDisplayName => 'VERSATILE';

  @override
  String get versatileDisplayNameSmall => 'SelfSeer';

  @override
  String get funDisplayName => 'FUNNY';

  @override
  String get funDisplayNameSmall => 'Enthusiastic';

  @override
  String get strategistDisplayName => 'STRATEGIST';

  @override
  String get strategistDisplayNameSmall => 'Analyst';

  @override
  String get tacticalDisplayName => 'TACTICIAN';

  @override
  String get tacticalDisplayNameSmall => 'Synthesizer';

  @override
  String get altruistDisplayName => 'ALTRUISTIC';

  @override
  String get altruistDisplayNameSmall => 'Empathetic';

  @override
  String get collaboratorDisplayName => 'COLLABORATOR';

  @override
  String get collaboratorDisplayNameSmall => 'Diplomat';

  @override
  String get guardianDescription =>
      'Recognize: You recognize, consume, ingest.\\nStore: You rest, store, and metabolize.';

  @override
  String get warriorDescription =>
      'Discard: You discard, flee, inhibit.\\nExecute: Fight, attack, and struggle.';

  @override
  String get versatileDescription =>
      'Self-Observe: You highlight negative information, feel pain, observe errors.\\nSelf-Transform: You reduce your errors and adapt stimuli.';

  @override
  String get funDescription =>
      'Self-Motivate: You highlight positive information, feel pleasure.\\nSelf-Enjoy: I enhance successes and contrast attitudes and ideas.';

  @override
  String get strategistDescription =>
      'Analyze: You review trends, ask if something could be false, analyze.\\nPredict: You predict what is most probable and create hypotheses.';

  @override
  String get tacticalDescription =>
      'Simplify: You generalize, compare the easiest, fastest way.\\nTrack: You search, hunt, and trace.';

  @override
  String get altruistDescription =>
      'Empathize: You empathize with what is important for others.\\nDeliberate: You consider myself and the needs of as many people as possible, practicing efficiency altruism.';

  @override
  String get collaboratorDescription =>
      'Negotiate: You help to understand, communicate.\\nCollaborate: You cooperate toward shared goals.';

  @override
  String get descriptionNotAvailable => 'Description not available.';

  @override
  String get exportDataTitle => 'Export Data';

  @override
  String get exportOptionsTitle => 'Export Options';

  @override
  String get exportToCSVTitle => 'Export to CSV';

  @override
  String get exportToCSVSubtitle => 'Save your moments data as a CSV file';

  @override
  String get exportSuccessfulTitle => 'Export Successful!';

  @override
  String fileSavedToLabel(String filePath) {
    return 'File saved to: $filePath';
  }

  @override
  String get viewFileButton => 'View File';

  @override
  String get csvDataExportedSuccessfully => 'CSV data exported successfully:';

  @override
  String get exportFailedTitle => 'Export Failed';

  @override
  String get noMomentsToExport => 'No moments to export.';

  @override
  String errorExportingData(String error) {
    return 'Error exporting data: $error';
  }

  @override
  String fileNotFound(String filePath) {
    return 'File not found: $filePath';
  }

  @override
  String errorOpeningFile(String error) {
    return 'Error opening file: $error';
  }

  @override
  String factorialComparisonLabel(String comparisonType) {
    return 'Factorial Comparison: $comparisonType';
  }

  @override
  String get improvementLabel => 'Improvement';

  @override
  String get worseningLabel => 'Worsening';

  @override
  String factorialSliderValueLabel(String value) {
    return 'Factorial Slider Value: $value';
  }

  @override
  String factorialMultiplierLabel(String value) {
    return 'Factorial Multiplier: $value';
  }

  @override
  String get whyRegisterMomentsTitle => 'Why register moments objectively?';

  @override
  String get whyRegisterMomentsContent =>
      'Your emotional peaks and valleys are not just loose data: they are like the coordinates of your internal compass. Recording your motivation (how driven you feel) and your satisfaction (how fulfilled you feel) gives you a clear map to plan goals, routines or tasks that really add to your life.';

  @override
  String get whenNoticeMoreTitle => 'When is this most noticeable?';

  @override
  String get whenNoticeMoreContent =>
      'When you\'re in an emotional low. In those moments, your brain tricks you with thoughts like: \"I never get anything right.\" \"There\'s nothing that motivates me.\" Although this isn\'t true, memory bias—that tendency to remember the negative or most recent—makes you feel that way. 📈 Keeping an objective record of your high moments gives you real proof of who you are when you\'re well, so you don\'t get lost in the fog when you\'re down.';

  @override
  String get highMotivationSatisfactionTitle =>
      'High motivation and high satisfaction';

  @override
  String get highMotivationSatisfactionContent =>
      'Quick question: What was the most significant thing that happened to you this month? If you had a daily record of your emotions, would you answer the same? Probably not. And there\'s a reason: Memory bias: According to Kahneman and Tversky (1979), our brain tends to give more weight to what\'s recent or intense (this is called the peak-end rule). That\'s why we sometimes forget valuable moments that weren\'t so \"noisy\". Solution: Writing down your peaks compensates for this mental trap and shows you the complete picture.';

  @override
  String get evolutionaryViewTitle => 'Evolutionary perspective';

  @override
  String get evolutionaryViewContent =>
      'Strong positive emotions—like pride or fulfillment—are signals of opportunities: a relationship that works, a personal achievement or a decision that aligns you with what you want. Our ancestors survived better if they remembered these moments to repeat them. Your brain isn\'t designed just to make you happy, but to help you thrive. If you know what motivates you, you can seek it more often.';

  @override
  String get practiceEstimatTitle => 'Practice with ESTIMAT:';

  @override
  String get practiceEstimatContent =>
      'Imagine you write down: ✍️ \"I felt powerful explaining my emotions in a workshop without being judged.\" 🔁 When reviewing several similar entries, ESTIMAT shows you: High motivation when you express what you feel. High satisfaction with active listening and validation. 🧭 What do you do with this? Look for or create more situations like this (workshops, talks with understanding friends). Use it as an anchor when you feel lost.';

  @override
  String get highMotivationLowSatisfactionTitle =>
      'High Motivation + Low Satisfaction';

  @override
  String get highMotivationLowSatisfactionContent =>
      'Did it happen to you to be really excited about something and then feel a \'meh\'? According to Gilbert and Wilson, we tend to overestimate future happiness by 40-60%.';

  @override
  String get lowMotivationHighSatisfactionTitle =>
      'Low Motivation + High Satisfaction';

  @override
  String get lowMotivationHighSatisfactionContent =>
      'Have you ever dragged yourself to do something and ended up surprising yourself with how good you felt? The University of British Columbia showed that people underestimate their enjoyment of exercise by 20-30%.';

  @override
  String get lowMotivationLowSatisfactionTitle =>
      'Low Motivation + Low Satisfaction';

  @override
  String get lowMotivationLowSatisfactionContent =>
      'And those days when there is no desire or pleasure? Those lows may be seedbeds for your best ideas. Students who practiced reflective self-assessment showed a 20% increase in their performance.';

  @override
  String get evolutiveFunctionsHeader => 'Evolutive Functions';

  @override
  String get valueDescriptionGuardian =>
      'Recognize: You recognize, consume, ingest.\nStore: You rest, store, and metabolize.';

  @override
  String get valueDescriptionWarrior =>
      'Discard: You discard, flee, inhibit.\nExecute: Fight, attack, and struggle.';

  @override
  String get valueDescriptionVersatile =>
      'Self-Observe: You highlight negative information, feel pain, observe errors.\nSelf-Transform: You reduce your errors and adapt stimuli.';

  @override
  String get valueDescriptionFunny =>
      'Self-Motivate: You highlight positive information, feel pleasure.\nSelf-Enjoy: I enhance successes and contrast attitudes and ideas.';

  @override
  String get valueDescriptionStrategist =>
      'Analyze: You review trends, ask if something could be false, analyze.\nPredict: You predict what is most probable and create hypotheses.';

  @override
  String get valueDescriptionTactician =>
      'Simplify: You generalize, compare the easiest, fastest way.\nTrack: You search, hunt, and trace.';

  @override
  String get valueDescriptionAltruistic =>
      'Empathize: You empathize with what is important for others.\nDeliberate: You consider myself and the needs of as many people as possible, practicing efficiency altruism.';

  @override
  String get valueDescriptionCollaborator =>
      'Negotiate: You help to understand, communicate.\nCollaborate: You cooperate toward shared goals.';

  @override
  String get latentValuesTitle => 'Latent Values';

  @override
  String get lifePossibilitiesChartTitle => 'Life Possibilities Chart';

  @override
  String get periodLabel => 'Period:';

  @override
  String get last7DaysFilter => 'Last 7 Days';

  @override
  String get customRangeFilter => 'Custom Range';

  @override
  String get customDateRangeFilterTitle => 'Custom Date Range Filter';

  @override
  String get startDateLabel => 'Start Date:';

  @override
  String get endDateLabel => 'End Date:';

  @override
  String get selectStartDateHint => 'Select start date';

  @override
  String get selectEndDateHint => 'Select end date';

  @override
  String get resetButton => 'Reset';

  @override
  String get customRangeActiveLabel => 'Custom range active';

  @override
  String get dontShowAgainLabel => 'Don\'t show this again';

  @override
  String get userInstructionsMenuTitle => 'User Instructions';

  @override
  String get userInstructionsScreenTitle => 'How to Use ESTIMAT KeyMoments';

  @override
  String get instructionsWelcomeTitle =>
      'Welcome to Your Personal Growth Journey';

  @override
  String get instructionsWelcomeContent =>
      'ESTIMAT KeyMoments helps you understand your personal values and decision patterns by analyzing your most significant life experiences. This guide will show you how to use the app effectively.';

  @override
  String get instructionsStep1Title =>
      'Step 1: Start with Your Extreme Moments';

  @override
  String get instructionsStep1Content =>
      'Begin by identifying your highest and lowest experiences - these are your reference points that establish your personal limits and provide the foundation for better recommendations.\n\n• **Highest moments**: Times when you felt most motivated and satisfied\n• **Lowest moments**: Times when you felt least motivated and satisfied\n\nThese extremes help the app understand your personal range and provide more accurate insights.';

  @override
  String get instructionsStep2Title => 'Step 2: Navigate the App Interface';

  @override
  String get instructionsStep2Content =>
      '**Main Screen**: Record new moments by selecting the type (improves/worsens motivation or satisfaction)\n\n**Moment Details**: Describe your experience and distribute percentages across four levels:\n• Elemental (physical responses)\n• Personal (individual experiences)\n• Informational (intellectual processes)\n• Social (interpersonal connections)\n\n**Summary Screen**: View your patterns and latent values analysis\n\n**Menu**: Access presentations, support, and additional features';

  @override
  String get instructionsStep3Title => 'Step 3: Record Moments Consistently';

  @override
  String get instructionsStep3Content =>
      'For best results:\n\n• Record moments when they\'re fresh in your memory\n• Be honest about the impact on your motivation and satisfaction\n• Provide specific evidence for your percentage distributions\n• Include both positive and negative experiences\n• Aim to record at least one moment per week';

  @override
  String get instructionsStep4Title => 'Step 4: Understand Your Latent Values';

  @override
  String get instructionsStep4Content =>
      'The app identifies eight latent values based on your moment patterns:\n\n**Elemental Level**: Guardian, Warrior\n**Personal Level**: Versatile, Fun\n**Informational Level**: Strategist, Tactical\n**Social Level**: Altruist, Collaborator\n\nThese values represent your core motivational drivers and help you understand what truly matters to you.';

  @override
  String get instructionsFutureTitle => 'Future AI Integration';

  @override
  String get instructionsFutureContent =>
      'ESTIMAT will continuously improve with Large Language Model (LLM) integration to provide:\n\n• **Personalized insights** based on your unique patterns\n• **Contextual recommendations** for decision-making\n• **Predictive analysis** of how choices might affect your well-being\n• **Adaptive questioning** to help you explore your values more deeply\n\nThe more you use the app, the better it becomes at understanding and supporting your personal growth journey.';

  @override
  String get instructionsTipsTitle => 'Pro Tips for Success';

  @override
  String get instructionsTipsContent =>
      '• **Start with extremes**: Your highest and lowest moments provide the most valuable reference points\n• **Be specific**: Detailed descriptions lead to better insights\n• **Review regularly**: Check your summary screen weekly to spot patterns\n• **Trust the process**: Patterns become clearer with more data\n• **Use for decisions**: Apply your latent values insights to future choices';

  @override
  String showingMomentsLabel(int count, String plural) {
    return 'Showing $count moment$plural';
  }

  @override
  String get whyFocusOnMomentsTitle => 'Why Focus on Moments?';

  @override
  String get whyFocusOnMomentsContent =>
      'Key moments are the building blocks of our decision-making process. By understanding these moments, we can:\n\n• Identify patterns in our behavior\n• Understand what truly motivates us\n• Recognize what brings us satisfaction\n• Make more conscious choices\n• Develop better self-awareness';

  @override
  String get discoveringLatentValuesTitle => 'Discovering Latent Values';

  @override
  String discoveringLatentValuesContent(
    String guardianLabel,
    String warriorLabel,
    String versatileLabel,
    String funLabel,
  ) {
    return 'Your latent values emerge from the combination of:\n\n• **Level percentages** (how you distribute across the four levels)\n• **Directional focus** (inward vs outward orientation)\n• **Impact type** (improves vs worsens)\n\nThese values reveal your core strengths: $guardianLabel, $warriorLabel, $versatileLabel, $funLabel, and others.';
  }

  @override
  String get understandingLatentValuesTitle => 'Understanding Latent Values';

  @override
  String get understandingLatentValuesContent =>
      'Your latent values emerge from how you distribute your focus across the four hierarchical levels and whether you tend toward inward or outward orientation. Each combination reveals core strengths and natural tendencies that guide your decision-making and life satisfaction.';

  @override
  String get directionsFocusTitle => 'Directions Focus';

  @override
  String get whyHierarchicalOrganizationTitle =>
      'Why Hierarchical Organization of Life Moments Might Matter';

  @override
  String get dataVisualizationBiasReductionTitle =>
      'Data Visualization & Bias Reduction';

  @override
  String get experimentalEvidenceTitle => 'Experimental Evidence';

  @override
  String get evolutionaryPsychologyPerspectiveTitle =>
      'Evolutionary Psychology Perspective';

  @override
  String get recognizeYourMomentOrientationLevel =>
      'Recognize your moment, your orientation and level';

  @override
  String get recognizeYourMoment => 'Recognize your moment';

  @override
  String get organizeTheMomentAndLookForEvidence =>
      'Now let\'s look at the distribution of information at the moment and look for evidence.';

  @override
  String get orientationTitle => 'Orientation';

  @override
  String get orientationQuestion =>
      'Was your moment more focused on changing internally or externally?';

  @override
  String get threeLevelsTitle => 'Levels';

  @override
  String get threeLevelsDescription =>
      'Hierarchical analysis of your moment across the four levels of human experience';

  @override
  String get pieStepElementalPersonalTitle => '3 Levels: Elemental vs Personal';

  @override
  String get pieStepElementalPersonalDescription =>
      'For your moment you focused more on your body state or on your personal interests/emotions?';

  @override
  String get pieStepPersonalInformationalTitle =>
      '3 Levels: Personal vs Informational';

  @override
  String get pieStepPersonalInformationalDescription =>
      'For your moment you focused more on your personal interests/emotions or on your gathering and processing information?';

  @override
  String get pieStepInformationalSocialTitle =>
      '3 Levels: Informational vs Social';

  @override
  String get pieStepInformationalSocialDescription =>
      'For your moment you focused more on analyzing information or on connecting with others?';

  @override
  String get pieStepEvidenceQuestion =>
      'What evidence or variables influenced your choice?';

  @override
  String get whyMomentsHeaderSubtitle =>
      'Analyze your moments, know your patterns';

  @override
  String get whyRegisterMomentsObjectivelyTitle =>
      'Why register moments objectively?';

  @override
  String get whyRegisterMomentsObjectivelyContent =>
      'Your emotional peaks and valleys are not just loose data: they are like the coordinates of your internal compass. Recording your motivation (how driven you feel) and your satisfaction (how fulfilled you feel) gives you a clear map to plan goals, routines or tasks that really add to your life.';

  @override
  String get whyRegisterMomentsObjectivelyHighlight =>
      '📈 Keeping a record of your motivation and satisfaction peaks serves to compensate for emotional distortion. It allows you to have evidence of who you are when you\'re well, so you don\'t lose sight of yourself when you\'re not.';

  @override
  String get highMotivationHighSatisfactionTitle =>
      'High Motivation + High Satisfaction';

  @override
  String get researchInfoBoxTitle => '🧠 Research';

  @override
  String get researchInfoBoxContent =>
      'According to Kahneman and Tversky (1979), our brain tends to give more weight to what is recent or intense (peak-end rule). That\'s why we sometimes forget valuable moments that weren\'t so \"noisy\".';

  @override
  String get evolutionaryViewInfoBoxTitle => '🔬 Evolutionary View';

  @override
  String get evolutionaryViewInfoBoxContent =>
      'Strong positive emotions signal adaptive opportunities: successful relationships, purpose-aligned decisions, social or personal achievements.';

  @override
  String get practiceEstimatInfoBoxTitle => '✍️ Practice with ESTIMAT';

  @override
  String get practiceEstimatInfoBoxContent =>
      'By reviewing multiple entries, ESTIMAT shows you patterns and helps you replicate those moments of high motivation and satisfaction.';

  @override
  String get highMotivationLowSatisfactionIntro =>
      'Have you ever been really excited about something and then felt a \"meh\"? 📱';

  @override
  String get impactBiasInfoBoxTitle => '📊 Impact Bias';

  @override
  String get impactBiasInfoBoxContent =>
      'According to Gilbert and Wilson, we tend to overestimate future happiness by';

  @override
  String get practiceInfoBoxTitle => '🎯 Practice';

  @override
  String get practiceInfoBoxContent =>
      '• Simulate: Before diving in, imagine colors, smells, sounds\n• Prediction vs. reality: Write down how much you think you\'ll enjoy it\n• Adjust: If you expected +3 and it was +1, rethink if it\'s worth repeating';

  @override
  String get lowMotivationHighSatisfactionIntro =>
      'Have you ever dragged yourself to do something and ended up surprised by how good you felt? ⛈️😊';

  @override
  String get pleasureUnderestimationInfoBoxTitle =>
      '🏃‍♂️ Pleasure Underestimation';

  @override
  String get pleasureUnderestimationInfoBoxContent =>
      'The University of British Columbia showed that people underestimate their enjoyment of exercise by';

  @override
  String get effortParadoxInfoBoxTitle => '🧬 Effort Paradox';

  @override
  String get effortParadoxInfoBoxContent =>
      'Every drop of effort is exchanged for an extra plus of satisfaction. That \"burst of joy\" is your brain saying \"Well done!\"';

  @override
  String get lowMotivationLowSatisfactionIntro =>
      'And those days when there\'s no desire or pleasure? 😔 Those lows might be seedbeds for your best ideas.';

  @override
  String get reflectionPowerInfoBoxTitle => '📈 Power of Reflection';

  @override
  String get reflectionPowerInfoBoxContent =>
      'Students who practiced reflective self-evaluation showed an increase of';

  @override
  String get practiceEstimatLowInfoBoxTitle => '🎯 Practice with ESTIMAT';

  @override
  String get practiceEstimatLowInfoBoxContent =>
      '• Record your low: Write without filters how you feel\n• Review your log: ESTIMAT will show you patterns\n• Do something small: A walk, a song, three gratitudes';

  @override
  String get generalOverviewTitle => 'The General Overview';

  @override
  String get generalOverviewIntro =>
      'Recording your emotions is not just a hobby—it\'s a tool to know yourself and get the most out of your brain.';

  @override
  String get memoryBiasStatTitle => '🧠 Memory bias';

  @override
  String get memoryBiasStatContent => 'We overvalue what\'s recent or intense';

  @override
  String get impactBiasStatTitle => '🎯 Impact bias';

  @override
  String get impactBiasStatContent => 'We overestimate future happiness';

  @override
  String get underestimationStatTitle => '💪 Underestimation';

  @override
  String get underestimationStatContent =>
      'We enjoy exercise more than we think';

  @override
  String get recoveryStatTitle => '🔄 Recovery';

  @override
  String get recoveryStatContent => 'Reflecting gives you more motivated days';

  @override
  String get generalOverviewConclusion =>
      'What small step can you take today to better understand your emotions? Try writing down a peak and a valley—the results might surprise you. ✨';

  @override
  String get whyHierarchicalHeaderSubtitle =>
      'Why Hierarchical Organization of Life Moments Might Matter';

  @override
  String get whyHierarchicalImportantNote =>
      'Important: If you\'re already experiencing consistent well-being and deep satisfaction, additional organization is likely unnecessary. This approach seems most relevant during transitions, complex decisions, or persistent dissatisfaction.';

  @override
  String get informationTheoryPerspectiveTitle =>
      'Information Theory Perspective';

  @override
  String get debunkingCommonMythsTitle => 'Debunking Common Myths';

  @override
  String get selfPerceptionBiasesTitle =>
      'The Problem of Self-Perception Biases';

  @override
  String get visualProportionsTitle => 'Advantages of Visual Proportions';

  @override
  String get statsVsIntuitionTitle => 'Personal Statistics vs. Intuition';

  @override
  String get memoryHierarchyTitle => 'Memory Hierarchy Experiments';

  @override
  String get decisionFatigueTitle => 'Decision Fatigue Studies';

  @override
  String get millerNumberTitle => 'Miller\'s Magical Number';

  @override
  String get availabilityBiasContent =>
      'Availability Bias (Tversky & Kahneman, 1973): We\'re likely to disproportionately remember recent or emotional events, which could distort our perception of life patterns.';

  @override
  String get overestimationLabel => 'Overestimation';

  @override
  String get overestimationSublabel => 'of recent vs. actual patterns';

  @override
  String get decisionDistortionLabel => 'Decision Distortion';

  @override
  String get decisionDistortionSublabel => 'from memory-based choices';

  @override
  String get hierarchicalVisualizationNote =>
      'Hierarchical moment visualization might counteract this bias by providing a more objective representation of temporal patterns.';

  @override
  String get clevelandMcGillContent =>
      'Cleveland & McGill (1984): Visual perception of proportions appears to be significantly more accurate than narrative memories for evaluating temporal distributions.';

  @override
  String get potentialPersonalApplicationsTitle =>
      'Potential Personal Applications:';

  @override
  String get personalApplicationsList =>
      '• Actual time distribution vs. perception\n• Energy patterns and emotional states\n• Frequency of different experience types\n• Progress toward long-term objectives';

  @override
  String get visualizationDiscrepanciesNote =>
      'These visualizations could reveal discrepancies between subjective perception and objective reality, facilitating more informed decisions.';

  @override
  String get personalIntuitionParadoxContent =>
      'Personal Intuition Paradox: While we trust our intuition for personal decisions, we\'re likely to apply rigorous statistical analysis for professional or financial decisions.';

  @override
  String get financialDecisionsLabel => 'Financial Decisions';

  @override
  String get financialDecisionsSublabel => 'use objective data';

  @override
  String get personalDecisionsLabel => 'Personal Decisions';

  @override
  String get personalDecisionsSublabel => 'use objective data';

  @override
  String get potentialImprovementLabel => 'Potential Improvement';

  @override
  String get potentialImprovementSublabel => 'with systematic organization';

  @override
  String get hierarchicalAnalyticalNote =>
      'Hierarchical organization might allow applying analytical rigor to life decisions while maintaining emotional and intuitive flexibility.';

  @override
  String get socialLevelTitle => '4. Social';

  @override
  String get informationalLevelTitle => '3. Informational';

  @override
  String get personalLevelTitle => '2. Personal';

  @override
  String get elementalLevelTitle => '1. Elemental';

  @override
  String get collaborateFunction => 'Collaborate';

  @override
  String get negotiateFunction => 'Negotiate';

  @override
  String get collaboratorValue => 'COLLABORATOR';

  @override
  String get diplomatSubtitle => 'Diplomat';

  @override
  String get deliberateFunction => 'Deliberate';

  @override
  String get empathizeFunction => 'Empathize';

  @override
  String get altruisticValue => 'ALTRUISTIC';

  @override
  String get empatheticSubtitle => 'Empathetic';

  @override
  String get predictFunction => 'Predict';

  @override
  String get analyzeFunction => 'Analyze';

  @override
  String get strategistValue => 'STRATEGIST';

  @override
  String get analystSubtitle => 'Analyst';

  @override
  String get trackFunction => 'Track';

  @override
  String get simplifyFunction => 'Simplify';

  @override
  String get tacticianValue => 'TACTICIAN';

  @override
  String get synthesizerSubtitle => 'Synthesizer';

  @override
  String get selfEnjoyFunction => 'Self-Enjoy';

  @override
  String get selfMotivateFunction => 'Self-Motivate';

  @override
  String get funnyValue => 'FUNNY';

  @override
  String get enthusiasticSubtitle => 'Enthusiastic';

  @override
  String get selfTransformFunction => 'Self-Transform';

  @override
  String get selfObserveFunction => 'Self-Observe';

  @override
  String get versatileValue => 'VERSATILE';

  @override
  String get selfSeerSubtitle => 'SelfSeer';

  @override
  String get executeFunction => 'Execute';

  @override
  String get discardFunction => 'Discard';

  @override
  String get warriorValue => 'WARRIOR';

  @override
  String get releaserSubtitle => 'Releaser';

  @override
  String get storeFunction => 'Store';

  @override
  String get recognizeFunction => 'Recognize';

  @override
  String get guardianValue => 'GUARDIAN';

  @override
  String get nurturerSubtitle => 'Nurturer';

  @override
  String get memoryHierarchyContent =>
      'Bower et al. (1969): Hierarchical organization can improve recall by approximately 200% compared to random presentation.';

  @override
  String get baselineLabel => 'Baseline';

  @override
  String get randomPresentationSublabel => 'recall capacity';

  @override
  String get hierarchicalOrganizationLabel => 'Hierarchical Organization';

  @override
  String get hierarchicalImprovementSublabel => 'improvement in recall';

  @override
  String get brainProcessesHierarchically =>
      'Likely implication: Your brain probably processes information hierarchically by nature. Fighting against this structure possibly wastes cognitive resources.';

  @override
  String get decisionFatigueContent =>
      'Baumeister et al. (1998): After repeated unstructured decisions, decision quality probably declines significantly.';

  @override
  String get evolutionaryPerspectiveTitle => 'Evolutionary Perspective:';

  @override
  String get ancestorsDecisionsContent =>
      'Our ancestors probably faced limited daily decisions in structured social hierarchies. Modern chaos of disorganized choices may exceed our cognitive capacity.';

  @override
  String get preOrganizedStructures =>
      'Pre-organized hierarchical structures could maintain decision quality even under cognitive load.';

  @override
  String get millerNumberContent =>
      'Miller (1956): Humans can possibly maintain 7±2 unrelated items in working memory, but probably can process 7±2 categories, each containing 7±2 subcategories.';

  @override
  String get individualItemsLabel => 'Individual Items';

  @override
  String get workingMemoryLimitSublabel => 'working memory limit';

  @override
  String get hierarchicalCapacityLabel => 'Hierarchical Capacity';

  @override
  String get organizedElementsSublabel => 'organized elements';

  @override
  String get exponentialProcessingCapacity =>
      'This could create exponential processing capacity through hierarchy, freeing mental resources for pattern recognition and future planning.';

  @override
  String get ancestralMismatchContent =>
      'Modern humans possibly face approximately 35,000 daily decisions, while our ancestors probably encountered 70-100 structured decisions in predictable social hierarchies.';

  @override
  String get ancestralDecisionsLabel => 'Ancestral Decisions';

  @override
  String get structuredPerDaySublabel => 'structured/day';

  @override
  String get modernDecisionsLabel => 'Modern Decisions';

  @override
  String get unstructuredPerDaySublabel => 'unstructured/day';

  @override
  String get schwartzOptionsContent =>
      'Schwartz (2004): More than 8-10 unstructured options can decrease satisfaction by 25% and decision quality by 15%.';

  @override
  String get foragingEfficiencyContent =>
      'Stephens & Krebs (1986): Animals that organized foraging behavior hierarchically (territory → patches → specific resources) likely showed 40-60% better energy efficiency.';

  @override
  String get energyEfficiencyLabel => 'Energy Efficiency';

  @override
  String get hierarchicalOrganizationSublabel => 'hierarchical organization';

  @override
  String get goalAchievementLabel => 'Goal Achievement';

  @override
  String get structuredFrameworksSublabel => 'structured frameworks';

  @override
  String get gigerenzerFrameworksContent =>
      'Gigerenzer (2007): People using hierarchical decision frameworks possibly achieve goals 35% faster with 50% less effort.';

  @override
  String get compressionAdvantageContent =>
      'Shannon (1948): Hierarchical organization probably achieves optimal data compression. Applied to life experiences, this could allow processing exponentially more information.';

  @override
  String get applicationToMomentsTitle => 'Application to Moments:';

  @override
  String get compressionMomentsContent =>
      'Instead of remembering hundreds of disconnected experiences, hierarchical organization possibly allows compressing similar moments into categories, freeing mental resources for pattern recognition and future planning.';

  @override
  String get predictionMachineContent =>
      'Clark (2013): The brain possibly operates as a \"prediction machine,\" constantly generating models of future experiences based on past patterns.';

  @override
  String get neuralReductionLabel => 'Neural Reduction';

  @override
  String get predictableExperiencesSublabel => 'predictable experiences';

  @override
  String get unpredictedActivityLabel => 'Unpredicted Activity';

  @override
  String get neuralActivitySublabel => 'neural activity';

  @override
  String get organizedMomentTracking =>
      'Organized moment tracking probably creates better predictive models, reducing cognitive load and improving future decision-making accuracy.';

  @override
  String get entropyReductionContent =>
      'Bialek et al. (2001): Neural networks using hierarchical processing possibly achieve superior efficiency in information transmission compared to flat structures.';

  @override
  String get lifeApplicationEntropy =>
      'Life application: Hierarchical moment organization probably allows extracting maximum insight from experiences while minimizing cognitive noise.';

  @override
  String get creativityMythCounterTitle => 'Counter-evidence:';

  @override
  String get creativityMythCounterContent =>
      '• Stokes (2005): Creative professionals with organizational frameworks possibly produce more innovative work\n• Schwartz (2004): Too many unstructured options probably decrease creative output\n• Hierarchical organization probably reduces cognitive noise, freeing mental resources for creative thinking';

  @override
  String get successMythCounterContent =>
      '• Ericsson (2016): Elite performers in all domains probably use highly structured practice and reflection systems\n• High-performing individuals probably show superior organizational skills, not less structure\n• Successful hunter-gatherer societies probably had complex hierarchical organization systems';

  @override
  String get hierarchyMythCounterContent =>
      '• All successful primate societies probably exhibit hierarchical organization with clear roles\n• The human brain probably evolved hierarchical processing as its fundamental architecture\n• Even egalitarian societies possibly maintain hierarchical organization for different domains';

  @override
  String get simplicityMythCounterContent =>
      '• Appropriate complexity probably matches environmental demands\n• Over-simplification possibly leads to system failure\n• Well-structured complexity probably reduces cognitive load\n• Hierarchical organization probably achieves optimal balance between simplicity and information richness';

  @override
  String get anxietyMythCounterContent =>
      '• Unstructured uncertainty probably generates more anxiety than organized complexity\n• Clear frameworks possibly reduce decision anxiety\n• Hierarchical organization probably provides predictive structure that calms the nervous system\n• Studies suggest organizational clarity reduces stress hormones';

  @override
  String get fullPresentationMethodologyTitle => 'The Methodology';

  @override
  String get fullPresentationMethodologyContent =>
      'Our approach combines psychological insights with practical analysis:\n\n1. **Moment Identification**: Recognize key moments in your life\n2. **Dimensional Analysis**: Categorize across four levels\n3. **Directional Focus**: Understand inward vs outward orientation\n4. **Impact Assessment**: Evaluate motivation and satisfaction effects\n5. **Pattern Recognition**: Discover your latent values';

  @override
  String get fullPresentationFourLevelsTitle =>
      'Four Levels of Human Experience';

  @override
  String get fullPresentationFourLevelsContent =>
      'These levels work hierarchically, building upon each other to create your complete experience.';

  @override
  String get fullPresentationDirectionalFocusTitle => 'Directional Focus';

  @override
  String get fullPresentationDirectionalFocusContent =>
      'Every moment has both inward and outward components, but one direction typically dominates. Understanding this helps reveal your natural tendencies and preferences.';

  @override
  String get fullPresentationPracticalApplicationTitle =>
      'Practical Application';

  @override
  String get fullPresentationPracticalApplicationContent =>
      'Use this methodology to:\n\n• **Track patterns** in your decision-making\n• **Identify triggers** for motivation and satisfaction\n• **Understand conflicts** between different aspects of yourself\n• **Make better choices** aligned with your values\n• **Develop strategies** for personal growth';

  @override
  String get fullPresentationConclusionTitle => 'Your Journey Begins';

  @override
  String get fullPresentationConclusionContent =>
      'Now that you understand the methodology, you can:\n\n• Start recording your key moments\n• Analyze your patterns over time\n• Discover your unique latent values\n• Use insights for personal development\n\nRemember: Self-awareness is the first step toward intentional living.';

  @override
  String get fullPresentationElementalDescription =>
      'Physical and instinctual responses';

  @override
  String get fullPresentationPersonalDescription =>
      'Individual experiences and emotions';

  @override
  String get fullPresentationInformationalDescription =>
      'Intellectual and analytical processes';

  @override
  String get fullPresentationSocialDescription =>
      'Interpersonal and cultural connections';

  @override
  String get ancestralMismatchTitle => 'Ancestral Environment Mismatch';

  @override
  String get socialHierarchyTitle => 'Social Hierarchy Hypothesis';

  @override
  String get foragingEfficiencyTitle => 'Foraging Efficiency Model';

  @override
  String get compressionAdvantageTitle => 'The Compression Advantage';

  @override
  String get predictionMachineTitle => 'The Brain as Prediction Machine';

  @override
  String get entropyReductionTitle => 'Entropy Reduction Principle';

  @override
  String get creativityMythTitle =>
      'Myth: \'Organization kills creativity and spontaneity\'';

  @override
  String get successMythTitle =>
      'Myth: \'Successful people don\'t need systems—they just improvise\'';

  @override
  String get hierarchyMythTitle =>
      'Myth: \'Hierarchy is unnatural and oppressive\'';

  @override
  String get simplicityMythTitle =>
      'Myth: \'Simple is always better than complex\'';

  @override
  String get anxietyMythTitle =>
      'Myth: \'Organization is only for anxious or controlling people\'';

  @override
  String get socialHierarchyContent =>
      'Sapolsky (2017): Humans and other primates likely show the lowest stress hormone levels when their social position is clearly defined, predictable, and internally controlled.';

  @override
  String get stressReductionElementsTitle =>
      'Key Elements for Stress Reduction:';

  @override
  String get stressReductionElementsList =>
      '• Hierarchical clarity: Defined position\n• Predictability: Consistent rules\n• Internal control: Agency within structure';

  @override
  String get randomPresentationLabel => 'Random Presentation';

  @override
  String get hierarchicalOrganizationBenefits =>
      'Hierarchical organization of life can mimic successful ancestral social structures, likely reducing stress and improving decision-making.';

  @override
  String get fourLevelsProcessTitle => 'Four Levels Process';

  @override
  String get hierarchicalStructureTitle => 'Hierarchical Structure';

  @override
  String get hierarchicalStructureDescription =>
      'The four dimensions follow a hierarchical structure:';

  @override
  String get levelsEquationText =>
      'Elemental + Personal + Informational + Social = 100%';

  @override
  String get elementalAllocationText =>
      'When you allocate a percentage to Elemental, the remaining percentage is distributed among Personal, Informational, and Social.';

  @override
  String get personalAllocationText =>
      'When you allocate a percentage to Personal, the remaining percentage is distributed between Informational and Social.';

  @override
  String get informationalAllocationText =>
      'When you allocate a percentage to Informational, the remaining percentage goes to Social.';

  @override
  String get directionsExplanationText =>
      'Inward + Outward = 100%\n\nThese dimensions represent your orientation or approach to each moment.';

  @override
  String get pleaseEnterTitle => 'Please enter a title';

  @override
  String get titleMustBeTwoWords => 'Title must be exactly two words';

  @override
  String get untitledMoment => 'Untitled Moment';

  @override
  String get whyMomentsTitle => 'Why Moments';

  @override
  String get whyMomentsSubtitle => 'Analyze your moments, know your patterns';

  @override
  String get whyHierarchicalTitle => 'Why Hierarchical Organization';

  @override
  String get whyHierarchicalSubtitle =>
      'Why Hierarchical Organization of Life Moments Might Matter';

  @override
  String get highMotivationHighSatisfactionContent =>
      'According to Kahneman and Tversky (1979), our brain tends to give more weight to the recent or intense (peak-end rule). That\'s why we sometimes forget valuable moments that weren\'t so \'noisy\'.';

  @override
  String get importantNote =>
      'Important: If you\'re already experiencing consistent well-being and deep satisfaction, additional organization is likely unnecessary. This approach seems most relevant during transitions, complex decisions, or persistent dissatisfaction.';

  @override
  String get dataVisualizationTitle => 'Data Visualization & Bias Reduction';

  @override
  String get dataVisualizationContent =>
      'Hierarchical moment visualization might counteract availability bias by providing a more objective representation of temporal patterns. Visual perception of proportions appears to be significantly more accurate than narrative memories.';

  @override
  String get experimentalEvidenceContent =>
      'Hierarchical organization might improve recall by approximately 200% compared to random presentation. After repeated unstructured decisions, decision quality likely declines significantly.';

  @override
  String get evolutionaryPsychologyTitle =>
      'Evolutionary Psychology Perspective';

  @override
  String get evolutionaryPsychologyContent =>
      'Modern humans possibly face approximately 35,000 daily decisions, while our ancestors probably encountered 70-100 structured decisions in predictable social hierarchies.';

  @override
  String get informationTheoryTitle => 'Information Theory Perspective';

  @override
  String get informationTheoryContent =>
      'Hierarchical organization probably achieves optimal data compression. Applied to life experiences, this could allow processing exponentially more information.';

  @override
  String get scientificFooter =>
      'Based on scientific research • Designed for your personal growth';

  @override
  String get guardianValueDescription =>
      'Recognize: You recognize, consume, ingest.\\nStore: You rest, store, and metabolize.';

  @override
  String get warriorValueDescription =>
      'Discard: You discard, flee, inhibit.\\nExecute: Fight, attack, and struggle.';

  @override
  String get versatileValueDescription =>
      'Self-Observe: You highlight negative information, feel pain, observe errors.\\nSelf-Transform: You reduce your errors and adapt stimuli.';

  @override
  String get funValueDescription =>
      'Self-Motivate: You highlight positive information, feel pleasure.\\nSelf-Enjoy: I enhance successes and contrast attitudes and ideas.';

  @override
  String get strategistValueDescription =>
      'Analyze: You review trends, ask if something could be false, analyze.\\nPredict: You predict what is most probable and create hypotheses.';

  @override
  String get tacticalValueDescription =>
      'Simplify: You generalize, compare the easiest, fastest way.\\nTrack: You search, hunt, and trace.';

  @override
  String get altruistValueDescription =>
      'Empathize: You empathize with what is important for others.\\nDeliberate: You consider myself and the needs of as many people as possible, practicing efficiency altruism.';

  @override
  String get collaboratorValueDescription =>
      'Negotiate: You help to understand, communicate.\\nCollaborate: You cooperate toward shared goals.';

  @override
  String get functionCollaborate => 'Collaborate';

  @override
  String get functionNegotiate => 'Negotiate';

  @override
  String get functionDeliberate => 'Deliberate';

  @override
  String get functionEmpathize => 'Empathize';

  @override
  String get functionPredict => 'Predict';

  @override
  String get functionAnalyze => 'Analyze';

  @override
  String get functionTrack => 'Track';

  @override
  String get functionSimplify => 'Simplify';

  @override
  String get functionSelfEnjoy => 'Self-Enjoy';

  @override
  String get functionSelfMotivate => 'Self-Motivate';

  @override
  String get functionSelfTransform => 'Self-Transform';

  @override
  String get functionSelfObserve => 'Self-Observe';

  @override
  String get functionExecute => 'Execute';

  @override
  String get functionDiscard => 'Discard';

  @override
  String get functionStore => 'Store';

  @override
  String get functionRecognize => 'Recognize';

  @override
  String get valueCollaborator => 'COLLABORATOR';

  @override
  String get valueAltruistic => 'ALTRUISTIC';

  @override
  String get valueStrategist => 'STRATEGIST';

  @override
  String get valueTactician => 'TACTICIAN';

  @override
  String get valueFunny => 'FUNNY';

  @override
  String get valueVersatile => 'VERSATILE';

  @override
  String get valueWarrior => 'WARRIOR';

  @override
  String get valueGuardian => 'GUARDIAN';

  @override
  String get subtitleDiplomat => 'Diplomat';

  @override
  String get subtitleEmpathetic => 'Empathetic';

  @override
  String get subtitleAnalyst => 'Analyst';

  @override
  String get subtitleSynthesizer => 'Synthesizer';

  @override
  String get subtitleEnthusiastic => 'Enthusiastic';

  @override
  String get subtitleSelfSeer => 'Self-Seer';

  @override
  String get subtitleReleaser => 'Releaser';

  @override
  String get subtitleNurturer => 'Nurturer';

  @override
  String get descriptionGuardian =>
      'Value focused on preserving, protecting and maintaining stability. Represents the ability to recognize and store important information for security and continuity.';

  @override
  String get descriptionWarrior =>
      'Value focused on action, execution and overcoming obstacles. Represents the ability to execute decisions and discard what no longer serves.';

  @override
  String get descriptionVersatile =>
      'Value focused on adaptability and self-observation. Represents the ability to transform oneself and observe oneself for personal growth.';

  @override
  String get descriptionFunny =>
      'Value focused on pleasure, motivation and positive energy. Represents the ability to self-enjoy and self-motivate to maintain well-being.';

  @override
  String get descriptionStrategist =>
      'Value focused on planning and deep analysis. Represents the ability to predict scenarios and analyze complex information.';

  @override
  String get descriptionTactician =>
      'Value focused on synthesis and efficient tracking. Represents the ability to simplify complexities and track progress.';

  @override
  String get descriptionAltruistic =>
      'Value focused on empathy and consideration for others. Represents the ability to deliberate and empathize for the common good.';

  @override
  String get descriptionCollaborator =>
      'Value focused on cooperation and negotiation. Represents the ability to collaborate effectively and negotiate mutually beneficial solutions.';
}
