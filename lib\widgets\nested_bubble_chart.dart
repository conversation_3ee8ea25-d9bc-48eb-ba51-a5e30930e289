import 'dart:math';
import 'package:flutter/material.dart';

class NestedBubble<PERSON>hart extends StatelessWidget {
  final double elementalPercentage;
  final double personalPercentage;
  final double informationalPercentage;
  final double socialPercentage;
  final bool showElemental;
  final bool showPersonal;
  final bool showInformational;
  final bool showSocial;
  // Add hierarchical percentages for proper display
  final Map<String, double>? hierarchicalPercentages;

  const NestedBubbleChart({
    super.key,
    required this.elementalPercentage,
    required this.personalPercentage,
    required this.informationalPercentage,
    required this.socialPercentage,
    this.showElemental = true,
    this.showPersonal = true,
    this.showInformational = true,
    this.showSocial = true,
    this.hierarchicalPercentages,
  });

  @override
  Widget build(BuildContext context) {
    // The maximum size of the chart - reduced to 70% of original size
    const chartSize = 210.0; // 70% of 300
    const totalWidth = 350.0; // 70% of 500

    return SizedBox(
      width: totalWidth,
      height: chartSize,
      child: CustomPaint(
        size: <PERSON><PERSON>(totalWidth, chartSize),
        painter: NestedBubblePainter(
          elementalPercentage: elementalPercentage,
          personalPercentage: personalPercentage,
          informationalPercentage: informationalPercentage,
          socialPercentage: socialPercentage,
          showElemental: showElemental,
          showPersonal: showPersonal,
          showInformational: showInformational,
          showSocial: showSocial,
          hierarchicalPercentages: hierarchicalPercentages,
          chartSize: chartSize,
        ),
      ),
    );
  }
}

class NestedBubblePainter extends CustomPainter {
  final double elementalPercentage;
  final double personalPercentage;
  final double informationalPercentage;
  final double socialPercentage;
  final bool showElemental;
  final bool showPersonal;
  final bool showInformational;
  final bool showSocial;
  final Map<String, double>? hierarchicalPercentages;
  final double chartSize;

  // Define colors as constants to ensure consistency
  static const elementalColor = Color(0xFF404040);
  static const personalColor = Color(0xFF595959);
  static const informationalColor = Color(0xFF808080);
  static const socialColor = Color(0xFF9F9F9F);

  NestedBubblePainter({
    required this.elementalPercentage,
    required this.personalPercentage,
    required this.informationalPercentage,
    required this.socialPercentage,
    required this.showElemental,
    required this.showPersonal,
    required this.showInformational,
    required this.showSocial,
    this.hierarchicalPercentages,
    required this.chartSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Chart is positioned on the left side of the canvas
    final chartCenter = Offset(chartSize / 2, size.height / 2);
    final maxRadius = chartSize / 2;

    // Get hierarchical percentages or use defaults
    final hierarchical = hierarchicalPercentages ?? {
      'elemental': 25.0,
      'personal': 25.0,
      'informational': 25.0,
      'social': 25.0,
    };

    // Draw bubbles without borders, from largest to smallest
    if (showElemental) {
      _drawBubbleWithoutBorder(canvas, chartCenter, maxRadius, elementalColor);
    }

    if (showPersonal) {
      final personalRadius = maxRadius * sqrt(personalPercentage / 100);
      if (personalPercentage > 0) {
        _drawBubbleWithoutBorder(canvas, chartCenter, personalRadius, personalColor);
      }
    }

    if (showInformational && personalPercentage > 0) {
      final personalRadius = maxRadius * sqrt(personalPercentage / 100);
      final maxInformationalPercentage = personalPercentage;
      final actualInformationalPercentage = min(informationalPercentage, maxInformationalPercentage);

      if (actualInformationalPercentage > 0) {
        final informationalRadius = personalRadius * sqrt(actualInformationalPercentage / personalPercentage);
        _drawBubbleWithoutBorder(canvas, chartCenter, informationalRadius, informationalColor);
      }
    }

    if (showSocial && informationalPercentage > 0 && personalPercentage > 0) {
      final personalRadius = maxRadius * sqrt(personalPercentage / 100);
      final maxInformationalPercentage = personalPercentage;
      final actualInformationalPercentage = min(informationalPercentage, maxInformationalPercentage);
      final informationalRadius = personalRadius * sqrt(actualInformationalPercentage / personalPercentage);

      final maxSocialPercentage = actualInformationalPercentage;
      final actualSocialPercentage = min(socialPercentage, maxSocialPercentage);

      if (actualSocialPercentage > 0) {
        final socialRadius = informationalRadius * sqrt(actualSocialPercentage / actualInformationalPercentage);
        _drawBubbleWithoutBorder(canvas, chartCenter, socialRadius, socialColor);
      }
    }

    // Draw leader lines and labels
    _drawLeaderLinesAndLabels(canvas, chartCenter, maxRadius, hierarchical);
  }

  void _drawBubbleWithoutBorder(Canvas canvas, Offset center, double radius, Color color) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius, paint);
  }

  void _drawLeaderLinesAndLabels(Canvas canvas, Offset chartCenter, double maxRadius, Map<String, double> hierarchical) {
    // Calculate actual bubble radii based on the current percentages
    List<Map<String, dynamic>> visibleLevels = [];

    // Elemental is always visible (100% of chart)
    if (showElemental) {
      visibleLevels.add({
        'name': 'Elemental',
        'color': elementalColor,
        'percentage': hierarchical['elemental']!,
        'radius': maxRadius,
      });
    }

    // Personal bubble - only if percentage > 0
    if (showPersonal && personalPercentage > 0) {
      final personalRadius = maxRadius * sqrt(personalPercentage / 100);
      visibleLevels.add({
        'name': 'Personal',
        'color': personalColor,
        'percentage': hierarchical['personal']!,
        'radius': personalRadius,
      });
    }

    // Informational bubble - only if percentage > 0 and personal exists
    if (showInformational && personalPercentage > 0 && informationalPercentage > 0) {
      final personalRadius = maxRadius * sqrt(personalPercentage / 100);
      final maxInformationalPercentage = personalPercentage;
      final actualInformationalPercentage = min(informationalPercentage, maxInformationalPercentage);

      if (actualInformationalPercentage > 0) {
        final informationalRadius = personalRadius * sqrt(actualInformationalPercentage / personalPercentage);
        visibleLevels.add({
          'name': 'Informational',
          'color': informationalColor,
          'percentage': hierarchical['informational']!,
          'radius': informationalRadius,
        });
      }
    }

    // Social bubble - only if percentage > 0 and informational exists
    if (showSocial && informationalPercentage > 0 && personalPercentage > 0 && socialPercentage > 0) {
      final personalRadius = maxRadius * sqrt(personalPercentage / 100);
      final maxInformationalPercentage = personalPercentage;
      final actualInformationalPercentage = min(informationalPercentage, maxInformationalPercentage);
      final informationalRadius = personalRadius * sqrt(actualInformationalPercentage / personalPercentage);

      final maxSocialPercentage = actualInformationalPercentage;
      final actualSocialPercentage = min(socialPercentage, maxSocialPercentage);

      if (actualSocialPercentage > 0) {
        final socialRadius = informationalRadius * sqrt(actualSocialPercentage / actualInformationalPercentage);
        visibleLevels.add({
          'name': 'Social',
          'color': socialColor,
          'percentage': hierarchical['social']!,
          'radius': socialRadius,
        });
      }
    }

    // Draw leader lines only for visible levels with percentage > 0
    for (int i = 0; i < visibleLevels.length; i++) {
      final level = visibleLevels[i];
      final radius = level['radius'] as double;
      final name = level['name'] as String;
      final percentage = level['percentage'] as double;

      // Only draw if percentage > 0
      if (percentage <= 0) continue;

      // Get the correct color for this level - using explicit Color values
      Color lineColor;
      switch (name) {
        case 'Elemental':
          lineColor = const Color(0xFF404040); // Dark gray
          break;
        case 'Personal':
          lineColor = const Color(0xFF595959); // Medium gray
          break;
        case 'Informational':
          lineColor = const Color(0xFF808080); // Light gray
          break;
        case 'Social':
          lineColor = const Color(0xFF9F9F9F); // Very light gray
          break;
        default:
          lineColor = const Color(0xFF404040); // fallback to Elemental
      }

      // Calculate the top point (apex) of the actual bubble
      final topPoint = Offset(chartCenter.dx, chartCenter.dy - radius);

      // Leader line extends horizontally to the right (80% of original length)
      final lineEndX = chartCenter.dx + maxRadius + 40; // 40px padding (80% of 50px)
      final lineY = topPoint.dy;

      // Draw horizontal leader line with 0.75pt thickness using the correct color
      final linePaint = Paint()
        ..color = lineColor
        ..strokeWidth = 0.75
        ..style = PaintingStyle.stroke;

      canvas.drawLine(topPoint, Offset(lineEndX, lineY), linePaint);

      // Draw label text with Garamond font at 50% size (7px)
      final labelText = '$name ${percentage.round()}%';
      final textSpan = TextSpan(
        text: labelText,
        style: TextStyle(
          color: Colors.black,
          fontSize: 7, // 50% of 14px
          fontFamily: 'Garamond',
          fontWeight: FontWeight.w400,
        ),
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.left,
      );

      textPainter.layout();

      // Position text to the right of the line end
      final textOffset = Offset(
        lineEndX + 10, // 10px padding from line end
        lineY - textPainter.height / 2,
      );

      textPainter.paint(canvas, textOffset);
    }
  }

  @override
  bool shouldRepaint(NestedBubblePainter oldDelegate) {
    return oldDelegate.elementalPercentage != elementalPercentage ||
           oldDelegate.personalPercentage != personalPercentage ||
           oldDelegate.informationalPercentage != informationalPercentage ||
           oldDelegate.socialPercentage != socialPercentage ||
           oldDelegate.showElemental != showElemental ||
           oldDelegate.showPersonal != showPersonal ||
           oldDelegate.showInformational != showInformational ||
           oldDelegate.showSocial != showSocial ||
           oldDelegate.hierarchicalPercentages != hierarchicalPercentages ||
           oldDelegate.chartSize != chartSize;
  }
}
