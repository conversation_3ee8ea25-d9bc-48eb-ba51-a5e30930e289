import 'package:flutter/material.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ESTIMAT',
      theme: ThemeData(
        primarySwatch: Colors.indigo,
        fontFamily: 'Roboto',
      ),
      home: EmotionTrackerScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class EmotionTrackerScreen extends StatefulWidget {
  @override
  _EmotionTrackerScreenState createState() => _EmotionTrackerScreenState();
}

class _EmotionTrackerScreenState extends State<EmotionTrackerScreen> {
  Map<String, bool> expandedSections = {};

  void toggleSection(String sectionId) {
    setState(() {
      expandedSections[sectionId] = !(expandedSections[sectionId] ?? false);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF8FAFC),
              Color(0xFFF1F5F9),
            ],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Header
                _buildHeader(),
                SizedBox(height: 32),
                
                // Sections
                ..._buildSections(),
                
                SizedBox(height: 48),
                
                // Footer
                _buildFooter(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: [Color(0xFF4F46E5), Color(0xFF7C3AED)],
          ).createShader(bounds),
          child: Text(
            'ESTIMAT',
            style: TextStyle(
              fontSize: 36,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        SizedBox(height: 8),
        Text(
          'Analiza tus momentos, conoce tus patrones',
          style: TextStyle(
            fontSize: 18,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  List<Widget> _buildSections() {
    final sections = [
      EmotionSection(
        id: 'intro',
        title: '¿Por qué registrar momentos objetivamente?',
        icon: Icons.psychology,
        gradient: [Color(0xFF6366F1), Color(0xFF7C3AED)],
        content: _buildIntroContent(),
      ),
      EmotionSection(
        id: 'high-high',
        title: 'Alta Motivación + Alta Satisfacción',
        icon: Icons.trending_up,
        gradient: [Color(0xFF10B981), Color(0xFF059669)],
        content: _buildHighHighContent(),
      ),
      EmotionSection(
        id: 'high-low',
        title: 'Alta Motivación + Baja Satisfacción',
        icon: Icons.gps_fixed,
        gradient: [Color(0xFFF97316), Color(0xFFEF4444)],
        content: _buildHighLowContent(),
      ),
      EmotionSection(
        id: 'low-high',
        title: 'Baja Motivación + Alta Satisfacción',
        icon: Icons.favorite,
        gradient: [Color(0xFFEC4899), Color(0xFFE11D48)],
        content: _buildLowHighContent(),
      ),
      EmotionSection(
        id: 'low-low',
        title: 'Baja Motivación + Baja Satisfacción',
        icon: Icons.lightbulb,
        gradient: [Color(0xFF6B7280), Color(0xFF475569)],
        content: _buildLowLowContent(),
      ),
      EmotionSection(
        id: 'summary',
        title: 'El Panorama General',
        icon: Icons.bar_chart,
        gradient: [Color(0xFF8B5CF6), Color(0xFF6366F1)],
        content: _buildSummaryContent(),
      ),
    ];

    return sections.map((section) => Padding(
      padding: EdgeInsets.only(bottom: 16),
      child: _buildSectionWidget(section),
    )).toList();
  }

  Widget _buildSectionWidget(EmotionSection section) {
    bool isExpanded = expandedSections[section.id] ?? false;
    
    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      child: Column(
        children: [
          GestureDetector(
            onTap: () => toggleSection(section.id),
            child: Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: section.gradient),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: section.gradient[0].withOpacity(0.3),
                    blurRadius: 8,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(section.icon, color: Colors.white, size: 24),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      section.title,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.keyboard_arrow_down : Icons.keyboard_arrow_right,
                    color: Colors.white,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
          AnimatedCrossFade(
            firstChild: Container(),
            secondChild: Container(
              margin: EdgeInsets.only(top: 8),
              child: section.content,
            ),
            crossFadeState: isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
            duration: Duration(milliseconds: 300),
          ),
        ],
      ),
    );
  }

  Widget _buildStatHighlight(String number, String text, List<Color> colors) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: colors),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: colors[0].withOpacity(0.3),
            blurRadius: 6,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        number,
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildContentCard(Widget child, {Color? borderColor}) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: borderColor != null ? Border.all(color: borderColor, width: 2) : null,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }

  Widget _buildIntroContent() {
    return _buildContentCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tus picos y valles emocionales no son solo datos sueltos: son como las coordenadas de tu brújula interna. Registrar tu motivación (qué tan impulsado te sentís) y tu satisfacción (qué tan pleno te sentís) te da un mapa claro para planificar metas, rutinas o tareas que realmente sumen a tu vida.',
            style: TextStyle(
              color: Colors.grey[700],
              height: 1.6,
              fontSize: 15,
            ),
          ),
          SizedBox(height: 16),
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFFDEF7FF), Color(0xFFF3E8FF)],
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '📈 Llevar un registro de tus picos de motivación y satisfacción sirve para compensar la distorsión emocional. Te permite tener evidencia de quién sos cuando estás bien, para no perderte de vista cuando estás mal.',
              style: TextStyle(
                color: Colors.grey[800],
                fontWeight: FontWeight.w500,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHighHighContent() {
    return _buildContentCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoBox(
            '🧠 Investigación',
            'Según Kahneman y Tversky (1979), nuestro cerebro tiende a darle más peso a lo reciente o lo intenso (regla del pico-final). Por eso, a veces olvidamos momentos valiosos que no fueron tan "ruidosos".',
            Color(0xFFF0FDF4),
            Color(0xFF16A34A),
          ),
          SizedBox(height: 12),
          _buildInfoBox(
            '🔬 Mirada Evolutiva',
            'Las emociones positivas fuertes señalan oportunidades adaptativas: relaciones exitosas, decisiones alineadas al propósito, logros sociales o personales.',
            Color(0xFFFEFCE8),
            Color(0xFFCA8A04),
          ),
          SizedBox(height: 12),
          _buildInfoBox(
            '✍️ Práctica con ESTIMAT',
            'Al revisar varias entradas, ESTIMAT te muestra patrones y te ayuda a replicar esos momentos de alta motivación y satisfacción.',
            Color(0xFFEFF6FF),
            Color(0xFF2563EB),
          ),
        ],
      ),
    );
  }

  Widget _buildHighLowContent() {
    return _buildContentCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '¿Te pasó estar re entusiasmado por algo y después sentir un "meh"? 📱',
            style: TextStyle(color: Colors.grey[700], fontSize: 15),
          ),
          SizedBox(height: 16),
          _buildInfoBox(
            '📊 Impact Bias',
            'Según Gilbert y Wilson, solemos sobreestimar la felicidad futura en un',
            Color(0xFFFFF7ED),
            Color(0xFFEA580C),
            extraWidget: _buildStatHighlight('40-60%', '', [Color(0xFFF97316), Color(0xFFEF4444)]),
          ),
          SizedBox(height: 12),
          _buildInfoBox(
            '🎯 Práctica',
            '• Simulá: Antes de lanzarte, imaginá colores, olores, sonidos\n• Predicción vs. realidad: Anotá cuánto creés que vas a disfrutar\n• Ajustá: Si esperabas +3 y fue +1, repensá si vale la pena repetir',
            Color(0xFFFAF5FF),
            Color(0xFF9333EA),
          ),
        ],
      ),
    );
  }

  Widget _buildLowHighContent() {
    return _buildContentCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '¿Alguna vez te arrastraste a hacer algo y terminaste sorprendiéndote con lo bien que te sentiste? ⛈️😊',
            style: TextStyle(color: Colors.grey[700], fontSize: 15),
          ),
          SizedBox(height: 16),
          _buildInfoBox(
            '🏃‍♂️ Subestimación del Placer',
            'La Universidad de British Columbia mostró que la gente subestima su disfrute del ejercicio en un',
            Color(0xFFFDF2F8),
            Color(0xFFEC4899),
            extraWidget: _buildStatHighlight('20-30%', '', [Color(0xFFEC4899), Color(0xFFE11D48)]),
          ),
          SizedBox(height: 12),
          _buildInfoBox(
            '🧬 Paradoja del Esfuerzo',
            'Cada gota de esfuerzo se canjea por un plus extra de satisfacción. Ese "golpe de alegría" es tu cerebro diciendo "¡Bien hecho!"',
            Color(0xFFF0FDF4),
            Color(0xFF16A34A),
          ),
        ],
      ),
    );
  }

  Widget _buildLowLowContent() {
    return _buildContentCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '¿Y esos días en que no hay ganas ni placer? 😔 Puede que esos bajones sean semilleros de tus mejores ideas.',
            style: TextStyle(color: Colors.grey[700], fontSize: 15),
          ),
          SizedBox(height: 16),
          _buildInfoBox(
            '📈 Poder de la Reflexión',
            'Los estudiantes que practicaban autoevaluación reflexiva mostraron un aumento del',
            Color(0xFFEFF6FF),
            Color(0xFF2563EB),
            extraWidget: _buildStatHighlight('20%', '', [Color(0xFF3B82F6), Color(0xFF6366F1)]),
          ),
          SizedBox(height: 12),
          _buildInfoBox(
            '🎯 Práctica con ESTIMAT',
            '• Registrá tu bajón: Escribí sin filtros cómo te sentís\n• Revisá tu bitácora: ESTIMAT te mostrará patrones\n• Hacé algo pequeño: Un paseo, una canción, tres agradecimientos',
            Color(0xFFFEFCE8),
            Color(0xFFCA8A04),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryContent() {
    return _buildContentCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Registrar tus emociones no es solo un pasatiempo—es una herramienta para conocerte y sacarle jugo a tu cerebro.',
            style: TextStyle(color: Colors.grey[700], fontSize: 15),
          ),
          SizedBox(height: 20),
          GridView.count(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              _buildStatCard('🧠 Sesgo de memoria', 'Sobrevaloramos lo reciente o intenso', [Color(0xFFFEF2F2), Color(0xFFFED7AA)]),
              _buildStatCard('🎯 Impact bias', 'Sobreestimamos la felicidad futura', [Color(0xFFEFF6FF), Color(0xFFE0F2FE)], stat: '40-60%'),
              _buildStatCard('💪 Subestimación', 'Disfrutamos el ejercicio más de lo que creemos', [Color(0xFFF0FDF4), Color(0xFFDCFCE7)], stat: '20-30%'),
              _buildStatCard('🔄 Recuperación', 'Reflexionar te da más días motivados', [Color(0xFFFAF5FF), Color(0xFFF3E8FF)], stat: '20%'),
            ],
          ),
          SizedBox(height: 20),
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFFEEF2FF), Color(0xFFF3E8FF)],
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '¿Qué pequeño paso podés dar hoy para entender mejor tus emociones? Probá anotar un pico y un valle—los resultados podrían sorprenderte. ✨',
              style: TextStyle(
                color: Colors.grey[800],
                fontWeight: FontWeight.w500,
                height: 1.5,
                fontSize: 15,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoBox(String title, String content, Color bgColor, Color titleColor, {Widget? extraWidget}) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border(left: BorderSide(color: titleColor, width: 4)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: titleColor,
              fontSize: 14,
            ),
          ),
          SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Text(
                  content,
                  style: TextStyle(
                    color: Colors.grey[700],
                    height: 1.5,
                    fontSize: 14,
                  ),
                ),
              ),
              if (extraWidget != null) extraWidget,
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String subtitle, List<Color> colors, {String? stat}) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: colors),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
              fontSize: 12,
            ),
          ),
          SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              color: Colors.grey[700],
              fontSize: 11,
              height: 1.3,
            ),
          ),
          if (stat != null) ...[
            SizedBox(height: 8),
            _buildStatHighlight(stat, '', [Color(0xFF6366F1), Color(0xFF8B5CF6)]),
          ],
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Text(
      'Basado en investigación científica • Diseñado para tu crecimiento personal',
      style: TextStyle(
        color: Colors.grey[500],
        fontSize: 12,
      ),
      textAlign: TextAlign.center,
    );
  }
}

class EmotionSection {
  final String id;
  final String title;
  final IconData icon;
  final List<Color> gradient;
  final Widget content;

  EmotionSection({
    required this.id,
    required this.title,
    required this.icon,
    required this.gradient,
    required this.content,
  });
}