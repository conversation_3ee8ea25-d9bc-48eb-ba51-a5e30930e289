import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_pt.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('es'),
    Locale('pt'),
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'Estimat KeyMoments'**
  String get appTitle;

  /// Title for language selection screen
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguageTitle;

  /// No description provided for @englishLanguage.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get englishLanguage;

  /// No description provided for @portugueseLanguage.
  ///
  /// In en, this message translates to:
  /// **'Portuguese'**
  String get portugueseLanguage;

  /// No description provided for @spanishLanguage.
  ///
  /// In en, this message translates to:
  /// **'Spanish'**
  String get spanishLanguage;

  /// Title for the onboarding screen
  ///
  /// In en, this message translates to:
  /// **'Welcome to Estimat KeyMoments'**
  String get onboardingScreenTitle;

  /// No description provided for @onboardingScene1Message.
  ///
  /// In en, this message translates to:
  /// **'Noise can be simplified into notes and turned into music'**
  String get onboardingScene1Message;

  /// No description provided for @onboardingScene1MessagePart2.
  ///
  /// In en, this message translates to:
  /// **'With a bit of proportion...'**
  String get onboardingScene1MessagePart2;

  /// No description provided for @onboardingScene2Message.
  ///
  /// In en, this message translates to:
  /// **'Any image can be replicated through light, shadow, and color, and transformed into art'**
  String get onboardingScene2Message;

  /// No description provided for @onboardingScene2MessagePart2.
  ///
  /// In en, this message translates to:
  /// **'With a bit of proportion...'**
  String get onboardingScene2MessagePart2;

  /// No description provided for @onboardingScene3Message.
  ///
  /// In en, this message translates to:
  /// **'Your cognitive overload—could you refine what you value most in yourself?'**
  String get onboardingScene3Message;

  /// No description provided for @onboardingScene3MessagePart2.
  ///
  /// In en, this message translates to:
  /// **'With a bit of ...'**
  String get onboardingScene3MessagePart2;

  /// No description provided for @onboardingScene4Message.
  ///
  /// In en, this message translates to:
  /// **'With information theory, an AI can reduce complex data to 5% and reconstruct it with 95% functional fidelity; these are latent vectors'**
  String get onboardingScene4Message;

  /// No description provided for @onboardingScene4MessagePart2.
  ///
  /// In en, this message translates to:
  /// **'Maybe you could apply a bit of that to your latent values...'**
  String get onboardingScene4MessagePart2;

  /// No description provided for @onboardingFlowchartMessage.
  ///
  /// In en, this message translates to:
  /// **'Let\'s identify our reference moments. Track the flows associated with those moments. Evaluate according to their function. Discover the latent values that drive us:'**
  String get onboardingFlowchartMessage;

  /// No description provided for @onboardingFlowchartTitle.
  ///
  /// In en, this message translates to:
  /// **'Process Flow'**
  String get onboardingFlowchartTitle;

  /// No description provided for @nextButton.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get nextButton;

  /// No description provided for @previousButton.
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previousButton;

  /// No description provided for @getStartedButton.
  ///
  /// In en, this message translates to:
  /// **'Get Started'**
  String get getStartedButton;

  /// Button text to exit the onboarding flow and go to the main app
  ///
  /// In en, this message translates to:
  /// **'Exit to App'**
  String get exitToAppButton;

  /// No description provided for @motivationAppBarTitle.
  ///
  /// In en, this message translates to:
  /// **'Motivation Moments'**
  String get motivationAppBarTitle;

  /// No description provided for @satisfactionAppBarTitle.
  ///
  /// In en, this message translates to:
  /// **'Satisfaction Moments'**
  String get satisfactionAppBarTitle;

  /// No description provided for @selectMomentTypeTitle.
  ///
  /// In en, this message translates to:
  /// **'Select Moment Type'**
  String get selectMomentTypeTitle;

  /// No description provided for @selectImprovementWorsening.
  ///
  /// In en, this message translates to:
  /// **'Choose between Improvement or Worsening:'**
  String get selectImprovementWorsening;

  /// No description provided for @selectMotivationSatisfaction.
  ///
  /// In en, this message translates to:
  /// **'Choose between Motivation or Satisfaction:'**
  String get selectMotivationSatisfaction;

  /// No description provided for @improvesLabel.
  ///
  /// In en, this message translates to:
  /// **'Improves'**
  String get improvesLabel;

  /// No description provided for @worsensLabel.
  ///
  /// In en, this message translates to:
  /// **'Worsens'**
  String get worsensLabel;

  /// No description provided for @motivationLabel.
  ///
  /// In en, this message translates to:
  /// **'Motivation'**
  String get motivationLabel;

  /// No description provided for @satisfactionLabel.
  ///
  /// In en, this message translates to:
  /// **'Satisfaction'**
  String get satisfactionLabel;

  /// Label for elemental level
  ///
  /// In en, this message translates to:
  /// **'Elemental'**
  String get elementalLabel;

  /// Label for personal level
  ///
  /// In en, this message translates to:
  /// **'Personal'**
  String get personalLabel;

  /// Label for informational level
  ///
  /// In en, this message translates to:
  /// **'Informational'**
  String get informationalLabel;

  /// Label for social level
  ///
  /// In en, this message translates to:
  /// **'Social'**
  String get socialLabel;

  /// Explanation of the elemental level
  ///
  /// In en, this message translates to:
  /// **'Based on environmental stimuli, we give a response that was modulated by evolution. More correlated with physical activities: Recognition, consumption, rest, storage, execute, fight, attack, and struggle.'**
  String get elementalExplanation;

  /// Explanation of the personal level
  ///
  /// In en, this message translates to:
  /// **'Adding to the memory that comes from our ancestors, we have a memory that is created from the unique interaction between an individual and their environment. ainful and pleasant. More correlated with: Error observation, adaptation, pain awareness, positive focus, pleasure, success enhancement'**
  String get personalExplanation;

  /// Explanation of the informational level
  ///
  /// In en, this message translates to:
  /// **'Upon reaching the intellectual level, in addition to integrating memories and experiences with the environment, we achieve a unique ability: to relate memories or an individual\'s own memories to each other and encode abstract information (example: Trend analysis, hypothesis creation, prediction, Generalization, efficiency, tracking).'**
  String get informationalExplanation;

  /// Explanation of the social level
  ///
  /// In en, this message translates to:
  /// **'Social relationships involve the connection of information between individuals, the exchange of culture and communication. We can define a boundary between intellectual level and social level when we use our intellectual processes to interact with other beings capable of combining their own memories. To Empathy, consideration of others\' needs, Communication, cooperation, shared goals'**
  String get socialExplanation;

  /// Label for inward direction
  ///
  /// In en, this message translates to:
  /// **'Inward'**
  String get inwardLabel;

  /// Label for outward direction
  ///
  /// In en, this message translates to:
  /// **'Outward'**
  String get outwardLabel;

  /// Explanation of inward direction
  ///
  /// In en, this message translates to:
  /// **'Reflexive: To store, self-transform, predict, empathize.'**
  String get inwardExplanation;

  /// Explanation of outward direction
  ///
  /// In en, this message translates to:
  /// **'Intuitive: To execute, enjoy, track, collaborate.'**
  String get outwardExplanation;

  /// No description provided for @titleInputHint.
  ///
  /// In en, this message translates to:
  /// **'Enter a two-word title'**
  String get titleInputHint;

  /// No description provided for @descriptionInputHint.
  ///
  /// In en, this message translates to:
  /// **'Describe this moment'**
  String get descriptionInputHint;

  /// No description provided for @evidenceInputHint.
  ///
  /// In en, this message translates to:
  /// **'Provide evidence for this distribution'**
  String get evidenceInputHint;

  /// No description provided for @describeDistributionHint.
  ///
  /// In en, this message translates to:
  /// **'Describe why you chose this distribution...'**
  String get describeDistributionHint;

  /// No description provided for @firstMomentEvidenceHint.
  ///
  /// In en, this message translates to:
  /// **'Explain why this moment creates more possibilities than just being alive. What new opportunities, paths, or potential does it open up?'**
  String get firstMomentEvidenceHint;

  /// No description provided for @comparisonEvidenceHint.
  ///
  /// In en, this message translates to:
  /// **'Explain how many possibilities this moment creates compared to your life baseline...'**
  String get comparisonEvidenceHint;

  /// No description provided for @lifePossibilitiesFactorTitle.
  ///
  /// In en, this message translates to:
  /// **'Life Possibilities Factor'**
  String get lifePossibilitiesFactorTitle;

  /// No description provided for @howManyPossibilitiesTitle.
  ///
  /// In en, this message translates to:
  /// **'How Many Possibilities Does This Moment Create'**
  String get howManyPossibilitiesTitle;

  /// No description provided for @comparedToLifeBaseline.
  ///
  /// In en, this message translates to:
  /// **'Compared to your life baseline, this moment creates:'**
  String get comparedToLifeBaseline;

  /// No description provided for @morePossibilitiesButton.
  ///
  /// In en, this message translates to:
  /// **'More Possibilities'**
  String get morePossibilitiesButton;

  /// No description provided for @fewerPossibilitiesButton.
  ///
  /// In en, this message translates to:
  /// **'Fewer Possibilities'**
  String get fewerPossibilitiesButton;

  /// No description provided for @vsLifeBaseline.
  ///
  /// In en, this message translates to:
  /// **'vs Life Baseline'**
  String get vsLifeBaseline;

  /// No description provided for @explainWhyFirstMomentTitle.
  ///
  /// In en, this message translates to:
  /// **'Explain Why This Moment Creates More Possibilities Than Life'**
  String get explainWhyFirstMomentTitle;

  /// No description provided for @provideEvidenceTitle.
  ///
  /// In en, this message translates to:
  /// **'Provide Evidence for this Possibilities Assessment'**
  String get provideEvidenceTitle;

  /// No description provided for @continueButton.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueButton;

  /// No description provided for @lifePossibilitiesChart.
  ///
  /// In en, this message translates to:
  /// **'Life Possibilities Chart'**
  String get lifePossibilitiesChart;

  /// No description provided for @allTimeFilter.
  ///
  /// In en, this message translates to:
  /// **'All Time'**
  String get allTimeFilter;

  /// No description provided for @lastWeekFilter.
  ///
  /// In en, this message translates to:
  /// **'Last Week'**
  String get lastWeekFilter;

  /// No description provided for @lastMonthFilter.
  ///
  /// In en, this message translates to:
  /// **'Last Month'**
  String get lastMonthFilter;

  /// No description provided for @last3MonthsFilter.
  ///
  /// In en, this message translates to:
  /// **'Last 3 Months'**
  String get last3MonthsFilter;

  /// No description provided for @currentPreviewLabel.
  ///
  /// In en, this message translates to:
  /// **'Current Preview:'**
  String get currentPreviewLabel;

  /// No description provided for @lifeLabel.
  ///
  /// In en, this message translates to:
  /// **'Life'**
  String get lifeLabel;

  /// No description provided for @previewLabel.
  ///
  /// In en, this message translates to:
  /// **'Preview'**
  String get previewLabel;

  /// No description provided for @lifeBaselineLabel.
  ///
  /// In en, this message translates to:
  /// **'Life Baseline (1x)'**
  String get lifeBaselineLabel;

  /// No description provided for @morePossibilitiesLabel.
  ///
  /// In en, this message translates to:
  /// **'More Possibilities'**
  String get morePossibilitiesLabel;

  /// No description provided for @fewerPossibilitiesLabel.
  ///
  /// In en, this message translates to:
  /// **'Fewer Possibilities'**
  String get fewerPossibilitiesLabel;

  /// No description provided for @currentPreviewLegend.
  ///
  /// In en, this message translates to:
  /// **'Current Preview'**
  String get currentPreviewLegend;

  /// No description provided for @lifePossibilitiesExplanation.
  ///
  /// In en, this message translates to:
  /// **'Compare this moment to your life baseline (1x). How many times more possibilities does this moment create for you? You can go above or below previous moments, but never below the life baseline.\\n\\nThe factorial calculation represents the exponential growth of possibilities that meaningful moments can create in your life.'**
  String get lifePossibilitiesExplanation;

  /// No description provided for @minimumLifeBaselineNote.
  ///
  /// In en, this message translates to:
  /// **'Minimum: 1.0x (life baseline)'**
  String get minimumLifeBaselineNote;

  /// No description provided for @guardianLabel.
  ///
  /// In en, this message translates to:
  /// **'Guardian'**
  String get guardianLabel;

  /// No description provided for @warriorLabel.
  ///
  /// In en, this message translates to:
  /// **'Warrior'**
  String get warriorLabel;

  /// No description provided for @versatileLabel.
  ///
  /// In en, this message translates to:
  /// **'Versatile'**
  String get versatileLabel;

  /// No description provided for @funLabel.
  ///
  /// In en, this message translates to:
  /// **'Fun'**
  String get funLabel;

  /// No description provided for @strategistLabel.
  ///
  /// In en, this message translates to:
  /// **'Strategist'**
  String get strategistLabel;

  /// No description provided for @tacticalLabel.
  ///
  /// In en, this message translates to:
  /// **'Tactical'**
  String get tacticalLabel;

  /// No description provided for @altruistLabel.
  ///
  /// In en, this message translates to:
  /// **'Altruist'**
  String get altruistLabel;

  /// No description provided for @collaboratorLabel.
  ///
  /// In en, this message translates to:
  /// **'Collaborator'**
  String get collaboratorLabel;

  /// No description provided for @summaryTitle.
  ///
  /// In en, this message translates to:
  /// **'Summary'**
  String get summaryTitle;

  /// No description provided for @motivationSectionTitle.
  ///
  /// In en, this message translates to:
  /// **'Motivation Analysis'**
  String get motivationSectionTitle;

  /// No description provided for @satisfactionSectionTitle.
  ///
  /// In en, this message translates to:
  /// **'Satisfaction Analysis'**
  String get satisfactionSectionTitle;

  /// No description provided for @latentValuesSectionTitle.
  ///
  /// In en, this message translates to:
  /// **'Latent Values'**
  String get latentValuesSectionTitle;

  /// No description provided for @improvesMotivationLabel.
  ///
  /// In en, this message translates to:
  /// **'Improves Motivation'**
  String get improvesMotivationLabel;

  /// No description provided for @worsensMotivationLabel.
  ///
  /// In en, this message translates to:
  /// **'Worsens Motivation'**
  String get worsensMotivationLabel;

  /// No description provided for @improvesSatisfactionLabel.
  ///
  /// In en, this message translates to:
  /// **'Improves Satisfaction'**
  String get improvesSatisfactionLabel;

  /// No description provided for @worsensSatisfactionLabel.
  ///
  /// In en, this message translates to:
  /// **'Worsens Satisfaction'**
  String get worsensSatisfactionLabel;

  /// No description provided for @proportionLabel.
  ///
  /// In en, this message translates to:
  /// **'Proportion'**
  String get proportionLabel;

  /// No description provided for @exportDataButton.
  ///
  /// In en, this message translates to:
  /// **'Export Data'**
  String get exportDataButton;

  /// No description provided for @viewOnboardingMenu.
  ///
  /// In en, this message translates to:
  /// **'Onboarding'**
  String get viewOnboardingMenu;

  /// No description provided for @viewFullPresentationMenu.
  ///
  /// In en, this message translates to:
  /// **'Full Presentation'**
  String get viewFullPresentationMenu;

  /// No description provided for @viewLevelProcessFocusMenu.
  ///
  /// In en, this message translates to:
  /// **'Levels and Directions'**
  String get viewLevelProcessFocusMenu;

  /// No description provided for @viewLatentValuesMenu.
  ///
  /// In en, this message translates to:
  /// **'Evolutive Functions and Values'**
  String get viewLatentValuesMenu;

  /// No description provided for @fromIACodeToHumanValuesMenu.
  ///
  /// In en, this message translates to:
  /// **'From IA code to human values'**
  String get fromIACodeToHumanValuesMenu;

  /// No description provided for @jacoMinestSupportMenu.
  ///
  /// In en, this message translates to:
  /// **'Jacominest Support'**
  String get jacoMinestSupportMenu;

  /// No description provided for @changeLanguageMenu.
  ///
  /// In en, this message translates to:
  /// **'Change Language'**
  String get changeLanguageMenu;

  /// No description provided for @supportScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get supportScreenTitle;

  /// No description provided for @supportMainMessage.
  ///
  /// In en, this message translates to:
  /// **'Saving a mind saves more than a thousand cans.'**
  String get supportMainMessage;

  /// No description provided for @supportIntroText.
  ///
  /// In en, this message translates to:
  /// **'The odds may not be much in our favor, but saving a mind that may be lost, hopeless, or trapped in destructive cycles can generate exponential returns in the long term.'**
  String get supportIntroText;

  /// No description provided for @supportEvidenceTitle.
  ///
  /// In en, this message translates to:
  /// **'Evidence'**
  String get supportEvidenceTitle;

  /// No description provided for @supportStatistic1.
  ///
  /// In en, this message translates to:
  /// **'People who participate in educational programs in prison are 43% less likely to return.'**
  String get supportStatistic1;

  /// No description provided for @supportStatistic1Source.
  ///
  /// In en, this message translates to:
  /// **'RAND Corporation (2013). \"Evaluating the Effectiveness of Correctional Education\"'**
  String get supportStatistic1Source;

  /// No description provided for @supportStatistic1Link.
  ///
  /// In en, this message translates to:
  /// **'https://www.rand.org/pubs/research_reports/RR266.html'**
  String get supportStatistic1Link;

  /// No description provided for @supportStatistic2.
  ///
  /// In en, this message translates to:
  /// **'Every dollar invested in prison education can save four to five dollars in re-incarceration costs.'**
  String get supportStatistic2;

  /// No description provided for @supportStatistic2Source.
  ///
  /// In en, this message translates to:
  /// **'Davis, L.M. et al. (2013). \"How Effective Is Correctional Education, and Where Do We Go from Here?\"'**
  String get supportStatistic2Source;

  /// No description provided for @supportStatistic2Link.
  ///
  /// In en, this message translates to:
  /// **'https://bja.ojp.gov/sites/g/files/xyckuh186/files/Publications/RAND_Correctional-Education-Meta-Analysis.pdf'**
  String get supportStatistic2Link;

  /// No description provided for @supportStatistic3.
  ///
  /// In en, this message translates to:
  /// **'Employment after release is 13% higher among those who participated in educational programs...'**
  String get supportStatistic3;

  /// No description provided for @supportStatistic3Source.
  ///
  /// In en, this message translates to:
  /// **'Bureau of Justice Statistics, U.S. Department of Justice'**
  String get supportStatistic3Source;

  /// No description provided for @supportStatistic3Link.
  ///
  /// In en, this message translates to:
  /// **'https://bjs.ojp.gov/topics/corrections'**
  String get supportStatistic3Link;

  /// No description provided for @supportConsumptionTitle.
  ///
  /// In en, this message translates to:
  /// **'People and Recycling'**
  String get supportConsumptionTitle;

  /// No description provided for @supportConsumptionSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Furthermore, a person throughout their lifetime consumes:'**
  String get supportConsumptionSubtitle;

  /// No description provided for @supportConsumption1.
  ///
  /// In en, this message translates to:
  /// **'Thousands of kilos of food and packaging'**
  String get supportConsumption1;

  /// No description provided for @supportConsumption2.
  ///
  /// In en, this message translates to:
  /// **'Tons of water'**
  String get supportConsumption2;

  /// No description provided for @supportConsumption3.
  ///
  /// In en, this message translates to:
  /// **'Electrical energy equivalent to years of household consumption'**
  String get supportConsumption3;

  /// No description provided for @supportConsumption4.
  ///
  /// In en, this message translates to:
  /// **'Construction materials, clothing, technology...'**
  String get supportConsumption4;

  /// No description provided for @expandToSeeMore.
  ///
  /// In en, this message translates to:
  /// **'Tap to see more'**
  String get expandToSeeMore;

  /// No description provided for @tapToCollapse.
  ///
  /// In en, this message translates to:
  /// **'Tap to collapse'**
  String get tapToCollapse;

  /// No description provided for @supportAppDescription.
  ///
  /// In en, this message translates to:
  /// **'This app was created with years of dedication to improve latent values using modern and mathematical knowledge.'**
  String get supportAppDescription;

  /// No description provided for @supportThanksJacominest.
  ///
  /// In en, this message translates to:
  /// **'Thanks to the Jacominesp association for supporting ways to better value the world\'s resources, starting with those who need hope the most.'**
  String get supportThanksJacominest;

  /// No description provided for @supportThanksWitness.
  ///
  /// In en, this message translates to:
  /// **'Thank you for being a witness to their struggle, María Rosa de Siqueira.'**
  String get supportThanksWitness;

  /// No description provided for @supportSourcesTitle.
  ///
  /// In en, this message translates to:
  /// **'Sources:'**
  String get supportSourcesTitle;

  /// No description provided for @supportSource1.
  ///
  /// In en, this message translates to:
  /// **'RAND Corporation (2013). \"Evaluating the Effectiveness of Correctional Education\"'**
  String get supportSource1;

  /// No description provided for @supportSource2.
  ///
  /// In en, this message translates to:
  /// **'Davis, L.M. et al. (2013). \"How Effective Is Correctional Education, and Where Do We Go from Here?\"'**
  String get supportSource2;

  /// No description provided for @supportSource3.
  ///
  /// In en, this message translates to:
  /// **'Bureau of Justice Statistics, U.S. Department of Justice'**
  String get supportSource3;

  /// No description provided for @continueToWebsiteButton.
  ///
  /// In en, this message translates to:
  /// **'Continue to Website'**
  String get continueToWebsiteButton;

  /// No description provided for @coffeeScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Buy Me a Coffee'**
  String get coffeeScreenTitle;

  /// No description provided for @coffeeMainMessage.
  ///
  /// In en, this message translates to:
  /// **'Support the development of ESTIMAT'**
  String get coffeeMainMessage;

  /// No description provided for @coffeeIntroText.
  ///
  /// In en, this message translates to:
  /// **'Your support helps us continue improving this app and developing new features to help people better understand their latent values and make more conscious decisions.'**
  String get coffeeIntroText;

  /// No description provided for @coffeeWhySupport.
  ///
  /// In en, this message translates to:
  /// **'Why support us?'**
  String get coffeeWhySupport;

  /// No description provided for @coffeeReason1.
  ///
  /// In en, this message translates to:
  /// **'Keep the app free and accessible to everyone'**
  String get coffeeReason1;

  /// No description provided for @coffeeReason2.
  ///
  /// In en, this message translates to:
  /// **'Fund research and development of new features'**
  String get coffeeReason2;

  /// No description provided for @coffeeReason3.
  ///
  /// In en, this message translates to:
  /// **'Support the mathematical and psychological research behind ESTIMAT'**
  String get coffeeReason3;

  /// No description provided for @coffeeReason4.
  ///
  /// In en, this message translates to:
  /// **'Help us reach more people who need better decision-making tools'**
  String get coffeeReason4;

  /// No description provided for @coffeeDonationOptions.
  ///
  /// In en, this message translates to:
  /// **'Choose your support level'**
  String get coffeeDonationOptions;

  /// No description provided for @coffeeSmall.
  ///
  /// In en, this message translates to:
  /// **'Small Coffee'**
  String get coffeeSmall;

  /// No description provided for @coffeeSmallDesc.
  ///
  /// In en, this message translates to:
  /// **'A simple thank you'**
  String get coffeeSmallDesc;

  /// No description provided for @coffeeMedium.
  ///
  /// In en, this message translates to:
  /// **'Large Coffee'**
  String get coffeeMedium;

  /// No description provided for @coffeeMediumDesc.
  ///
  /// In en, this message translates to:
  /// **'Support development'**
  String get coffeeMediumDesc;

  /// No description provided for @coffeeLarge.
  ///
  /// In en, this message translates to:
  /// **'Coffee & Pastry'**
  String get coffeeLarge;

  /// No description provided for @coffeeLargeDesc.
  ///
  /// In en, this message translates to:
  /// **'Boost our research'**
  String get coffeeLargeDesc;

  /// No description provided for @coffeeCustom.
  ///
  /// In en, this message translates to:
  /// **'Custom Amount'**
  String get coffeeCustom;

  /// No description provided for @coffeeCustomDesc.
  ///
  /// In en, this message translates to:
  /// **'Choose your own amount'**
  String get coffeeCustomDesc;

  /// No description provided for @contactTitle.
  ///
  /// In en, this message translates to:
  /// **'Contact & Feedback'**
  String get contactTitle;

  /// No description provided for @contactEmail.
  ///
  /// In en, this message translates to:
  /// **'<EMAIL>'**
  String get contactEmail;

  /// No description provided for @contactEmailDesc.
  ///
  /// In en, this message translates to:
  /// **'Send us your feedback, suggestions, or questions'**
  String get contactEmailDesc;

  /// No description provided for @contactThankYou.
  ///
  /// In en, this message translates to:
  /// **'Thank you for your support!'**
  String get contactThankYou;

  /// No description provided for @contactDevelopmentTeam.
  ///
  /// In en, this message translates to:
  /// **'Development Team'**
  String get contactDevelopmentTeam;

  /// No description provided for @contactResearchTeam.
  ///
  /// In en, this message translates to:
  /// **'Research & Mathematical Foundation'**
  String get contactResearchTeam;

  /// No description provided for @buyNowButton.
  ///
  /// In en, this message translates to:
  /// **'Buy Now'**
  String get buyNowButton;

  /// No description provided for @sendEmailButton.
  ///
  /// In en, this message translates to:
  /// **'Send Email'**
  String get sendEmailButton;

  /// No description provided for @copyEmailButton.
  ///
  /// In en, this message translates to:
  /// **'Copy Email'**
  String get copyEmailButton;

  /// No description provided for @emailCopiedMessage.
  ///
  /// In en, this message translates to:
  /// **'Email copied to clipboard!'**
  String get emailCopiedMessage;

  /// No description provided for @coffeeMenuTitle.
  ///
  /// In en, this message translates to:
  /// **'Support Development'**
  String get coffeeMenuTitle;

  /// No description provided for @saveButton.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get saveButton;

  /// No description provided for @cancelButton.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancelButton;

  /// No description provided for @backButton.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get backButton;

  /// No description provided for @whyMomentsScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Why Moments?'**
  String get whyMomentsScreenTitle;

  /// No description provided for @whyMomentsMenuTitle.
  ///
  /// In en, this message translates to:
  /// **'Why Moments?'**
  String get whyMomentsMenuTitle;

  /// Menu title for Why Hierarchical Organization screen
  ///
  /// In en, this message translates to:
  /// **'Why Hierarchical Organization?'**
  String get whyHierarchicalMenuTitle;

  /// No description provided for @whyHierarchicalScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Why Hierarchical Organization'**
  String get whyHierarchicalScreenTitle;

  /// No description provided for @levelsAndDirectionsScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Levels and Directions'**
  String get levelsAndDirectionsScreenTitle;

  /// No description provided for @skipForNowButton.
  ///
  /// In en, this message translates to:
  /// **'Skip for now'**
  String get skipForNowButton;

  /// No description provided for @noMomentsRecordedYet.
  ///
  /// In en, this message translates to:
  /// **'No moments recorded yet.'**
  String get noMomentsRecordedYet;

  /// Page counter showing current page out of total pages
  ///
  /// In en, this message translates to:
  /// **'{current} / {total}'**
  String pageCounter(int current, int total);

  /// No description provided for @welcomeToEstimatKeyMoments.
  ///
  /// In en, this message translates to:
  /// **'Welcome to Estimat KeyMoments'**
  String get welcomeToEstimatKeyMoments;

  /// No description provided for @estimatKeyMomentsDescription.
  ///
  /// In en, this message translates to:
  /// **'A comprehensive methodology for understanding and analyzing the key moments that shape your life decisions and personal growth.'**
  String get estimatKeyMomentsDescription;

  /// No description provided for @editThisMomentButton.
  ///
  /// In en, this message translates to:
  /// **'Edit this moment'**
  String get editThisMomentButton;

  /// Close button text
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get closeButton;

  /// No description provided for @insertNewMomentButton.
  ///
  /// In en, this message translates to:
  /// **'Insert a new moment'**
  String get insertNewMomentButton;

  /// No description provided for @viewAllMomentsButton.
  ///
  /// In en, this message translates to:
  /// **'View All Moments'**
  String get viewAllMomentsButton;

  /// No description provided for @momentsHistoryTitle.
  ///
  /// In en, this message translates to:
  /// **'Moments History'**
  String get momentsHistoryTitle;

  /// Title for moment saved dialog
  ///
  /// In en, this message translates to:
  /// **'{momentType} Saved'**
  String momentSavedTitle(String momentType);

  /// No description provided for @momentSavedMessage.
  ///
  /// In en, this message translates to:
  /// **'Your moment has been saved successfully.'**
  String get momentSavedMessage;

  /// No description provided for @guardianDisplayName.
  ///
  /// In en, this message translates to:
  /// **'GUARDIAN'**
  String get guardianDisplayName;

  /// No description provided for @guardianDisplayNameSmall.
  ///
  /// In en, this message translates to:
  /// **'Nurturer'**
  String get guardianDisplayNameSmall;

  /// No description provided for @warriorDisplayName.
  ///
  /// In en, this message translates to:
  /// **'WARRIOR'**
  String get warriorDisplayName;

  /// No description provided for @warriorDisplayNameSmall.
  ///
  /// In en, this message translates to:
  /// **'Releaser'**
  String get warriorDisplayNameSmall;

  /// No description provided for @versatileDisplayName.
  ///
  /// In en, this message translates to:
  /// **'VERSATILE'**
  String get versatileDisplayName;

  /// No description provided for @versatileDisplayNameSmall.
  ///
  /// In en, this message translates to:
  /// **'SelfSeer'**
  String get versatileDisplayNameSmall;

  /// No description provided for @funDisplayName.
  ///
  /// In en, this message translates to:
  /// **'FUNNY'**
  String get funDisplayName;

  /// No description provided for @funDisplayNameSmall.
  ///
  /// In en, this message translates to:
  /// **'Enthusiastic'**
  String get funDisplayNameSmall;

  /// No description provided for @strategistDisplayName.
  ///
  /// In en, this message translates to:
  /// **'STRATEGIST'**
  String get strategistDisplayName;

  /// No description provided for @strategistDisplayNameSmall.
  ///
  /// In en, this message translates to:
  /// **'Analyst'**
  String get strategistDisplayNameSmall;

  /// No description provided for @tacticalDisplayName.
  ///
  /// In en, this message translates to:
  /// **'TACTICIAN'**
  String get tacticalDisplayName;

  /// No description provided for @tacticalDisplayNameSmall.
  ///
  /// In en, this message translates to:
  /// **'Synthesizer'**
  String get tacticalDisplayNameSmall;

  /// No description provided for @altruistDisplayName.
  ///
  /// In en, this message translates to:
  /// **'ALTRUISTIC'**
  String get altruistDisplayName;

  /// No description provided for @altruistDisplayNameSmall.
  ///
  /// In en, this message translates to:
  /// **'Empathetic'**
  String get altruistDisplayNameSmall;

  /// No description provided for @collaboratorDisplayName.
  ///
  /// In en, this message translates to:
  /// **'COLLABORATOR'**
  String get collaboratorDisplayName;

  /// No description provided for @collaboratorDisplayNameSmall.
  ///
  /// In en, this message translates to:
  /// **'Diplomat'**
  String get collaboratorDisplayNameSmall;

  /// No description provided for @guardianDescription.
  ///
  /// In en, this message translates to:
  /// **'Recognize: You recognize, consume, ingest.\\nStore: You rest, store, and metabolize.'**
  String get guardianDescription;

  /// No description provided for @warriorDescription.
  ///
  /// In en, this message translates to:
  /// **'Discard: You discard, flee, inhibit.\\nExecute: Fight, attack, and struggle.'**
  String get warriorDescription;

  /// No description provided for @versatileDescription.
  ///
  /// In en, this message translates to:
  /// **'Self-Observe: You highlight negative information, feel pain, observe errors.\\nSelf-Transform: You reduce your errors and adapt stimuli.'**
  String get versatileDescription;

  /// No description provided for @funDescription.
  ///
  /// In en, this message translates to:
  /// **'Self-Motivate: You highlight positive information, feel pleasure.\\nSelf-Enjoy: I enhance successes and contrast attitudes and ideas.'**
  String get funDescription;

  /// No description provided for @strategistDescription.
  ///
  /// In en, this message translates to:
  /// **'Analyze: You review trends, ask if something could be false, analyze.\\nPredict: You predict what is most probable and create hypotheses.'**
  String get strategistDescription;

  /// No description provided for @tacticalDescription.
  ///
  /// In en, this message translates to:
  /// **'Simplify: You generalize, compare the easiest, fastest way.\\nTrack: You search, hunt, and trace.'**
  String get tacticalDescription;

  /// No description provided for @altruistDescription.
  ///
  /// In en, this message translates to:
  /// **'Empathize: You empathize with what is important for others.\\nDeliberate: You consider myself and the needs of as many people as possible, practicing efficiency altruism.'**
  String get altruistDescription;

  /// No description provided for @collaboratorDescription.
  ///
  /// In en, this message translates to:
  /// **'Negotiate: You help to understand, communicate.\\nCollaborate: You cooperate toward shared goals.'**
  String get collaboratorDescription;

  /// Default text when description is not available
  ///
  /// In en, this message translates to:
  /// **'Description not available.'**
  String get descriptionNotAvailable;

  /// No description provided for @exportDataTitle.
  ///
  /// In en, this message translates to:
  /// **'Export Data'**
  String get exportDataTitle;

  /// No description provided for @exportOptionsTitle.
  ///
  /// In en, this message translates to:
  /// **'Export Options'**
  String get exportOptionsTitle;

  /// No description provided for @exportToCSVTitle.
  ///
  /// In en, this message translates to:
  /// **'Export to CSV'**
  String get exportToCSVTitle;

  /// No description provided for @exportToCSVSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Save your moments data as a CSV file'**
  String get exportToCSVSubtitle;

  /// No description provided for @exportSuccessfulTitle.
  ///
  /// In en, this message translates to:
  /// **'Export Successful!'**
  String get exportSuccessfulTitle;

  /// Label showing where file was saved
  ///
  /// In en, this message translates to:
  /// **'File saved to: {filePath}'**
  String fileSavedToLabel(String filePath);

  /// No description provided for @viewFileButton.
  ///
  /// In en, this message translates to:
  /// **'View File'**
  String get viewFileButton;

  /// No description provided for @csvDataExportedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'CSV data exported successfully:'**
  String get csvDataExportedSuccessfully;

  /// No description provided for @exportFailedTitle.
  ///
  /// In en, this message translates to:
  /// **'Export Failed'**
  String get exportFailedTitle;

  /// No description provided for @noMomentsToExport.
  ///
  /// In en, this message translates to:
  /// **'No moments to export.'**
  String get noMomentsToExport;

  /// Error message when export fails
  ///
  /// In en, this message translates to:
  /// **'Error exporting data: {error}'**
  String errorExportingData(String error);

  /// Error message when file is not found
  ///
  /// In en, this message translates to:
  /// **'File not found: {filePath}'**
  String fileNotFound(String filePath);

  /// Error message when file cannot be opened
  ///
  /// In en, this message translates to:
  /// **'Error opening file: {error}'**
  String errorOpeningFile(String error);

  /// Label for factorial comparison
  ///
  /// In en, this message translates to:
  /// **'Factorial Comparison: {comparisonType}'**
  String factorialComparisonLabel(String comparisonType);

  /// No description provided for @improvementLabel.
  ///
  /// In en, this message translates to:
  /// **'Improvement'**
  String get improvementLabel;

  /// No description provided for @worseningLabel.
  ///
  /// In en, this message translates to:
  /// **'Worsening'**
  String get worseningLabel;

  /// Label for factorial slider value
  ///
  /// In en, this message translates to:
  /// **'Factorial Slider Value: {value}'**
  String factorialSliderValueLabel(String value);

  /// Label for factorial multiplier
  ///
  /// In en, this message translates to:
  /// **'Factorial Multiplier: {value}'**
  String factorialMultiplierLabel(String value);

  /// No description provided for @whyRegisterMomentsTitle.
  ///
  /// In en, this message translates to:
  /// **'Why register moments objectively?'**
  String get whyRegisterMomentsTitle;

  /// No description provided for @whyRegisterMomentsContent.
  ///
  /// In en, this message translates to:
  /// **'Your emotional peaks and valleys are not just loose data: they are like the coordinates of your internal compass. Recording your motivation (how driven you feel) and your satisfaction (how fulfilled you feel) gives you a clear map to plan goals, routines or tasks that really add to your life.'**
  String get whyRegisterMomentsContent;

  /// No description provided for @whenNoticeMoreTitle.
  ///
  /// In en, this message translates to:
  /// **'When is this most noticeable?'**
  String get whenNoticeMoreTitle;

  /// No description provided for @whenNoticeMoreContent.
  ///
  /// In en, this message translates to:
  /// **'When you\'re in an emotional low. In those moments, your brain tricks you with thoughts like: \"I never get anything right.\" \"There\'s nothing that motivates me.\" Although this isn\'t true, memory bias—that tendency to remember the negative or most recent—makes you feel that way. 📈 Keeping an objective record of your high moments gives you real proof of who you are when you\'re well, so you don\'t get lost in the fog when you\'re down.'**
  String get whenNoticeMoreContent;

  /// No description provided for @highMotivationSatisfactionTitle.
  ///
  /// In en, this message translates to:
  /// **'High motivation and high satisfaction'**
  String get highMotivationSatisfactionTitle;

  /// No description provided for @highMotivationSatisfactionContent.
  ///
  /// In en, this message translates to:
  /// **'Quick question: What was the most significant thing that happened to you this month? If you had a daily record of your emotions, would you answer the same? Probably not. And there\'s a reason: Memory bias: According to Kahneman and Tversky (1979), our brain tends to give more weight to what\'s recent or intense (this is called the peak-end rule). That\'s why we sometimes forget valuable moments that weren\'t so \"noisy\". Solution: Writing down your peaks compensates for this mental trap and shows you the complete picture.'**
  String get highMotivationSatisfactionContent;

  /// No description provided for @evolutionaryViewTitle.
  ///
  /// In en, this message translates to:
  /// **'Evolutionary perspective'**
  String get evolutionaryViewTitle;

  /// No description provided for @evolutionaryViewContent.
  ///
  /// In en, this message translates to:
  /// **'Strong positive emotions—like pride or fulfillment—are signals of opportunities: a relationship that works, a personal achievement or a decision that aligns you with what you want. Our ancestors survived better if they remembered these moments to repeat them. Your brain isn\'t designed just to make you happy, but to help you thrive. If you know what motivates you, you can seek it more often.'**
  String get evolutionaryViewContent;

  /// No description provided for @practiceEstimatTitle.
  ///
  /// In en, this message translates to:
  /// **'Practice with ESTIMAT:'**
  String get practiceEstimatTitle;

  /// No description provided for @practiceEstimatContent.
  ///
  /// In en, this message translates to:
  /// **'Imagine you write down: ✍️ \"I felt powerful explaining my emotions in a workshop without being judged.\" 🔁 When reviewing several similar entries, ESTIMAT shows you: High motivation when you express what you feel. High satisfaction with active listening and validation. 🧭 What do you do with this? Look for or create more situations like this (workshops, talks with understanding friends). Use it as an anchor when you feel lost.'**
  String get practiceEstimatContent;

  /// No description provided for @highMotivationLowSatisfactionTitle.
  ///
  /// In en, this message translates to:
  /// **'High Motivation + Low Satisfaction'**
  String get highMotivationLowSatisfactionTitle;

  /// No description provided for @highMotivationLowSatisfactionContent.
  ///
  /// In en, this message translates to:
  /// **'Did it happen to you to be really excited about something and then feel a \'meh\'? According to Gilbert and Wilson, we tend to overestimate future happiness by 40-60%.'**
  String get highMotivationLowSatisfactionContent;

  /// No description provided for @lowMotivationHighSatisfactionTitle.
  ///
  /// In en, this message translates to:
  /// **'Low Motivation + High Satisfaction'**
  String get lowMotivationHighSatisfactionTitle;

  /// No description provided for @lowMotivationHighSatisfactionContent.
  ///
  /// In en, this message translates to:
  /// **'Have you ever dragged yourself to do something and ended up surprising yourself with how good you felt? The University of British Columbia showed that people underestimate their enjoyment of exercise by 20-30%.'**
  String get lowMotivationHighSatisfactionContent;

  /// No description provided for @lowMotivationLowSatisfactionTitle.
  ///
  /// In en, this message translates to:
  /// **'Low Motivation + Low Satisfaction'**
  String get lowMotivationLowSatisfactionTitle;

  /// No description provided for @lowMotivationLowSatisfactionContent.
  ///
  /// In en, this message translates to:
  /// **'And those days when there is no desire or pleasure? Those lows may be seedbeds for your best ideas. Students who practiced reflective self-assessment showed a 20% increase in their performance.'**
  String get lowMotivationLowSatisfactionContent;

  /// Header for evolutive functions column in latent values screen
  ///
  /// In en, this message translates to:
  /// **'Evolutive Functions'**
  String get evolutiveFunctionsHeader;

  /// Description for Guardian latent value
  ///
  /// In en, this message translates to:
  /// **'Recognize: You recognize, consume, ingest.\nStore: You rest, store, and metabolize.'**
  String get valueDescriptionGuardian;

  /// Description for Warrior latent value
  ///
  /// In en, this message translates to:
  /// **'Discard: You discard, flee, inhibit.\nExecute: Fight, attack, and struggle.'**
  String get valueDescriptionWarrior;

  /// Description for Versatile latent value
  ///
  /// In en, this message translates to:
  /// **'Self-Observe: You highlight negative information, feel pain, observe errors.\nSelf-Transform: You reduce your errors and adapt stimuli.'**
  String get valueDescriptionVersatile;

  /// Description for Funny latent value
  ///
  /// In en, this message translates to:
  /// **'Self-Motivate: You highlight positive information, feel pleasure.\nSelf-Enjoy: I enhance successes and contrast attitudes and ideas.'**
  String get valueDescriptionFunny;

  /// Description for Strategist latent value
  ///
  /// In en, this message translates to:
  /// **'Analyze: You review trends, ask if something could be false, analyze.\nPredict: You predict what is most probable and create hypotheses.'**
  String get valueDescriptionStrategist;

  /// Description for Tactician latent value
  ///
  /// In en, this message translates to:
  /// **'Simplify: You generalize, compare the easiest, fastest way.\nTrack: You search, hunt, and trace.'**
  String get valueDescriptionTactician;

  /// Description for Altruistic latent value
  ///
  /// In en, this message translates to:
  /// **'Empathize: You empathize with what is important for others.\nDeliberate: You consider myself and the needs of as many people as possible, practicing efficiency altruism.'**
  String get valueDescriptionAltruistic;

  /// Description for Collaborator latent value
  ///
  /// In en, this message translates to:
  /// **'Negotiate: You help to understand, communicate.\nCollaborate: You cooperate toward shared goals.'**
  String get valueDescriptionCollaborator;

  /// Title for latent values section
  ///
  /// In en, this message translates to:
  /// **'Latent Values'**
  String get latentValuesTitle;

  /// Title for life possibilities chart section
  ///
  /// In en, this message translates to:
  /// **'Life Possibilities Chart'**
  String get lifePossibilitiesChartTitle;

  /// Label for period selection
  ///
  /// In en, this message translates to:
  /// **'Period:'**
  String get periodLabel;

  /// Filter option for last 7 days
  ///
  /// In en, this message translates to:
  /// **'Last 7 Days'**
  String get last7DaysFilter;

  /// Filter option for custom date range
  ///
  /// In en, this message translates to:
  /// **'Custom Range'**
  String get customRangeFilter;

  /// Title for custom date range filter section
  ///
  /// In en, this message translates to:
  /// **'Custom Date Range Filter'**
  String get customDateRangeFilterTitle;

  /// Label for start date selection
  ///
  /// In en, this message translates to:
  /// **'Start Date:'**
  String get startDateLabel;

  /// Label for end date selection
  ///
  /// In en, this message translates to:
  /// **'End Date:'**
  String get endDateLabel;

  /// Hint text for start date selection
  ///
  /// In en, this message translates to:
  /// **'Select start date'**
  String get selectStartDateHint;

  /// Hint text for end date selection
  ///
  /// In en, this message translates to:
  /// **'Select end date'**
  String get selectEndDateHint;

  /// Reset button text
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get resetButton;

  /// Label indicating custom range is active
  ///
  /// In en, this message translates to:
  /// **'Custom range active'**
  String get customRangeActiveLabel;

  /// Checkbox label for not showing onboarding again
  ///
  /// In en, this message translates to:
  /// **'Don\'t show this again'**
  String get dontShowAgainLabel;

  /// Menu title for user instructions
  ///
  /// In en, this message translates to:
  /// **'User Instructions'**
  String get userInstructionsMenuTitle;

  /// Title for user instructions screen
  ///
  /// In en, this message translates to:
  /// **'How to Use ESTIMAT KeyMoments'**
  String get userInstructionsScreenTitle;

  /// Welcome title in user instructions
  ///
  /// In en, this message translates to:
  /// **'Welcome to Your Personal Growth Journey'**
  String get instructionsWelcomeTitle;

  /// No description provided for @instructionsWelcomeContent.
  ///
  /// In en, this message translates to:
  /// **'ESTIMAT KeyMoments helps you understand your personal values and decision patterns by analyzing your most significant life experiences. This guide will show you how to use the app effectively.'**
  String get instructionsWelcomeContent;

  /// Title for step 1 in user instructions
  ///
  /// In en, this message translates to:
  /// **'Step 1: Start with Your Extreme Moments'**
  String get instructionsStep1Title;

  /// No description provided for @instructionsStep1Content.
  ///
  /// In en, this message translates to:
  /// **'Begin by identifying your highest and lowest experiences - these are your reference points that establish your personal limits and provide the foundation for better recommendations.\n\n• **Highest moments**: Times when you felt most motivated and satisfied\n• **Lowest moments**: Times when you felt least motivated and satisfied\n\nThese extremes help the app understand your personal range and provide more accurate insights.'**
  String get instructionsStep1Content;

  /// Title for step 2 in user instructions
  ///
  /// In en, this message translates to:
  /// **'Step 2: Navigate the App Interface'**
  String get instructionsStep2Title;

  /// No description provided for @instructionsStep2Content.
  ///
  /// In en, this message translates to:
  /// **'**Main Screen**: Record new moments by selecting the type (improves/worsens motivation or satisfaction)\n\n**Moment Details**: Describe your experience and distribute percentages across four levels:\n• Elemental (physical responses)\n• Personal (individual experiences)\n• Informational (intellectual processes)\n• Social (interpersonal connections)\n\n**Summary Screen**: View your patterns and latent values analysis\n\n**Menu**: Access presentations, support, and additional features'**
  String get instructionsStep2Content;

  /// Title for step 3 in user instructions
  ///
  /// In en, this message translates to:
  /// **'Step 3: Record Moments Consistently'**
  String get instructionsStep3Title;

  /// No description provided for @instructionsStep3Content.
  ///
  /// In en, this message translates to:
  /// **'For best results:\n\n• Record moments when they\'re fresh in your memory\n• Be honest about the impact on your motivation and satisfaction\n• Provide specific evidence for your percentage distributions\n• Include both positive and negative experiences\n• Aim to record at least one moment per week'**
  String get instructionsStep3Content;

  /// Title for step 4 in user instructions
  ///
  /// In en, this message translates to:
  /// **'Step 4: Understand Your Latent Values'**
  String get instructionsStep4Title;

  /// No description provided for @instructionsStep4Content.
  ///
  /// In en, this message translates to:
  /// **'The app identifies eight latent values based on your moment patterns:\n\n**Elemental Level**: Guardian, Warrior\n**Personal Level**: Versatile, Fun\n**Informational Level**: Strategist, Tactical\n**Social Level**: Altruist, Collaborator\n\nThese values represent your core motivational drivers and help you understand what truly matters to you.'**
  String get instructionsStep4Content;

  /// Title for future AI features section
  ///
  /// In en, this message translates to:
  /// **'Future AI Integration'**
  String get instructionsFutureTitle;

  /// No description provided for @instructionsFutureContent.
  ///
  /// In en, this message translates to:
  /// **'ESTIMAT will continuously improve with Large Language Model (LLM) integration to provide:\n\n• **Personalized insights** based on your unique patterns\n• **Contextual recommendations** for decision-making\n• **Predictive analysis** of how choices might affect your well-being\n• **Adaptive questioning** to help you explore your values more deeply\n\nThe more you use the app, the better it becomes at understanding and supporting your personal growth journey.'**
  String get instructionsFutureContent;

  /// Title for pro tips section
  ///
  /// In en, this message translates to:
  /// **'Pro Tips for Success'**
  String get instructionsTipsTitle;

  /// No description provided for @instructionsTipsContent.
  ///
  /// In en, this message translates to:
  /// **'• **Start with extremes**: Your highest and lowest moments provide the most valuable reference points\n• **Be specific**: Detailed descriptions lead to better insights\n• **Review regularly**: Check your summary screen weekly to spot patterns\n• **Trust the process**: Patterns become clearer with more data\n• **Use for decisions**: Apply your latent values insights to future choices'**
  String get instructionsTipsContent;

  /// Menu title for changing language
  ///
  /// In en, this message translates to:
  /// **'Change Language'**
  String get changeLanguageMenuTitle;

  /// Message shown when language is changed
  ///
  /// In en, this message translates to:
  /// **'Language changed successfully'**
  String get languageChangedMessage;

  /// Label showing number of moments displayed
  ///
  /// In en, this message translates to:
  /// **'Showing {count} moment{plural}'**
  String showingMomentsLabel(int count, String plural);

  /// Title for why focus on moments section
  ///
  /// In en, this message translates to:
  /// **'Why Focus on Moments?'**
  String get whyFocusOnMomentsTitle;

  /// Content explaining why focus on moments
  ///
  /// In en, this message translates to:
  /// **'Key moments are the building blocks of our decision-making process. By understanding these moments, we can:\n\n• Identify patterns in our behavior\n• Understand what truly motivates us\n• Recognize what brings us satisfaction\n• Make more conscious choices\n• Develop better self-awareness'**
  String get whyFocusOnMomentsContent;

  /// Title for discovering latent values section
  ///
  /// In en, this message translates to:
  /// **'Discovering Latent Values'**
  String get discoveringLatentValuesTitle;

  /// Content explaining discovering latent values
  ///
  /// In en, this message translates to:
  /// **'Your latent values emerge from the combination of:\n\n• **Level percentages** (how you distribute across the four levels)\n• **Directional focus** (inward vs outward orientation)\n• **Impact type** (improves vs worsens)\n\nThese values reveal your core strengths: {guardianLabel}, {warriorLabel}, {versatileLabel}, {funLabel}, and others.'**
  String discoveringLatentValuesContent(
    String guardianLabel,
    String warriorLabel,
    String versatileLabel,
    String funLabel,
  );

  /// Title for understanding latent values section
  ///
  /// In en, this message translates to:
  /// **'Understanding Latent Values'**
  String get understandingLatentValuesTitle;

  /// Content explaining understanding latent values
  ///
  /// In en, this message translates to:
  /// **'Your latent values emerge from how you distribute your focus across the four hierarchical levels and whether you tend toward inward or outward orientation. Each combination reveals core strengths and natural tendencies that guide your decision-making and life satisfaction.'**
  String get understandingLatentValuesContent;

  /// Title for the directions focus section
  ///
  /// In en, this message translates to:
  /// **'Directions Focus'**
  String get directionsFocusTitle;

  /// Title for why hierarchical organization section
  ///
  /// In en, this message translates to:
  /// **'Why Hierarchical Organization of Life Moments Might Matter'**
  String get whyHierarchicalOrganizationTitle;

  /// Title for data visualization and bias reduction section
  ///
  /// In en, this message translates to:
  /// **'Data Visualization & Bias Reduction'**
  String get dataVisualizationBiasReductionTitle;

  /// Title for experimental evidence section
  ///
  /// In en, this message translates to:
  /// **'Experimental Evidence'**
  String get experimentalEvidenceTitle;

  /// Title for evolutionary psychology perspective section
  ///
  /// In en, this message translates to:
  /// **'Evolutionary Psychology Perspective'**
  String get evolutionaryPsychologyPerspectiveTitle;

  /// No description provided for @recognizeYourMomentOrientationLevel.
  ///
  /// In en, this message translates to:
  /// **'Recognize your moment, your orientation and level'**
  String get recognizeYourMomentOrientationLevel;

  /// No description provided for @recognizeYourMoment.
  ///
  /// In en, this message translates to:
  /// **'Recognize your moment'**
  String get recognizeYourMoment;

  /// No description provided for @organizeTheMomentAndLookForEvidence.
  ///
  /// In en, this message translates to:
  /// **'Now let\'s look at the distribution of information at the moment and look for evidence.'**
  String get organizeTheMomentAndLookForEvidence;

  /// No description provided for @orientationTitle.
  ///
  /// In en, this message translates to:
  /// **'Orientation'**
  String get orientationTitle;

  /// No description provided for @orientationQuestion.
  ///
  /// In en, this message translates to:
  /// **'Was your moment more focused on changing internally or externally?'**
  String get orientationQuestion;

  /// Title for the 3 levels section that encompasses all level comparisons
  ///
  /// In en, this message translates to:
  /// **'Levels'**
  String get threeLevelsTitle;

  /// Description of the 3 levels concept
  ///
  /// In en, this message translates to:
  /// **'Hierarchical analysis of your moment across the four levels of human experience'**
  String get threeLevelsDescription;

  /// No description provided for @pieStepElementalPersonalTitle.
  ///
  /// In en, this message translates to:
  /// **'3 Levels: Elemental vs Personal'**
  String get pieStepElementalPersonalTitle;

  /// No description provided for @pieStepElementalPersonalDescription.
  ///
  /// In en, this message translates to:
  /// **'For your moment you focused more on your body state or on your personal interests/emotions?'**
  String get pieStepElementalPersonalDescription;

  /// No description provided for @pieStepPersonalInformationalTitle.
  ///
  /// In en, this message translates to:
  /// **'3 Levels: Personal vs Informational'**
  String get pieStepPersonalInformationalTitle;

  /// No description provided for @pieStepPersonalInformationalDescription.
  ///
  /// In en, this message translates to:
  /// **'For your moment you focused more on your personal interests/emotions or on your gathering and processing information?'**
  String get pieStepPersonalInformationalDescription;

  /// No description provided for @pieStepInformationalSocialTitle.
  ///
  /// In en, this message translates to:
  /// **'3 Levels: Informational vs Social'**
  String get pieStepInformationalSocialTitle;

  /// No description provided for @pieStepInformationalSocialDescription.
  ///
  /// In en, this message translates to:
  /// **'For your moment you focused more on analyzing information or on connecting with others?'**
  String get pieStepInformationalSocialDescription;

  /// No description provided for @pieStepEvidenceQuestion.
  ///
  /// In en, this message translates to:
  /// **'What evidence or variables influenced your choice?'**
  String get pieStepEvidenceQuestion;

  /// No description provided for @whyMomentsHeaderSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Analyze your moments, know your patterns'**
  String get whyMomentsHeaderSubtitle;

  /// No description provided for @whyRegisterMomentsObjectivelyTitle.
  ///
  /// In en, this message translates to:
  /// **'Why register moments objectively?'**
  String get whyRegisterMomentsObjectivelyTitle;

  /// No description provided for @whyRegisterMomentsObjectivelyContent.
  ///
  /// In en, this message translates to:
  /// **'Your emotional peaks and valleys are not just loose data: they are like the coordinates of your internal compass. Recording your motivation (how driven you feel) and your satisfaction (how fulfilled you feel) gives you a clear map to plan goals, routines or tasks that really add to your life.'**
  String get whyRegisterMomentsObjectivelyContent;

  /// No description provided for @whyRegisterMomentsObjectivelyHighlight.
  ///
  /// In en, this message translates to:
  /// **'📈 Keeping a record of your motivation and satisfaction peaks serves to compensate for emotional distortion. It allows you to have evidence of who you are when you\'re well, so you don\'t lose sight of yourself when you\'re not.'**
  String get whyRegisterMomentsObjectivelyHighlight;

  /// No description provided for @highMotivationHighSatisfactionTitle.
  ///
  /// In en, this message translates to:
  /// **'High Motivation + High Satisfaction'**
  String get highMotivationHighSatisfactionTitle;

  /// No description provided for @researchInfoBoxTitle.
  ///
  /// In en, this message translates to:
  /// **'🧠 Research'**
  String get researchInfoBoxTitle;

  /// No description provided for @researchInfoBoxContent.
  ///
  /// In en, this message translates to:
  /// **'According to Kahneman and Tversky (1979), our brain tends to give more weight to what is recent or intense (peak-end rule). That\'s why we sometimes forget valuable moments that weren\'t so \"noisy\".'**
  String get researchInfoBoxContent;

  /// No description provided for @evolutionaryViewInfoBoxTitle.
  ///
  /// In en, this message translates to:
  /// **'🔬 Evolutionary View'**
  String get evolutionaryViewInfoBoxTitle;

  /// No description provided for @evolutionaryViewInfoBoxContent.
  ///
  /// In en, this message translates to:
  /// **'Strong positive emotions signal adaptive opportunities: successful relationships, purpose-aligned decisions, social or personal achievements.'**
  String get evolutionaryViewInfoBoxContent;

  /// No description provided for @practiceEstimatInfoBoxTitle.
  ///
  /// In en, this message translates to:
  /// **'✍️ Practice with ESTIMAT'**
  String get practiceEstimatInfoBoxTitle;

  /// No description provided for @practiceEstimatInfoBoxContent.
  ///
  /// In en, this message translates to:
  /// **'By reviewing multiple entries, ESTIMAT shows you patterns and helps you replicate those moments of high motivation and satisfaction.'**
  String get practiceEstimatInfoBoxContent;

  /// No description provided for @highMotivationLowSatisfactionIntro.
  ///
  /// In en, this message translates to:
  /// **'Have you ever been really excited about something and then felt a \"meh\"? 📱'**
  String get highMotivationLowSatisfactionIntro;

  /// No description provided for @impactBiasInfoBoxTitle.
  ///
  /// In en, this message translates to:
  /// **'📊 Impact Bias'**
  String get impactBiasInfoBoxTitle;

  /// No description provided for @impactBiasInfoBoxContent.
  ///
  /// In en, this message translates to:
  /// **'According to Gilbert and Wilson, we tend to overestimate future happiness by'**
  String get impactBiasInfoBoxContent;

  /// No description provided for @practiceInfoBoxTitle.
  ///
  /// In en, this message translates to:
  /// **'🎯 Practice'**
  String get practiceInfoBoxTitle;

  /// No description provided for @practiceInfoBoxContent.
  ///
  /// In en, this message translates to:
  /// **'• Simulate: Before diving in, imagine colors, smells, sounds\n• Prediction vs. reality: Write down how much you think you\'ll enjoy it\n• Adjust: If you expected +3 and it was +1, rethink if it\'s worth repeating'**
  String get practiceInfoBoxContent;

  /// No description provided for @lowMotivationHighSatisfactionIntro.
  ///
  /// In en, this message translates to:
  /// **'Have you ever dragged yourself to do something and ended up surprised by how good you felt? ⛈️😊'**
  String get lowMotivationHighSatisfactionIntro;

  /// No description provided for @pleasureUnderestimationInfoBoxTitle.
  ///
  /// In en, this message translates to:
  /// **'🏃‍♂️ Pleasure Underestimation'**
  String get pleasureUnderestimationInfoBoxTitle;

  /// No description provided for @pleasureUnderestimationInfoBoxContent.
  ///
  /// In en, this message translates to:
  /// **'The University of British Columbia showed that people underestimate their enjoyment of exercise by'**
  String get pleasureUnderestimationInfoBoxContent;

  /// No description provided for @effortParadoxInfoBoxTitle.
  ///
  /// In en, this message translates to:
  /// **'🧬 Effort Paradox'**
  String get effortParadoxInfoBoxTitle;

  /// No description provided for @effortParadoxInfoBoxContent.
  ///
  /// In en, this message translates to:
  /// **'Every drop of effort is exchanged for an extra plus of satisfaction. That \"burst of joy\" is your brain saying \"Well done!\"'**
  String get effortParadoxInfoBoxContent;

  /// No description provided for @lowMotivationLowSatisfactionIntro.
  ///
  /// In en, this message translates to:
  /// **'And those days when there\'s no desire or pleasure? 😔 Those lows might be seedbeds for your best ideas.'**
  String get lowMotivationLowSatisfactionIntro;

  /// No description provided for @reflectionPowerInfoBoxTitle.
  ///
  /// In en, this message translates to:
  /// **'📈 Power of Reflection'**
  String get reflectionPowerInfoBoxTitle;

  /// No description provided for @reflectionPowerInfoBoxContent.
  ///
  /// In en, this message translates to:
  /// **'Students who practiced reflective self-evaluation showed an increase of'**
  String get reflectionPowerInfoBoxContent;

  /// No description provided for @practiceEstimatLowInfoBoxTitle.
  ///
  /// In en, this message translates to:
  /// **'🎯 Practice with ESTIMAT'**
  String get practiceEstimatLowInfoBoxTitle;

  /// No description provided for @practiceEstimatLowInfoBoxContent.
  ///
  /// In en, this message translates to:
  /// **'• Record your low: Write without filters how you feel\n• Review your log: ESTIMAT will show you patterns\n• Do something small: A walk, a song, three gratitudes'**
  String get practiceEstimatLowInfoBoxContent;

  /// No description provided for @generalOverviewTitle.
  ///
  /// In en, this message translates to:
  /// **'The General Overview'**
  String get generalOverviewTitle;

  /// No description provided for @generalOverviewIntro.
  ///
  /// In en, this message translates to:
  /// **'Recording your emotions is not just a hobby—it\'s a tool to know yourself and get the most out of your brain.'**
  String get generalOverviewIntro;

  /// No description provided for @memoryBiasStatTitle.
  ///
  /// In en, this message translates to:
  /// **'🧠 Memory bias'**
  String get memoryBiasStatTitle;

  /// No description provided for @memoryBiasStatContent.
  ///
  /// In en, this message translates to:
  /// **'We overvalue what\'s recent or intense'**
  String get memoryBiasStatContent;

  /// No description provided for @impactBiasStatTitle.
  ///
  /// In en, this message translates to:
  /// **'🎯 Impact bias'**
  String get impactBiasStatTitle;

  /// No description provided for @impactBiasStatContent.
  ///
  /// In en, this message translates to:
  /// **'We overestimate future happiness'**
  String get impactBiasStatContent;

  /// No description provided for @underestimationStatTitle.
  ///
  /// In en, this message translates to:
  /// **'💪 Underestimation'**
  String get underestimationStatTitle;

  /// No description provided for @underestimationStatContent.
  ///
  /// In en, this message translates to:
  /// **'We enjoy exercise more than we think'**
  String get underestimationStatContent;

  /// No description provided for @recoveryStatTitle.
  ///
  /// In en, this message translates to:
  /// **'🔄 Recovery'**
  String get recoveryStatTitle;

  /// No description provided for @recoveryStatContent.
  ///
  /// In en, this message translates to:
  /// **'Reflecting gives you more motivated days'**
  String get recoveryStatContent;

  /// No description provided for @generalOverviewConclusion.
  ///
  /// In en, this message translates to:
  /// **'What small step can you take today to better understand your emotions? Try writing down a peak and a valley—the results might surprise you. ✨'**
  String get generalOverviewConclusion;

  /// No description provided for @whyHierarchicalHeaderSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Why Hierarchical Organization of Life Moments Might Matter'**
  String get whyHierarchicalHeaderSubtitle;

  /// No description provided for @whyHierarchicalImportantNote.
  ///
  /// In en, this message translates to:
  /// **'Important: If you\'re already experiencing consistent well-being and deep satisfaction, additional organization is likely unnecessary. This approach seems most relevant during transitions, complex decisions, or persistent dissatisfaction.'**
  String get whyHierarchicalImportantNote;

  /// No description provided for @informationTheoryPerspectiveTitle.
  ///
  /// In en, this message translates to:
  /// **'Information Theory Perspective'**
  String get informationTheoryPerspectiveTitle;

  /// No description provided for @debunkingCommonMythsTitle.
  ///
  /// In en, this message translates to:
  /// **'Debunking Common Myths'**
  String get debunkingCommonMythsTitle;

  /// No description provided for @selfPerceptionBiasesTitle.
  ///
  /// In en, this message translates to:
  /// **'The Problem of Self-Perception Biases'**
  String get selfPerceptionBiasesTitle;

  /// No description provided for @visualProportionsTitle.
  ///
  /// In en, this message translates to:
  /// **'Advantages of Visual Proportions'**
  String get visualProportionsTitle;

  /// No description provided for @statsVsIntuitionTitle.
  ///
  /// In en, this message translates to:
  /// **'Personal Statistics vs. Intuition'**
  String get statsVsIntuitionTitle;

  /// No description provided for @memoryHierarchyTitle.
  ///
  /// In en, this message translates to:
  /// **'Memory Hierarchy Experiments'**
  String get memoryHierarchyTitle;

  /// No description provided for @decisionFatigueTitle.
  ///
  /// In en, this message translates to:
  /// **'Decision Fatigue Studies'**
  String get decisionFatigueTitle;

  /// No description provided for @millerNumberTitle.
  ///
  /// In en, this message translates to:
  /// **'Miller\'s Magical Number'**
  String get millerNumberTitle;

  /// No description provided for @availabilityBiasContent.
  ///
  /// In en, this message translates to:
  /// **'Availability Bias (Tversky & Kahneman, 1973): We\'re likely to disproportionately remember recent or emotional events, which could distort our perception of life patterns.'**
  String get availabilityBiasContent;

  /// No description provided for @overestimationLabel.
  ///
  /// In en, this message translates to:
  /// **'Overestimation'**
  String get overestimationLabel;

  /// No description provided for @overestimationSublabel.
  ///
  /// In en, this message translates to:
  /// **'of recent vs. actual patterns'**
  String get overestimationSublabel;

  /// No description provided for @decisionDistortionLabel.
  ///
  /// In en, this message translates to:
  /// **'Decision Distortion'**
  String get decisionDistortionLabel;

  /// No description provided for @decisionDistortionSublabel.
  ///
  /// In en, this message translates to:
  /// **'from memory-based choices'**
  String get decisionDistortionSublabel;

  /// No description provided for @hierarchicalVisualizationNote.
  ///
  /// In en, this message translates to:
  /// **'Hierarchical moment visualization might counteract this bias by providing a more objective representation of temporal patterns.'**
  String get hierarchicalVisualizationNote;

  /// No description provided for @clevelandMcGillContent.
  ///
  /// In en, this message translates to:
  /// **'Cleveland & McGill (1984): Visual perception of proportions appears to be significantly more accurate than narrative memories for evaluating temporal distributions.'**
  String get clevelandMcGillContent;

  /// No description provided for @potentialPersonalApplicationsTitle.
  ///
  /// In en, this message translates to:
  /// **'Potential Personal Applications:'**
  String get potentialPersonalApplicationsTitle;

  /// No description provided for @personalApplicationsList.
  ///
  /// In en, this message translates to:
  /// **'• Actual time distribution vs. perception\n• Energy patterns and emotional states\n• Frequency of different experience types\n• Progress toward long-term objectives'**
  String get personalApplicationsList;

  /// No description provided for @visualizationDiscrepanciesNote.
  ///
  /// In en, this message translates to:
  /// **'These visualizations could reveal discrepancies between subjective perception and objective reality, facilitating more informed decisions.'**
  String get visualizationDiscrepanciesNote;

  /// No description provided for @personalIntuitionParadoxContent.
  ///
  /// In en, this message translates to:
  /// **'Personal Intuition Paradox: While we trust our intuition for personal decisions, we\'re likely to apply rigorous statistical analysis for professional or financial decisions.'**
  String get personalIntuitionParadoxContent;

  /// No description provided for @financialDecisionsLabel.
  ///
  /// In en, this message translates to:
  /// **'Financial Decisions'**
  String get financialDecisionsLabel;

  /// No description provided for @financialDecisionsSublabel.
  ///
  /// In en, this message translates to:
  /// **'use objective data'**
  String get financialDecisionsSublabel;

  /// No description provided for @personalDecisionsLabel.
  ///
  /// In en, this message translates to:
  /// **'Personal Decisions'**
  String get personalDecisionsLabel;

  /// No description provided for @personalDecisionsSublabel.
  ///
  /// In en, this message translates to:
  /// **'use objective data'**
  String get personalDecisionsSublabel;

  /// No description provided for @potentialImprovementLabel.
  ///
  /// In en, this message translates to:
  /// **'Potential Improvement'**
  String get potentialImprovementLabel;

  /// No description provided for @potentialImprovementSublabel.
  ///
  /// In en, this message translates to:
  /// **'with systematic organization'**
  String get potentialImprovementSublabel;

  /// No description provided for @hierarchicalAnalyticalNote.
  ///
  /// In en, this message translates to:
  /// **'Hierarchical organization might allow applying analytical rigor to life decisions while maintaining emotional and intuitive flexibility.'**
  String get hierarchicalAnalyticalNote;

  /// No description provided for @socialLevelTitle.
  ///
  /// In en, this message translates to:
  /// **'4. Social'**
  String get socialLevelTitle;

  /// No description provided for @informationalLevelTitle.
  ///
  /// In en, this message translates to:
  /// **'3. Informational'**
  String get informationalLevelTitle;

  /// No description provided for @personalLevelTitle.
  ///
  /// In en, this message translates to:
  /// **'2. Personal'**
  String get personalLevelTitle;

  /// No description provided for @elementalLevelTitle.
  ///
  /// In en, this message translates to:
  /// **'1. Elemental'**
  String get elementalLevelTitle;

  /// No description provided for @collaborateFunction.
  ///
  /// In en, this message translates to:
  /// **'Collaborate'**
  String get collaborateFunction;

  /// No description provided for @negotiateFunction.
  ///
  /// In en, this message translates to:
  /// **'Negotiate'**
  String get negotiateFunction;

  /// No description provided for @collaboratorValue.
  ///
  /// In en, this message translates to:
  /// **'COLLABORATOR'**
  String get collaboratorValue;

  /// No description provided for @diplomatSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Diplomat'**
  String get diplomatSubtitle;

  /// No description provided for @deliberateFunction.
  ///
  /// In en, this message translates to:
  /// **'Deliberate'**
  String get deliberateFunction;

  /// No description provided for @empathizeFunction.
  ///
  /// In en, this message translates to:
  /// **'Empathize'**
  String get empathizeFunction;

  /// No description provided for @altruisticValue.
  ///
  /// In en, this message translates to:
  /// **'ALTRUISTIC'**
  String get altruisticValue;

  /// No description provided for @empatheticSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Empathetic'**
  String get empatheticSubtitle;

  /// No description provided for @predictFunction.
  ///
  /// In en, this message translates to:
  /// **'Predict'**
  String get predictFunction;

  /// No description provided for @analyzeFunction.
  ///
  /// In en, this message translates to:
  /// **'Analyze'**
  String get analyzeFunction;

  /// No description provided for @strategistValue.
  ///
  /// In en, this message translates to:
  /// **'STRATEGIST'**
  String get strategistValue;

  /// No description provided for @analystSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Analyst'**
  String get analystSubtitle;

  /// No description provided for @trackFunction.
  ///
  /// In en, this message translates to:
  /// **'Track'**
  String get trackFunction;

  /// No description provided for @simplifyFunction.
  ///
  /// In en, this message translates to:
  /// **'Simplify'**
  String get simplifyFunction;

  /// No description provided for @tacticianValue.
  ///
  /// In en, this message translates to:
  /// **'TACTICIAN'**
  String get tacticianValue;

  /// No description provided for @synthesizerSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Synthesizer'**
  String get synthesizerSubtitle;

  /// No description provided for @selfEnjoyFunction.
  ///
  /// In en, this message translates to:
  /// **'Self-Enjoy'**
  String get selfEnjoyFunction;

  /// No description provided for @selfMotivateFunction.
  ///
  /// In en, this message translates to:
  /// **'Self-Motivate'**
  String get selfMotivateFunction;

  /// No description provided for @funnyValue.
  ///
  /// In en, this message translates to:
  /// **'FUNNY'**
  String get funnyValue;

  /// No description provided for @enthusiasticSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Enthusiastic'**
  String get enthusiasticSubtitle;

  /// No description provided for @selfTransformFunction.
  ///
  /// In en, this message translates to:
  /// **'Self-Transform'**
  String get selfTransformFunction;

  /// No description provided for @selfObserveFunction.
  ///
  /// In en, this message translates to:
  /// **'Self-Observe'**
  String get selfObserveFunction;

  /// No description provided for @versatileValue.
  ///
  /// In en, this message translates to:
  /// **'VERSATILE'**
  String get versatileValue;

  /// No description provided for @selfSeerSubtitle.
  ///
  /// In en, this message translates to:
  /// **'SelfSeer'**
  String get selfSeerSubtitle;

  /// No description provided for @executeFunction.
  ///
  /// In en, this message translates to:
  /// **'Execute'**
  String get executeFunction;

  /// No description provided for @discardFunction.
  ///
  /// In en, this message translates to:
  /// **'Discard'**
  String get discardFunction;

  /// No description provided for @warriorValue.
  ///
  /// In en, this message translates to:
  /// **'WARRIOR'**
  String get warriorValue;

  /// No description provided for @releaserSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Releaser'**
  String get releaserSubtitle;

  /// No description provided for @storeFunction.
  ///
  /// In en, this message translates to:
  /// **'Store'**
  String get storeFunction;

  /// No description provided for @recognizeFunction.
  ///
  /// In en, this message translates to:
  /// **'Recognize'**
  String get recognizeFunction;

  /// No description provided for @guardianValue.
  ///
  /// In en, this message translates to:
  /// **'GUARDIAN'**
  String get guardianValue;

  /// No description provided for @nurturerSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Nurturer'**
  String get nurturerSubtitle;

  /// No description provided for @memoryHierarchyContent.
  ///
  /// In en, this message translates to:
  /// **'Bower et al. (1969): Hierarchical organization can improve recall by approximately 200% compared to random presentation.'**
  String get memoryHierarchyContent;

  /// No description provided for @baselineLabel.
  ///
  /// In en, this message translates to:
  /// **'Baseline'**
  String get baselineLabel;

  /// No description provided for @randomPresentationSublabel.
  ///
  /// In en, this message translates to:
  /// **'recall capacity'**
  String get randomPresentationSublabel;

  /// No description provided for @hierarchicalOrganizationLabel.
  ///
  /// In en, this message translates to:
  /// **'Hierarchical Organization'**
  String get hierarchicalOrganizationLabel;

  /// No description provided for @hierarchicalImprovementSublabel.
  ///
  /// In en, this message translates to:
  /// **'improvement in recall'**
  String get hierarchicalImprovementSublabel;

  /// No description provided for @brainProcessesHierarchically.
  ///
  /// In en, this message translates to:
  /// **'Likely implication: Your brain probably processes information hierarchically by nature. Fighting against this structure possibly wastes cognitive resources.'**
  String get brainProcessesHierarchically;

  /// No description provided for @decisionFatigueContent.
  ///
  /// In en, this message translates to:
  /// **'Baumeister et al. (1998): After repeated unstructured decisions, decision quality probably declines significantly.'**
  String get decisionFatigueContent;

  /// No description provided for @evolutionaryPerspectiveTitle.
  ///
  /// In en, this message translates to:
  /// **'Evolutionary Perspective:'**
  String get evolutionaryPerspectiveTitle;

  /// No description provided for @ancestorsDecisionsContent.
  ///
  /// In en, this message translates to:
  /// **'Our ancestors probably faced limited daily decisions in structured social hierarchies. Modern chaos of disorganized choices may exceed our cognitive capacity.'**
  String get ancestorsDecisionsContent;

  /// No description provided for @preOrganizedStructures.
  ///
  /// In en, this message translates to:
  /// **'Pre-organized hierarchical structures could maintain decision quality even under cognitive load.'**
  String get preOrganizedStructures;

  /// No description provided for @millerNumberContent.
  ///
  /// In en, this message translates to:
  /// **'Miller (1956): Humans can possibly maintain 7±2 unrelated items in working memory, but probably can process 7±2 categories, each containing 7±2 subcategories.'**
  String get millerNumberContent;

  /// No description provided for @individualItemsLabel.
  ///
  /// In en, this message translates to:
  /// **'Individual Items'**
  String get individualItemsLabel;

  /// No description provided for @workingMemoryLimitSublabel.
  ///
  /// In en, this message translates to:
  /// **'working memory limit'**
  String get workingMemoryLimitSublabel;

  /// No description provided for @hierarchicalCapacityLabel.
  ///
  /// In en, this message translates to:
  /// **'Hierarchical Capacity'**
  String get hierarchicalCapacityLabel;

  /// No description provided for @organizedElementsSublabel.
  ///
  /// In en, this message translates to:
  /// **'organized elements'**
  String get organizedElementsSublabel;

  /// No description provided for @exponentialProcessingCapacity.
  ///
  /// In en, this message translates to:
  /// **'This could create exponential processing capacity through hierarchy, freeing mental resources for pattern recognition and future planning.'**
  String get exponentialProcessingCapacity;

  /// No description provided for @ancestralMismatchContent.
  ///
  /// In en, this message translates to:
  /// **'Modern humans possibly face approximately 35,000 daily decisions, while our ancestors probably encountered 70-100 structured decisions in predictable social hierarchies.'**
  String get ancestralMismatchContent;

  /// No description provided for @ancestralDecisionsLabel.
  ///
  /// In en, this message translates to:
  /// **'Ancestral Decisions'**
  String get ancestralDecisionsLabel;

  /// No description provided for @structuredPerDaySublabel.
  ///
  /// In en, this message translates to:
  /// **'structured/day'**
  String get structuredPerDaySublabel;

  /// No description provided for @modernDecisionsLabel.
  ///
  /// In en, this message translates to:
  /// **'Modern Decisions'**
  String get modernDecisionsLabel;

  /// No description provided for @unstructuredPerDaySublabel.
  ///
  /// In en, this message translates to:
  /// **'unstructured/day'**
  String get unstructuredPerDaySublabel;

  /// No description provided for @schwartzOptionsContent.
  ///
  /// In en, this message translates to:
  /// **'Schwartz (2004): More than 8-10 unstructured options can decrease satisfaction by 25% and decision quality by 15%.'**
  String get schwartzOptionsContent;

  /// No description provided for @foragingEfficiencyContent.
  ///
  /// In en, this message translates to:
  /// **'Stephens & Krebs (1986): Animals that organized foraging behavior hierarchically (territory → patches → specific resources) likely showed 40-60% better energy efficiency.'**
  String get foragingEfficiencyContent;

  /// No description provided for @energyEfficiencyLabel.
  ///
  /// In en, this message translates to:
  /// **'Energy Efficiency'**
  String get energyEfficiencyLabel;

  /// No description provided for @hierarchicalOrganizationSublabel.
  ///
  /// In en, this message translates to:
  /// **'hierarchical organization'**
  String get hierarchicalOrganizationSublabel;

  /// No description provided for @goalAchievementLabel.
  ///
  /// In en, this message translates to:
  /// **'Goal Achievement'**
  String get goalAchievementLabel;

  /// No description provided for @structuredFrameworksSublabel.
  ///
  /// In en, this message translates to:
  /// **'structured frameworks'**
  String get structuredFrameworksSublabel;

  /// No description provided for @gigerenzerFrameworksContent.
  ///
  /// In en, this message translates to:
  /// **'Gigerenzer (2007): People using hierarchical decision frameworks possibly achieve goals 35% faster with 50% less effort.'**
  String get gigerenzerFrameworksContent;

  /// No description provided for @compressionAdvantageContent.
  ///
  /// In en, this message translates to:
  /// **'Shannon (1948): Hierarchical organization probably achieves optimal data compression. Applied to life experiences, this could allow processing exponentially more information.'**
  String get compressionAdvantageContent;

  /// No description provided for @applicationToMomentsTitle.
  ///
  /// In en, this message translates to:
  /// **'Application to Moments:'**
  String get applicationToMomentsTitle;

  /// No description provided for @compressionMomentsContent.
  ///
  /// In en, this message translates to:
  /// **'Instead of remembering hundreds of disconnected experiences, hierarchical organization possibly allows compressing similar moments into categories, freeing mental resources for pattern recognition and future planning.'**
  String get compressionMomentsContent;

  /// No description provided for @predictionMachineContent.
  ///
  /// In en, this message translates to:
  /// **'Clark (2013): The brain possibly operates as a \"prediction machine,\" constantly generating models of future experiences based on past patterns.'**
  String get predictionMachineContent;

  /// No description provided for @neuralReductionLabel.
  ///
  /// In en, this message translates to:
  /// **'Neural Reduction'**
  String get neuralReductionLabel;

  /// No description provided for @predictableExperiencesSublabel.
  ///
  /// In en, this message translates to:
  /// **'predictable experiences'**
  String get predictableExperiencesSublabel;

  /// No description provided for @unpredictedActivityLabel.
  ///
  /// In en, this message translates to:
  /// **'Unpredicted Activity'**
  String get unpredictedActivityLabel;

  /// No description provided for @neuralActivitySublabel.
  ///
  /// In en, this message translates to:
  /// **'neural activity'**
  String get neuralActivitySublabel;

  /// No description provided for @organizedMomentTracking.
  ///
  /// In en, this message translates to:
  /// **'Organized moment tracking probably creates better predictive models, reducing cognitive load and improving future decision-making accuracy.'**
  String get organizedMomentTracking;

  /// No description provided for @entropyReductionContent.
  ///
  /// In en, this message translates to:
  /// **'Bialek et al. (2001): Neural networks using hierarchical processing possibly achieve superior efficiency in information transmission compared to flat structures.'**
  String get entropyReductionContent;

  /// No description provided for @lifeApplicationEntropy.
  ///
  /// In en, this message translates to:
  /// **'Life application: Hierarchical moment organization probably allows extracting maximum insight from experiences while minimizing cognitive noise.'**
  String get lifeApplicationEntropy;

  /// No description provided for @creativityMythCounterTitle.
  ///
  /// In en, this message translates to:
  /// **'Counter-evidence:'**
  String get creativityMythCounterTitle;

  /// No description provided for @creativityMythCounterContent.
  ///
  /// In en, this message translates to:
  /// **'• Stokes (2005): Creative professionals with organizational frameworks possibly produce more innovative work\n• Schwartz (2004): Too many unstructured options probably decrease creative output\n• Hierarchical organization probably reduces cognitive noise, freeing mental resources for creative thinking'**
  String get creativityMythCounterContent;

  /// No description provided for @successMythCounterContent.
  ///
  /// In en, this message translates to:
  /// **'• Ericsson (2016): Elite performers in all domains probably use highly structured practice and reflection systems\n• High-performing individuals probably show superior organizational skills, not less structure\n• Successful hunter-gatherer societies probably had complex hierarchical organization systems'**
  String get successMythCounterContent;

  /// No description provided for @hierarchyMythCounterContent.
  ///
  /// In en, this message translates to:
  /// **'• All successful primate societies probably exhibit hierarchical organization with clear roles\n• The human brain probably evolved hierarchical processing as its fundamental architecture\n• Even egalitarian societies possibly maintain hierarchical organization for different domains'**
  String get hierarchyMythCounterContent;

  /// No description provided for @simplicityMythCounterContent.
  ///
  /// In en, this message translates to:
  /// **'• Appropriate complexity probably matches environmental demands\n• Over-simplification possibly leads to system failure\n• Well-structured complexity probably reduces cognitive load\n• Hierarchical organization probably achieves optimal balance between simplicity and information richness'**
  String get simplicityMythCounterContent;

  /// No description provided for @anxietyMythCounterContent.
  ///
  /// In en, this message translates to:
  /// **'• Unstructured uncertainty probably generates more anxiety than organized complexity\n• Clear frameworks possibly reduce decision anxiety\n• Hierarchical organization probably provides predictive structure that calms the nervous system\n• Studies suggest organizational clarity reduces stress hormones'**
  String get anxietyMythCounterContent;

  /// Title for the methodology page in full presentation
  ///
  /// In en, this message translates to:
  /// **'The Methodology'**
  String get fullPresentationMethodologyTitle;

  /// Content for the methodology page in full presentation
  ///
  /// In en, this message translates to:
  /// **'Our approach combines psychological insights with practical analysis:\n\n1. **Moment Identification**: Recognize key moments in your life\n2. **Dimensional Analysis**: Categorize across four levels\n3. **Directional Focus**: Understand inward vs outward orientation\n4. **Impact Assessment**: Evaluate motivation and satisfaction effects\n5. **Pattern Recognition**: Discover your latent values'**
  String get fullPresentationMethodologyContent;

  /// Title for the four levels page in full presentation
  ///
  /// In en, this message translates to:
  /// **'Four Levels of Human Experience'**
  String get fullPresentationFourLevelsTitle;

  /// Additional content for the four levels page in full presentation
  ///
  /// In en, this message translates to:
  /// **'These levels work hierarchically, building upon each other to create your complete experience.'**
  String get fullPresentationFourLevelsContent;

  /// Title for the directional focus page in full presentation
  ///
  /// In en, this message translates to:
  /// **'Directional Focus'**
  String get fullPresentationDirectionalFocusTitle;

  /// Additional content for the directional focus page in full presentation
  ///
  /// In en, this message translates to:
  /// **'Every moment has both inward and outward components, but one direction typically dominates. Understanding this helps reveal your natural tendencies and preferences.'**
  String get fullPresentationDirectionalFocusContent;

  /// Title for the practical application page in full presentation
  ///
  /// In en, this message translates to:
  /// **'Practical Application'**
  String get fullPresentationPracticalApplicationTitle;

  /// Content for the practical application page in full presentation
  ///
  /// In en, this message translates to:
  /// **'Use this methodology to:\n\n• **Track patterns** in your decision-making\n• **Identify triggers** for motivation and satisfaction\n• **Understand conflicts** between different aspects of yourself\n• **Make better choices** aligned with your values\n• **Develop strategies** for personal growth'**
  String get fullPresentationPracticalApplicationContent;

  /// Title for the conclusion page in full presentation
  ///
  /// In en, this message translates to:
  /// **'Your Journey Begins'**
  String get fullPresentationConclusionTitle;

  /// Content for the conclusion page in full presentation
  ///
  /// In en, this message translates to:
  /// **'Now that you understand the methodology, you can:\n\n• Start recording your key moments\n• Analyze your patterns over time\n• Discover your unique latent values\n• Use insights for personal development\n\nRemember: Self-awareness is the first step toward intentional living.'**
  String get fullPresentationConclusionContent;

  /// Description for elemental level in full presentation
  ///
  /// In en, this message translates to:
  /// **'Physical and instinctual responses'**
  String get fullPresentationElementalDescription;

  /// Description for personal level in full presentation
  ///
  /// In en, this message translates to:
  /// **'Individual experiences and emotions'**
  String get fullPresentationPersonalDescription;

  /// Description for informational level in full presentation
  ///
  /// In en, this message translates to:
  /// **'Intellectual and analytical processes'**
  String get fullPresentationInformationalDescription;

  /// Description for social level in full presentation
  ///
  /// In en, this message translates to:
  /// **'Interpersonal and cultural connections'**
  String get fullPresentationSocialDescription;

  /// Title for ancestral mismatch section
  ///
  /// In en, this message translates to:
  /// **'Ancestral Environment Mismatch'**
  String get ancestralMismatchTitle;

  /// Title for social hierarchy section
  ///
  /// In en, this message translates to:
  /// **'Social Hierarchy Hypothesis'**
  String get socialHierarchyTitle;

  /// Title for foraging efficiency section
  ///
  /// In en, this message translates to:
  /// **'Foraging Efficiency Model'**
  String get foragingEfficiencyTitle;

  /// Title for compression advantage section
  ///
  /// In en, this message translates to:
  /// **'The Compression Advantage'**
  String get compressionAdvantageTitle;

  /// Title for prediction machine section
  ///
  /// In en, this message translates to:
  /// **'The Brain as Prediction Machine'**
  String get predictionMachineTitle;

  /// Title for entropy reduction section
  ///
  /// In en, this message translates to:
  /// **'Entropy Reduction Principle'**
  String get entropyReductionTitle;

  /// Title for creativity myth section
  ///
  /// In en, this message translates to:
  /// **'Myth: \'Organization kills creativity and spontaneity\''**
  String get creativityMythTitle;

  /// Title for success myth section
  ///
  /// In en, this message translates to:
  /// **'Myth: \'Successful people don\'t need systems—they just improvise\''**
  String get successMythTitle;

  /// Title for hierarchy myth section
  ///
  /// In en, this message translates to:
  /// **'Myth: \'Hierarchy is unnatural and oppressive\''**
  String get hierarchyMythTitle;

  /// Title for simplicity myth section
  ///
  /// In en, this message translates to:
  /// **'Myth: \'Simple is always better than complex\''**
  String get simplicityMythTitle;

  /// Title for anxiety myth section
  ///
  /// In en, this message translates to:
  /// **'Myth: \'Organization is only for anxious or controlling people\''**
  String get anxietyMythTitle;

  /// Content for social hierarchy section
  ///
  /// In en, this message translates to:
  /// **'Sapolsky (2017): Humans and other primates likely show the lowest stress hormone levels when their social position is clearly defined, predictable, and internally controlled.'**
  String get socialHierarchyContent;

  /// Title for stress reduction elements
  ///
  /// In en, this message translates to:
  /// **'Key Elements for Stress Reduction:'**
  String get stressReductionElementsTitle;

  /// List of stress reduction elements
  ///
  /// In en, this message translates to:
  /// **'• Hierarchical clarity: Defined position\n• Predictability: Consistent rules\n• Internal control: Agency within structure'**
  String get stressReductionElementsList;

  /// Label for random presentation in memory hierarchy
  ///
  /// In en, this message translates to:
  /// **'Random Presentation'**
  String get randomPresentationLabel;

  /// Benefits of hierarchical organization
  ///
  /// In en, this message translates to:
  /// **'Hierarchical organization of life can mimic successful ancestral social structures, likely reducing stress and improving decision-making.'**
  String get hierarchicalOrganizationBenefits;

  /// Title for the four levels process section
  ///
  /// In en, this message translates to:
  /// **'Four Levels Process'**
  String get fourLevelsProcessTitle;

  /// Title for the hierarchical structure section
  ///
  /// In en, this message translates to:
  /// **'Hierarchical Structure'**
  String get hierarchicalStructureTitle;

  /// Description of hierarchical structure
  ///
  /// In en, this message translates to:
  /// **'The four dimensions follow a hierarchical structure:'**
  String get hierarchicalStructureDescription;

  /// Equation showing all levels equal 100%
  ///
  /// In en, this message translates to:
  /// **'Elemental + Personal + Informational + Social = 100%'**
  String get levelsEquationText;

  /// Explanation of elemental allocation
  ///
  /// In en, this message translates to:
  /// **'When you allocate a percentage to Elemental, the remaining percentage is distributed among Personal, Informational, and Social.'**
  String get elementalAllocationText;

  /// Explanation of personal allocation
  ///
  /// In en, this message translates to:
  /// **'When you allocate a percentage to Personal, the remaining percentage is distributed between Informational and Social.'**
  String get personalAllocationText;

  /// Explanation of informational allocation
  ///
  /// In en, this message translates to:
  /// **'When you allocate a percentage to Informational, the remaining percentage goes to Social.'**
  String get informationalAllocationText;

  /// Explanation of inward and outward directions
  ///
  /// In en, this message translates to:
  /// **'Inward + Outward = 100%\n\nThese dimensions represent your orientation or approach to each moment.'**
  String get directionsExplanationText;

  /// No description provided for @pleaseEnterTitle.
  ///
  /// In en, this message translates to:
  /// **'Please enter a title'**
  String get pleaseEnterTitle;

  /// No description provided for @titleMustBeTwoWords.
  ///
  /// In en, this message translates to:
  /// **'Title must be exactly two words'**
  String get titleMustBeTwoWords;

  /// No description provided for @untitledMoment.
  ///
  /// In en, this message translates to:
  /// **'Untitled Moment'**
  String get untitledMoment;

  /// No description provided for @whyMomentsTitle.
  ///
  /// In en, this message translates to:
  /// **'Why Moments'**
  String get whyMomentsTitle;

  /// No description provided for @whyMomentsSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Analyze your moments, know your patterns'**
  String get whyMomentsSubtitle;

  /// No description provided for @whyHierarchicalTitle.
  ///
  /// In en, this message translates to:
  /// **'Why Hierarchical Organization'**
  String get whyHierarchicalTitle;

  /// No description provided for @whyHierarchicalSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Why Hierarchical Organization of Life Moments Might Matter'**
  String get whyHierarchicalSubtitle;

  /// No description provided for @highMotivationHighSatisfactionContent.
  ///
  /// In en, this message translates to:
  /// **'According to Kahneman and Tversky (1979), our brain tends to give more weight to the recent or intense (peak-end rule). That\'s why we sometimes forget valuable moments that weren\'t so \'noisy\'.'**
  String get highMotivationHighSatisfactionContent;

  /// No description provided for @importantNote.
  ///
  /// In en, this message translates to:
  /// **'Important: If you\'re already experiencing consistent well-being and deep satisfaction, additional organization is likely unnecessary. This approach seems most relevant during transitions, complex decisions, or persistent dissatisfaction.'**
  String get importantNote;

  /// No description provided for @dataVisualizationTitle.
  ///
  /// In en, this message translates to:
  /// **'Data Visualization & Bias Reduction'**
  String get dataVisualizationTitle;

  /// No description provided for @dataVisualizationContent.
  ///
  /// In en, this message translates to:
  /// **'Hierarchical moment visualization might counteract availability bias by providing a more objective representation of temporal patterns. Visual perception of proportions appears to be significantly more accurate than narrative memories.'**
  String get dataVisualizationContent;

  /// No description provided for @experimentalEvidenceContent.
  ///
  /// In en, this message translates to:
  /// **'Hierarchical organization might improve recall by approximately 200% compared to random presentation. After repeated unstructured decisions, decision quality likely declines significantly.'**
  String get experimentalEvidenceContent;

  /// No description provided for @evolutionaryPsychologyTitle.
  ///
  /// In en, this message translates to:
  /// **'Evolutionary Psychology Perspective'**
  String get evolutionaryPsychologyTitle;

  /// No description provided for @evolutionaryPsychologyContent.
  ///
  /// In en, this message translates to:
  /// **'Modern humans possibly face approximately 35,000 daily decisions, while our ancestors probably encountered 70-100 structured decisions in predictable social hierarchies.'**
  String get evolutionaryPsychologyContent;

  /// No description provided for @informationTheoryTitle.
  ///
  /// In en, this message translates to:
  /// **'Information Theory Perspective'**
  String get informationTheoryTitle;

  /// No description provided for @informationTheoryContent.
  ///
  /// In en, this message translates to:
  /// **'Hierarchical organization probably achieves optimal data compression. Applied to life experiences, this could allow processing exponentially more information.'**
  String get informationTheoryContent;

  /// No description provided for @scientificFooter.
  ///
  /// In en, this message translates to:
  /// **'Based on scientific research • Designed for your personal growth'**
  String get scientificFooter;

  /// No description provided for @guardianValueDescription.
  ///
  /// In en, this message translates to:
  /// **'Recognize: You recognize, consume, ingest.\\nStore: You rest, store, and metabolize.'**
  String get guardianValueDescription;

  /// No description provided for @warriorValueDescription.
  ///
  /// In en, this message translates to:
  /// **'Discard: You discard, flee, inhibit.\\nExecute: Fight, attack, and struggle.'**
  String get warriorValueDescription;

  /// No description provided for @versatileValueDescription.
  ///
  /// In en, this message translates to:
  /// **'Self-Observe: You highlight negative information, feel pain, observe errors.\\nSelf-Transform: You reduce your errors and adapt stimuli.'**
  String get versatileValueDescription;

  /// No description provided for @funValueDescription.
  ///
  /// In en, this message translates to:
  /// **'Self-Motivate: You highlight positive information, feel pleasure.\\nSelf-Enjoy: I enhance successes and contrast attitudes and ideas.'**
  String get funValueDescription;

  /// No description provided for @strategistValueDescription.
  ///
  /// In en, this message translates to:
  /// **'Analyze: You review trends, ask if something could be false, analyze.\\nPredict: You predict what is most probable and create hypotheses.'**
  String get strategistValueDescription;

  /// No description provided for @tacticalValueDescription.
  ///
  /// In en, this message translates to:
  /// **'Simplify: You generalize, compare the easiest, fastest way.\\nTrack: You search, hunt, and trace.'**
  String get tacticalValueDescription;

  /// No description provided for @altruistValueDescription.
  ///
  /// In en, this message translates to:
  /// **'Empathize: You empathize with what is important for others.\\nDeliberate: You consider myself and the needs of as many people as possible, practicing efficiency altruism.'**
  String get altruistValueDescription;

  /// No description provided for @collaboratorValueDescription.
  ///
  /// In en, this message translates to:
  /// **'Negotiate: You help to understand, communicate.\\nCollaborate: You cooperate toward shared goals.'**
  String get collaboratorValueDescription;

  /// No description provided for @functionCollaborate.
  ///
  /// In en, this message translates to:
  /// **'Collaborate'**
  String get functionCollaborate;

  /// No description provided for @functionNegotiate.
  ///
  /// In en, this message translates to:
  /// **'Negotiate'**
  String get functionNegotiate;

  /// No description provided for @functionDeliberate.
  ///
  /// In en, this message translates to:
  /// **'Deliberate'**
  String get functionDeliberate;

  /// No description provided for @functionEmpathize.
  ///
  /// In en, this message translates to:
  /// **'Empathize'**
  String get functionEmpathize;

  /// No description provided for @functionPredict.
  ///
  /// In en, this message translates to:
  /// **'Predict'**
  String get functionPredict;

  /// No description provided for @functionAnalyze.
  ///
  /// In en, this message translates to:
  /// **'Analyze'**
  String get functionAnalyze;

  /// No description provided for @functionTrack.
  ///
  /// In en, this message translates to:
  /// **'Track'**
  String get functionTrack;

  /// No description provided for @functionSimplify.
  ///
  /// In en, this message translates to:
  /// **'Simplify'**
  String get functionSimplify;

  /// No description provided for @functionSelfEnjoy.
  ///
  /// In en, this message translates to:
  /// **'Self-Enjoy'**
  String get functionSelfEnjoy;

  /// No description provided for @functionSelfMotivate.
  ///
  /// In en, this message translates to:
  /// **'Self-Motivate'**
  String get functionSelfMotivate;

  /// No description provided for @functionSelfTransform.
  ///
  /// In en, this message translates to:
  /// **'Self-Transform'**
  String get functionSelfTransform;

  /// No description provided for @functionSelfObserve.
  ///
  /// In en, this message translates to:
  /// **'Self-Observe'**
  String get functionSelfObserve;

  /// No description provided for @functionExecute.
  ///
  /// In en, this message translates to:
  /// **'Execute'**
  String get functionExecute;

  /// No description provided for @functionDiscard.
  ///
  /// In en, this message translates to:
  /// **'Discard'**
  String get functionDiscard;

  /// No description provided for @functionStore.
  ///
  /// In en, this message translates to:
  /// **'Store'**
  String get functionStore;

  /// No description provided for @functionRecognize.
  ///
  /// In en, this message translates to:
  /// **'Recognize'**
  String get functionRecognize;

  /// No description provided for @valueCollaborator.
  ///
  /// In en, this message translates to:
  /// **'COLLABORATOR'**
  String get valueCollaborator;

  /// No description provided for @valueAltruistic.
  ///
  /// In en, this message translates to:
  /// **'ALTRUISTIC'**
  String get valueAltruistic;

  /// No description provided for @valueStrategist.
  ///
  /// In en, this message translates to:
  /// **'STRATEGIST'**
  String get valueStrategist;

  /// No description provided for @valueTactician.
  ///
  /// In en, this message translates to:
  /// **'TACTICIAN'**
  String get valueTactician;

  /// No description provided for @valueFunny.
  ///
  /// In en, this message translates to:
  /// **'FUNNY'**
  String get valueFunny;

  /// No description provided for @valueVersatile.
  ///
  /// In en, this message translates to:
  /// **'VERSATILE'**
  String get valueVersatile;

  /// No description provided for @valueWarrior.
  ///
  /// In en, this message translates to:
  /// **'WARRIOR'**
  String get valueWarrior;

  /// No description provided for @valueGuardian.
  ///
  /// In en, this message translates to:
  /// **'GUARDIAN'**
  String get valueGuardian;

  /// No description provided for @subtitleDiplomat.
  ///
  /// In en, this message translates to:
  /// **'Diplomat'**
  String get subtitleDiplomat;

  /// No description provided for @subtitleEmpathetic.
  ///
  /// In en, this message translates to:
  /// **'Empathetic'**
  String get subtitleEmpathetic;

  /// No description provided for @subtitleAnalyst.
  ///
  /// In en, this message translates to:
  /// **'Analyst'**
  String get subtitleAnalyst;

  /// No description provided for @subtitleSynthesizer.
  ///
  /// In en, this message translates to:
  /// **'Synthesizer'**
  String get subtitleSynthesizer;

  /// No description provided for @subtitleEnthusiastic.
  ///
  /// In en, this message translates to:
  /// **'Enthusiastic'**
  String get subtitleEnthusiastic;

  /// No description provided for @subtitleSelfSeer.
  ///
  /// In en, this message translates to:
  /// **'Self-Seer'**
  String get subtitleSelfSeer;

  /// No description provided for @subtitleReleaser.
  ///
  /// In en, this message translates to:
  /// **'Releaser'**
  String get subtitleReleaser;

  /// No description provided for @subtitleNurturer.
  ///
  /// In en, this message translates to:
  /// **'Nurturer'**
  String get subtitleNurturer;

  /// No description provided for @descriptionGuardian.
  ///
  /// In en, this message translates to:
  /// **'Value focused on preserving, protecting and maintaining stability. Represents the ability to recognize and store important information for security and continuity.'**
  String get descriptionGuardian;

  /// No description provided for @descriptionWarrior.
  ///
  /// In en, this message translates to:
  /// **'Value focused on action, execution and overcoming obstacles. Represents the ability to execute decisions and discard what no longer serves.'**
  String get descriptionWarrior;

  /// No description provided for @descriptionVersatile.
  ///
  /// In en, this message translates to:
  /// **'Value focused on adaptability and self-observation. Represents the ability to transform oneself and observe oneself for personal growth.'**
  String get descriptionVersatile;

  /// No description provided for @descriptionFunny.
  ///
  /// In en, this message translates to:
  /// **'Value focused on pleasure, motivation and positive energy. Represents the ability to self-enjoy and self-motivate to maintain well-being.'**
  String get descriptionFunny;

  /// No description provided for @descriptionStrategist.
  ///
  /// In en, this message translates to:
  /// **'Value focused on planning and deep analysis. Represents the ability to predict scenarios and analyze complex information.'**
  String get descriptionStrategist;

  /// No description provided for @descriptionTactician.
  ///
  /// In en, this message translates to:
  /// **'Value focused on synthesis and efficient tracking. Represents the ability to simplify complexities and track progress.'**
  String get descriptionTactician;

  /// No description provided for @descriptionAltruistic.
  ///
  /// In en, this message translates to:
  /// **'Value focused on empathy and consideration for others. Represents the ability to deliberate and empathize for the common good.'**
  String get descriptionAltruistic;

  /// No description provided for @descriptionCollaborator.
  ///
  /// In en, this message translates to:
  /// **'Value focused on cooperation and negotiation. Represents the ability to collaborate effectively and negotiate mutually beneficial solutions.'**
  String get descriptionCollaborator;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'es', 'pt'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
    case 'pt':
      return AppLocalizationsPt();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
