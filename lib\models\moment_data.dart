import 'dart:math' as math;
import 'package:uuid/uuid.dart';
import 'moment_type.dart';

class MomentData {
  final String id;
  final DateTime timestamp;
  final MomentType type;
  final String title;
  final String description;

  // Hierarchical percentages (sum to 100%)
  final double elementalPercentage;
  final double personalPercentage;
  final double informationalPercentage;
  final double socialPercentage;

  // Orientation percentages (sum to 100%)
  final double inwardPercentage;
  final double outwardPercentage;

  // Evidence fields
  final String elementalPersonalEvidence;
  final String personalInformationalEvidence;
  final String informationalSocialEvidence;
  final String inwardOutwardEvidence;
  final String momentTypeEvidence; // Renamed from improvesWorsensEvidence

  // Improves/Worsens comparison ratio (0.0 to 1.0) - DEPRECATED
  final double improvesWorsensRatio;

  // New factorial-based comparison system
  final bool isImprovementComparedToLife; // true = improvement, false = worsening
  final double factorialSliderValue; // 0-100 slider value
  final double factorialMultiplier; // calculated factorial value

  // Calculated latent values
  late final double guardianPercentage;
  late final double warriorPercentage;
  late final double versatilePercentage;
  late final double funPercentage;
  late final double strategistPercentage;
  late final double tacticalPercentage;
  late final double altruistPercentage;
  late final double collaboratorPercentage;

  // Highlight value (the highest latent value)
  late final String highlightValue;

  MomentData({
    String? id,
    DateTime? timestamp,
    required this.type,
    required this.title,
    required this.description,
    required this.elementalPercentage,
    required this.personalPercentage,
    required this.informationalPercentage,
    required this.socialPercentage,
    required this.inwardPercentage,
    required this.outwardPercentage,
    this.elementalPersonalEvidence = '',
    this.personalInformationalEvidence = '',
    this.informationalSocialEvidence = '',
    this.inwardOutwardEvidence = '',
    this.momentTypeEvidence = '', // Renamed from improvesWorsensEvidence
    this.improvesWorsensRatio = 0.5, // DEPRECATED - kept for backward compatibility
    this.isImprovementComparedToLife = true, // Default to improvement
    this.factorialSliderValue = 1.0, // Default to minimal factorial value
    double? customFactorialMultiplier, // Optional override for custom calculation
  }) :
    id = id ?? const Uuid().v4(),
    timestamp = timestamp ?? DateTime.now(),
    factorialMultiplier = customFactorialMultiplier ?? _calculateFactorial(factorialSliderValue) {
    // Debug: Final factorial multiplier value: $factorialMultiplier (custom: $customFactorialMultiplier, slider: $factorialSliderValue)
    _calculateLatentValues();
    _determineHighlightValue();
  }

  // Calculate factorial value with safety limits - can go below 1x for "fewer possibilities"
  static double _calculateFactorial(double sliderValue) {
    // Clamp slider value to safe range (0.1-100)
    final clampedValue = sliderValue.clamp(0.1, 100.0);

    // For values below 1, return the fractional value directly (not factorial)
    if (clampedValue < 1.0) {
      return clampedValue; // 0.1x to 0.9x represent fractions of life baseline
    }

    // For values 1-20, calculate actual factorial
    if (clampedValue <= 20.0) {
      int intValue = clampedValue.round();
      double result = 1.0;
      for (int i = 1; i <= intValue; i++) {
        result *= i;
      }
      return result;
    }

    // For values > 20, use approximation to avoid overflow
    // Using Stirling's approximation: n! ≈ √(2πn) * (n/e)^n
    final n = clampedValue;
    return math.sqrt(2 * math.pi * n) * math.pow(n / math.e, n);
  }

  void _calculateLatentValues() {
    final improvesWorsensFactor = type.isImproves ? 1.0 : -1.0;

    // Calculate latent values based on the formulas
    guardianPercentage = elementalPercentage * inwardPercentage * improvesWorsensFactor / 10000;
    warriorPercentage = elementalPercentage * outwardPercentage * improvesWorsensFactor / 10000;
    versatilePercentage = personalPercentage * inwardPercentage * improvesWorsensFactor / 10000;
    funPercentage = personalPercentage * outwardPercentage * improvesWorsensFactor / 10000;
    strategistPercentage = informationalPercentage * inwardPercentage * improvesWorsensFactor / 10000;
    tacticalPercentage = informationalPercentage * outwardPercentage * improvesWorsensFactor / 10000;
    altruistPercentage = socialPercentage * inwardPercentage * improvesWorsensFactor / 10000;
    collaboratorPercentage = socialPercentage * outwardPercentage * improvesWorsensFactor / 10000;
  }

  void _determineHighlightValue() {
    // Find the highest latent value
    final values = {
      'Guardian': guardianPercentage,
      'Warrior': warriorPercentage,
      'Versatile': versatilePercentage,
      'Fun': funPercentage,
      'Strategist': strategistPercentage,
      'Tactical': tacticalPercentage,
      'Altruist': altruistPercentage,
      'Collaborator': collaboratorPercentage,
    };

    highlightValue = values.entries
        .reduce((a, b) => a.value.abs() > b.value.abs() ? a : b)
        .key;
  }

  // Convert to CSV row
  List<String> toCsvRow() {
    return [
      timestamp.toIso8601String(),                    // 1. Time stamp
      type.isImproves ? 'Improves' : 'Worsens',       // 2. Moment type: improve or worsen
      type.isMotivation ? 'Motivation' : 'Satisfaction', // 3. Moment type: motivation or satisfaction
      highlightValue,                                 // 4. Highlight value (highest %)
      description,                                    // 5. Moment description
      title,                                          // 6. Two-word title
      elementalPercentage.toStringAsFixed(2),         // 7. Elemental %
      elementalPersonalEvidence,                      // 8. Evidence of Distribution between elemental and personal
      personalPercentage.toStringAsFixed(2),          // 9. Personal %
      personalInformationalEvidence,                  // 10. Evidence of distribution between personal and informational
      informationalPercentage.toStringAsFixed(2),     // 11. Informational %
      socialPercentage.toStringAsFixed(2),            // 12. Social %
      informationalSocialEvidence,                    // 13. Evidence between informational and social
      inwardPercentage.toStringAsFixed(2),            // 14. Inward %
      outwardPercentage.toStringAsFixed(2),           // 15. Outward %
      inwardOutwardEvidence,                          // 16. Evidence of inward vs. outward
      isImprovementComparedToLife ? 'Improvement' : 'Worsening', // 17. Factorial comparison type
      factorialSliderValue.toStringAsFixed(2),        // 18. Factorial slider value (0-100)
      factorialMultiplier.toStringAsFixed(2),         // 19. Factorial multiplier value
    ];
  }

  // CSV header
  static List<String> csvHeader() {
    return [
      'Timestamp',                                    // 1. Time stamp
      'Moment Type (Improve/Worsen)',                 // 2. Moment type: improve or worsen
      'Moment Type (Motivation/Satisfaction)',        // 3. Moment type: motivation or satisfaction
      'Highlight Value',                              // 4. Highlight value (highest %)
      'Description',                                  // 5. Moment description
      'Title',                                        // 6. Two-word title
      'Elemental %',                                  // 7. Elemental %
      'Elemental-Personal Evidence',                  // 8. Evidence of Distribution between elemental and personal
      'Personal %',                                   // 9. Personal %
      'Personal-Informational Evidence',              // 10. Evidence of distribution between personal and informational
      'Informational %',                              // 11. Informational %
      'Social %',                                     // 12. Social %
      'Informational-Social Evidence',                // 13. Evidence between informational and social
      'Inward %',                                     // 14. Inward %
      'Outward %',                                    // 15. Outward %
      'Inward-Outward Evidence',                      // 16. Evidence of inward vs. outward
      'Factorial Comparison Type',                    // 17. Improvement or Worsening compared to life
      'Factorial Slider Value',                       // 18. Slider value (0-100)
      'Factorial Multiplier',                         // 19. Calculated factorial value
    ];
  }
}
