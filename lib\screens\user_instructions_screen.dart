import 'package:flutter/material.dart';
import 'package:estimat_keymoments/l10n/app_localizations.dart';
import '../widgets/app_menu.dart';

class UserInstructionsScreen extends StatelessWidget {
  const UserInstructionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.userInstructionsScreenTitle),
        backgroundColor: const Color(0xFF2A6BAA),
        actions: const [
          AppMenu(),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            _buildSection(
              context,
              '🎯',
              localizations.instructionsWelcomeTitle,
              localizations.instructionsWelcomeContent,
              Colors.deepPurple.shade100,
            ),
            const SizedBox(height: 20),
            
            // Step 1: Extreme Moments
            _buildSection(
              context,
              '⚡',
              localizations.instructionsStep1Title,
              localizations.instructionsStep1Content,
              Colors.orange.shade100,
            ),
            const SizedBox(height: 20),
            
            // Step 2: Navigation
            _buildSection(
              context,
              '🧭',
              localizations.instructionsStep2Title,
              localizations.instructionsStep2Content,
              Colors.blue.shade100,
            ),
            const SizedBox(height: 20),
            
            // Step 3: Recording
            _buildSection(
              context,
              '📝',
              localizations.instructionsStep3Title,
              localizations.instructionsStep3Content,
              Colors.green.shade100,
            ),
            const SizedBox(height: 20),
            
            // Step 4: Latent Values
            _buildSection(
              context,
              '💎',
              localizations.instructionsStep4Title,
              localizations.instructionsStep4Content,
              Colors.purple.shade100,
            ),
            const SizedBox(height: 20),
            
            // Future AI Integration
            _buildSection(
              context,
              '🤖',
              localizations.instructionsFutureTitle,
              localizations.instructionsFutureContent,
              Colors.cyan.shade100,
            ),
            const SizedBox(height: 20),
            
            // Pro Tips
            _buildSection(
              context,
              '💡',
              localizations.instructionsTipsTitle,
              localizations.instructionsTipsContent,
              Colors.yellow.shade100,
            ),
            const SizedBox(height: 40),
            
            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () => Navigator.pushReplacementNamed(context, '/'),
                  icon: const Icon(Icons.play_arrow),
                  label: Text(localizations.getStartedButton),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2A6BAA),
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => Navigator.pushNamed(context, '/onboarding_presentation'),
                  icon: const Icon(Icons.slideshow),
                  label: Text(localizations.viewOnboardingMenu),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade600,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context,
    String emoji,
    String title,
    String content,
    Color backgroundColor,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: backgroundColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                emoji,
                style: const TextStyle(fontSize: 24),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              height: 1.5,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }
}
