import 'package:flutter/material.dart';
import 'package:estimat_keymoments/l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import '../models/moment_data.dart';
import '../providers/moments_provider.dart';
import '../widgets/app_menu.dart';
import '../widgets/value_bar_chart.dart';
import '../widgets/dynamic_factorial_chart.dart';

class SummaryScreen extends StatefulWidget {
  const SummaryScreen({super.key});

  @override
  State<SummaryScreen> createState() => _SummaryScreenState();
}

class _SummaryScreenState extends State<SummaryScreen> {
  String _selectedPeriod = 'last_week';
  DateTime? _startDate;
  DateTime? _endDate;
  bool _useCustomDateRange = false;



  void _updatePeriod(String period) {
    setState(() {
      _selectedPeriod = period;
      _useCustomDateRange = false;
      _startDate = null;
      _endDate = null;
    });
  }

  void _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now().subtract(const Duration(days: 30)),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _startDate = picked;
        _useCustomDateRange = true;
      });
    }
  }

  void _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now(),
      firstDate: _startDate ?? DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _endDate = picked;
        _useCustomDateRange = true;
      });
    }
  }

  void _resetDateFilter() {
    setState(() {
      _useCustomDateRange = false;
      _startDate = null;
      _endDate = null;
      _selectedPeriod = 'last_week';
    });
  }

  List<MomentData> _filterMomentsByDateRange(List<MomentData> moments) {
    if (!_useCustomDateRange || _startDate == null || _endDate == null) {
      return moments;
    }

    final startOfDay = DateTime(_startDate!.year, _startDate!.month, _startDate!.day);
    final endOfDay = DateTime(_endDate!.year, _endDate!.month, _endDate!.day, 23, 59, 59);

    return moments.where((moment) {
      return moment.timestamp.isAfter(startOfDay.subtract(const Duration(seconds: 1))) &&
             moment.timestamp.isBefore(endOfDay.add(const Duration(seconds: 1)));
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final momentsProvider = Provider.of<MomentsProvider>(context);
    final moments = momentsProvider.moments;

    if (moments.isEmpty) {
      return Scaffold(
        appBar: AppBar(
          title: Text(localizations.summaryTitle),
          actions: const [
            AppMenu(),
          ],
        ),
        body: Center(
          child: Text(localizations.noMomentsRecordedYet),
        ),
      );
    }

    // Get the most recent moment
    moments.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    final latestMoment = moments.first;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.summaryTitle),
        backgroundColor: Colors.blueGrey[800],
        actions: const [
          AppMenu(),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Dominant focus aspect box at the top
            _buildDominantFocusAspectBox(context, latestMoment),
            const SizedBox(height: 24),
            _buildLatentValuesSection(context, latestMoment, localizations),
            const SizedBox(height: 24),
            _buildLifePossibilitiesChartSection(context, moments, localizations),
            const SizedBox(height: 24),
            _buildMomentsHistorySection(context, moments, localizations),
            const SizedBox(height: 24),
            // Action buttons row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: ElevatedButton.icon(
                      onPressed: () {
                        _showSuccessDialog(context, latestMoment);
                      },
                      icon: const Icon(Icons.save),
                      label: Text(localizations.saveButton),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green[700],
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pushNamed(context, '/data_export');
                      },
                      icon: const Icon(Icons.download),
                      label: Text(localizations.exportDataButton),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blueGrey[700],
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: ElevatedButton.icon(
                      onPressed: () {
                        _editCurrentMoment(context, latestMoment);
                      },
                      icon: const Icon(Icons.edit),
                      label: Text(localizations.editThisMomentButton),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange[700],
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLifePossibilitiesChartSection(BuildContext context, List<MomentData> moments, AppLocalizations localizations) {
    if (moments.isEmpty) {
      return const SizedBox.shrink();
    }

    // Apply date range filtering if custom range is selected
    final filteredMoments = _filterMomentsByDateRange(moments);

    return Card(
      color: Colors.orange[50],
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizations.lifePossibilitiesChartTitle,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Period filter dropdown
            Row(
              children: [
                Text(localizations.periodLabel),
                DropdownButton<String>(
                  value: _useCustomDateRange ? 'custom' : _selectedPeriod,
                  onChanged: (String? newValue) {
                    if (newValue != null && newValue != 'custom') {
                      _updatePeriod(newValue);
                    }
                  },
                  items: [
                    DropdownMenuItem(value: 'last_week', child: Text(localizations.last7DaysFilter)),
                    DropdownMenuItem(value: 'last_month', child: Text(localizations.lastMonthFilter)),
                    DropdownMenuItem(value: 'last_3_months', child: Text(localizations.last3MonthsFilter)),
                    DropdownMenuItem(value: 'all', child: Text(localizations.allTimeFilter)),
                    DropdownMenuItem(value: 'custom', child: Text(localizations.customRangeFilter)),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Custom date range controls
            Card(
              color: Colors.grey[100],
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      localizations.customDateRangeFilterTitle,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(localizations.startDateLabel, style: const TextStyle(fontWeight: FontWeight.w500)),
                              const SizedBox(height: 4),
                              InkWell(
                                onTap: _selectStartDate,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(4),
                                    color: Colors.white,
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.calendar_today, size: 16),
                                      const SizedBox(width: 8),
                                      Text(
                                        _startDate != null
                                          ? '${_startDate!.day}/${_startDate!.month}/${_startDate!.year}'
                                          : localizations.selectStartDateHint,
                                        style: TextStyle(
                                          color: _startDate != null ? Colors.black : Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(localizations.endDateLabel, style: const TextStyle(fontWeight: FontWeight.w500)),
                              const SizedBox(height: 4),
                              InkWell(
                                onTap: _selectEndDate,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(4),
                                    color: Colors.white,
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.calendar_today, size: 16),
                                      const SizedBox(width: 8),
                                      Text(
                                        _endDate != null
                                          ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}'
                                          : localizations.selectEndDateHint,
                                        style: TextStyle(
                                          color: _endDate != null ? Colors.black : Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        ElevatedButton.icon(
                          onPressed: _resetDateFilter,
                          icon: const Icon(Icons.refresh, size: 16),
                          label: Text(localizations.resetButton),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[300],
                            foregroundColor: Colors.black87,
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          ),
                        ),
                        const SizedBox(width: 8),
                        if (_useCustomDateRange && _startDate != null && _endDate != null)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.green[100],
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.green[300]!),
                            ),
                            child: Text(
                              localizations.customRangeActiveLabel,
                              style: TextStyle(
                                color: Colors.green[800],
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Chart
            SizedBox(
              height: 450, // Further increased height to prevent overflow
              child: DynamicFactorialChart(
                moments: filteredMoments,
                currentSliderValue: 1.0, // Default value for summary view
                isCurrentImprovement: true, // Default for summary view
                selectedPeriod: _useCustomDateRange ? 'all' : _selectedPeriod,
                onPeriodChanged: _updatePeriod,
                currentPreviewValue: null, // No preview in summary view
                excludeMomentId: null, // No exclusions in summary view
                showPreview: false, // Don't show preview in summary view
              ),
            ),

            // Chart info
            if (filteredMoments.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                localizations.showingMomentsLabel(
                  filteredMoments.length,
                  filteredMoments.length != 1 ? 's' : ''
                ) +
                (_useCustomDateRange && _startDate != null && _endDate != null
                  ? ' from ${_startDate!.day}/${_startDate!.month}/${_startDate!.year} to ${_endDate!.day}/${_endDate!.month}/${_endDate!.year}'
                  : ''),
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }



  Widget _buildLatentValuesSection(BuildContext context, MomentData moment, AppLocalizations localizations) {
    return Card(
      color: Colors.grey[100],
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Latent Values Bar Chart
            _buildLatentValuesChart(moment, localizations),
          ],
        ),
      ),
    );
  }

  Widget _buildLatentValuesChart(MomentData moment, AppLocalizations localizations) {
    // Keep English keys for the widget but pass localization for display
    final latentValues = {
      'Guardian': moment.guardianPercentage,
      'Warrior': moment.warriorPercentage,
      'Versatile': moment.versatilePercentage,
      'Fun': moment.funPercentage,
      'Strategist': moment.strategistPercentage,
      'Tactical': moment.tacticalPercentage,
      'Altruist': moment.altruistPercentage,
      'Collaborator': moment.collaboratorPercentage,
    };

    final colors = {
      'Guardian': const Color(0xFF6A0F36),
      'Warrior': const Color(0xFF5B1106),
      'Versatile': const Color(0xFF332444),
      'Fun': const Color(0xFF794B19),
      'Strategist': const Color(0xFF1E293B),
      'Tactical': const Color(0xFF5E4E0F),
      'Altruist': const Color(0xFF0F5040),
      'Collaborator': const Color(0xFF254110),
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.latentValuesTitle,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ValueBarChart(
          values: latentValues,
          colors: colors,
          title: localizations.latentValuesTitle,
          isInward: false,
          nameMapper: (englishName) {
            // Map English names to Portuguese localized names
            switch (englishName) {
              case 'Guardian':
                return localizations.valueGuardian;
              case 'Warrior':
                return localizations.valueWarrior;
              case 'Versatile':
                return localizations.valueVersatile;
              case 'Fun':
                return localizations.valueFunny;
              case 'Strategist':
                return localizations.valueStrategist;
              case 'Tactical':
                return localizations.valueTactician;
              case 'Altruist':
                return localizations.valueAltruistic;
              case 'Collaborator':
                return localizations.valueCollaborator;
              default:
                return englishName;
            }
          },
          subtitleMapper: (englishName) {
            // Map English names to Portuguese localized subtitles
            switch (englishName) {
              case 'Guardian':
                return localizations.subtitleNurturer;
              case 'Warrior':
                return localizations.subtitleReleaser;
              case 'Versatile':
                return localizations.subtitleSelfSeer;
              case 'Fun':
                return localizations.subtitleEnthusiastic;
              case 'Strategist':
                return localizations.subtitleAnalyst;
              case 'Tactical':
                return localizations.subtitleSynthesizer;
              case 'Altruist':
                return localizations.subtitleEmpathetic;
              case 'Collaborator':
                return localizations.subtitleDiplomat;
              default:
                return '';
            }
          },
          descriptionMapper: (englishName) {
            // Map English names to Portuguese localized descriptions
            switch (englishName) {
              case 'Guardian':
                return localizations.guardianValueDescription.replaceAll('\\n', '\n');
              case 'Warrior':
                return localizations.warriorValueDescription.replaceAll('\\n', '\n');
              case 'Versatile':
                return localizations.versatileValueDescription.replaceAll('\\n', '\n');
              case 'Fun':
                return localizations.funValueDescription.replaceAll('\\n', '\n');
              case 'Strategist':
                return localizations.strategistValueDescription.replaceAll('\\n', '\n');
              case 'Tactical':
                return localizations.tacticalValueDescription.replaceAll('\\n', '\n');
              case 'Altruist':
                return localizations.altruistValueDescription.replaceAll('\\n', '\n');
              case 'Collaborator':
                return localizations.collaboratorValueDescription.replaceAll('\\n', '\n');
              default:
                return localizations.descriptionNotAvailable;
            }
          },
        ),
      ],
    );
  }



  Widget _buildDominantFocusAspectBox(BuildContext context, MomentData moment) {
    final dominantValue = moment.highlightValue;
    final dominantColor = _getColorForLatentValue(dominantValue);

    return Card(
      color: dominantColor,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: Column(
            children: [
              // Hierarchical value names (smaller above, larger below)
              Text(
                _getDisplayName(context, dominantValue, isSmall: true),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _getDisplayName(context, dominantValue, isSmall: false),
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: () => _showValueDescription(context, dominantValue),
                    child: const Icon(
                      Icons.info_outline,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // User-given title
              Text(
                moment.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMomentsHistorySection(BuildContext context, List<MomentData> moments, AppLocalizations localizations) {
    return Card(
      color: Colors.grey[100], // Changed to neutral color
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizations.momentsHistoryTitle,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: moments.length > 5 ? 5 : moments.length,
              itemBuilder: (context, index) {
                final moment = moments[index];
                return Container(
                  margin: const EdgeInsets.only(bottom: 8.0),
                  decoration: BoxDecoration(
                    color: Colors.grey[50], // Neutral background
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: ListTile(
                    title: Text(moment.title),
                    subtitle: Text(
                      '${moment.type.name} - ${moment.timestamp.toString().substring(0, 16)}',
                    ),
                    leading: CircleAvatar(
                      backgroundColor: _getColorForLatentValue(moment.highlightValue),
                      child: Text(
                        moment.highlightValue.substring(0, 1),
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    onTap: () {
                      // Show moment details with edit option
                      _showMomentDetails(context, moment, localizations);
                    },
                  ),
                );
              },
            ),
            if (moments.length > 5)
              TextButton(
                onPressed: () {
                  // Navigate to full history
                },
                child: Text(localizations.viewAllMomentsButton),
              ),
          ],
        ),
      ),
    );
  }

  void _showMomentDetails(BuildContext context, MomentData moment, AppLocalizations localizations) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(moment.title),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Type: ${moment.type.name}'),
              const SizedBox(height: 8),
              Text('Description: ${moment.description}'),
              const SizedBox(height: 8),
              Text('Highlight: ${moment.highlightValue}'),
              const SizedBox(height: 8),

              // Hierarchical percentages
              Text('Elemental: ${moment.elementalPercentage.round()}%'),
              Text('Personal: ${moment.personalPercentage.round()}%'),
              Text('Informational: ${moment.informationalPercentage.round()}%'),
              Text('Social: ${moment.socialPercentage.round()}%'),
              const SizedBox(height: 8),

              // Orientation percentages
              Text('Inward: ${moment.inwardPercentage.round()}%'),
              Text('Outward: ${moment.outwardPercentage.round()}%'),
              const SizedBox(height: 8),

              // Evidence fields (previously missing)
              if (moment.elementalPersonalEvidence.isNotEmpty) ...[
                Text('Elemental-Personal Evidence: ${moment.elementalPersonalEvidence}'),
                const SizedBox(height: 4),
              ],
              if (moment.personalInformationalEvidence.isNotEmpty) ...[
                Text('Personal-Informational Evidence: ${moment.personalInformationalEvidence}'),
                const SizedBox(height: 4),
              ],
              if (moment.informationalSocialEvidence.isNotEmpty) ...[
                Text('Informational-Social Evidence: ${moment.informationalSocialEvidence}'),
                const SizedBox(height: 4),
              ],
              if (moment.inwardOutwardEvidence.isNotEmpty) ...[
                Text('Inward-Outward Evidence: ${moment.inwardOutwardEvidence}'),
                const SizedBox(height: 4),
              ],
              if (moment.momentTypeEvidence.isNotEmpty) ...[
                Text('Moment Type Evidence: ${moment.momentTypeEvidence}'),
                const SizedBox(height: 4),
              ],

              // Factorial comparison data
              const SizedBox(height: 8),
              Text(localizations.factorialComparisonLabel(moment.isImprovementComparedToLife ? localizations.improvementLabel : localizations.worseningLabel)),
              Text(localizations.factorialSliderValueLabel(moment.factorialSliderValue.toStringAsFixed(2))),
              Text(localizations.factorialMultiplierLabel(moment.factorialMultiplier.toStringAsFixed(2))),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text(localizations.closeButton),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _editCurrentMoment(context, moment);
            },
            child: Text(localizations.editThisMomentButton),
          ),
        ],
      ),
    );
  }

  String _getDisplayName(BuildContext context, String value, {required bool isSmall}) {
    final localizations = AppLocalizations.of(context)!;

    // Use the same localization keys as latent_values_screen.dart and improve_worse_comparison_screen.dart
    switch (value) {
      case 'Guardian':
        return isSmall ? localizations.nurturerSubtitle : localizations.valueGuardian;
      case 'Warrior':
        return isSmall ? localizations.releaserSubtitle : localizations.valueWarrior;
      case 'Versatile':
        return isSmall ? localizations.subtitleSelfSeer : localizations.valueVersatile;
      case 'Fun':
        return isSmall ? localizations.subtitleEnthusiastic : localizations.valueFunny;
      case 'Strategist':
        return isSmall ? localizations.subtitleAnalyst : localizations.valueStrategist;
      case 'Tactical':
        return isSmall ? localizations.subtitleSynthesizer : localizations.valueTactician;
      case 'Altruist':
        return isSmall ? localizations.subtitleEmpathetic : localizations.valueAltruistic;
      case 'Collaborator':
        return isSmall ? localizations.subtitleDiplomat : localizations.valueCollaborator;
      default:
        return value;
    }
  }

  void _showValueDescription(BuildContext context, String latentValue) {
    final description = _getValueDescription(context, latentValue);
    final subtitle = _getValueSubtitle(context, latentValue);
    final localizedName = _getDisplayName(context, latentValue, isSmall: false);
    final localizations = AppLocalizations.of(context)!;
    final valueColor = _getColorForLatentValue(latentValue);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: valueColor,
          title: Text(
            subtitle.isNotEmpty
              ? '$subtitle $localizedName'.toUpperCase()
              : localizedName.toUpperCase(),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: Colors.white,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Show the explanation/description below the dual title
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                localizations.closeButton,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  String _getValueSubtitle(BuildContext context, String value) {
    final localizations = AppLocalizations.of(context)!;

    // Get the subtitle (small name) for each latent value
    switch (value) {
      case 'Guardian':
        return localizations.subtitleNurturer;
      case 'Warrior':
        return localizations.subtitleReleaser;
      case 'Versatile':
        return localizations.subtitleSelfSeer;
      case 'Fun':
        return localizations.subtitleEnthusiastic;
      case 'Strategist':
        return localizations.subtitleAnalyst;
      case 'Tactical':
        return localizations.subtitleSynthesizer;
      case 'Altruist':
        return localizations.subtitleEmpathetic;
      case 'Collaborator':
        return localizations.subtitleDiplomat;
      default:
        return '';
    }
  }

  String _getValueDescription(BuildContext context, String value) {
    final localizations = AppLocalizations.of(context)!;

    // Use the same localization keys as latent_values_screen.dart and improve_worse_comparison_screen.dart
    switch (value) {
      case 'Guardian':
        return localizations.guardianValueDescription.replaceAll('\\n', '\n');
      case 'Warrior':
        return localizations.warriorValueDescription.replaceAll('\\n', '\n');
      case 'Versatile':
        return localizations.versatileValueDescription.replaceAll('\\n', '\n');
      case 'Fun':
        return localizations.funValueDescription.replaceAll('\\n', '\n');
      case 'Strategist':
        return localizations.strategistValueDescription.replaceAll('\\n', '\n');
      case 'Tactical':
        return localizations.tacticalValueDescription.replaceAll('\\n', '\n');
      case 'Altruist':
        return localizations.altruistValueDescription.replaceAll('\\n', '\n');
      case 'Collaborator':
        return localizations.collaboratorValueDescription.replaceAll('\\n', '\n');
      default:
        return localizations.descriptionNotAvailable;
    }
  }

  Color _getColorForLatentValue(String value) {
    switch (value) {
      case 'Guardian':
        return const Color(0xFF6A0F36);
      case 'Warrior':
        return const Color(0xFF5B1106);
      case 'Versatile':
        return const Color(0xFF332444);
      case 'Fun':
        return const Color(0xFF794B19);
      case 'Strategist':
        return const Color(0xFF1E293B);
      case 'Tactical':
        return const Color(0xFF5E4E0F);
      case 'Altruist':
        return const Color(0xFF0F5040);
      case 'Collaborator':
        return const Color(0xFF254110);
      default:
        return Colors.grey;
    }
  }

  void _showSuccessDialog(BuildContext context, MomentData savedMoment) {
    final localizations = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.momentSavedTitle(savedMoment.type.name)),
        content: Text(localizations.momentSavedMessage),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _editCurrentMoment(context, savedMoment);
            },
            child: Text(localizations.editThisMomentButton),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to create a new moment
              Navigator.pushReplacementNamed(context, '/');
            },
            child: Text(localizations.insertNewMomentButton),
          ),
        ],
      ),
    );
  }

  void _editCurrentMoment(BuildContext context, MomentData moment) {
    final momentsProvider = Provider.of<MomentsProvider>(context, listen: false);
    // Set the current moment for editing and navigate back
    momentsProvider.setCurrentMoment(moment);
    Navigator.pushReplacementNamed(context, '/');
  }
}
