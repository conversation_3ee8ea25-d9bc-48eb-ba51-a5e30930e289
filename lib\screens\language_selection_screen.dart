import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:estimat_keymoments/l10n/app_localizations.dart';
import '../providers/locale_provider.dart';
import '../providers/user_preferences_provider.dart';

class LanguageSelectionScreen extends StatelessWidget {
  const LanguageSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    // final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.selectLanguageTitle),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () => _selectLanguage(context, const Locale('en')),
              child: Text(localizations.englishLanguage),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _selectLanguage(context, const Locale('es')),
              child: Text(localizations.spanishLanguage),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _selectLanguage(context, const Locale('pt')),
              child: Text(localizations.portugueseLanguage),
            ),
          ],
        ),
      ),
    );
  }

  void _selectLanguage(BuildContext context, Locale locale) async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final userPreferencesProvider = Provider.of<UserPreferencesProvider>(context, listen: false);

    await localeProvider.setLocale(locale);

    final savedRoute = await localeProvider.getSavedRoute();
    final currentRoute = ModalRoute.of(context)?.settings.name ?? '/';

    if (savedRoute != currentRoute && savedRoute != '/language_selection') {
      if (context.mounted) {
        Navigator.pushReplacementNamed(context, savedRoute);
      }
    } else {
      // Check if user wants to see onboarding or skip to main app
      if (userPreferencesProvider.isFirstTimeUser && userPreferencesProvider.showOnboarding) {
        if (context.mounted) {
          Navigator.pushReplacementNamed(context, '/onboarding_presentation');
        }
      } else {
        if (context.mounted) {
          Navigator.pushReplacementNamed(context, '/');
        }
      }
    }
  }
}
