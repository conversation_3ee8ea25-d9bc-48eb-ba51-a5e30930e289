import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../l10n/app_localizations.dart';
import '../models/moment_data.dart';

class DynamicFactorialChart extends StatelessWidget {
  final List<MomentData> moments;
  final double currentSliderValue;
  final bool isCurrentImprovement;
  final String selectedPeriod;
  final ValueChanged<String>? onPeriodChanged;
  final double? currentPreviewValue; // Custom preview value
  final String? excludeMomentId; // ID of moment to exclude from saved moments (currently being edited)
  final bool showPreview; // Whether to show the preview point

  const DynamicFactorialChart({
    super.key,
    required this.moments,
    required this.currentSliderValue,
    required this.isCurrentImprovement,
    this.selectedPeriod = 'all',
    this.onPeriodChanged,
    this.currentPreviewValue,
    this.excludeMomentId,
    this.showPreview = true, // Default to showing preview
  });

  // Calculate factorial value (same logic as in MomentData)
  static double _calculateFactorial(double sliderValue) {
    final clampedValue = sliderValue.clamp(0.1, 100.0);

    // For values below 1, return the fractional value directly (not factorial)
    if (clampedValue < 1.0) {
      return clampedValue; // 0.1x to 0.9x represent fractions of life baseline
    }

    if (clampedValue <= 20.0) {
      int intValue = clampedValue.round();
      double result = 1.0;
      for (int i = 1; i <= intValue; i++) {
        result *= i;
      }
      return result;
    }

    final n = clampedValue;
    return math.sqrt(2 * math.pi * n) * math.pow(n / math.e, n);
  }

  List<MomentData> _filterMomentsByPeriod() {
    // First exclude the moment being edited (if any)
    var filteredMoments = moments;
    if (excludeMomentId != null) {
      filteredMoments = moments.where((moment) => moment.id != excludeMomentId).toList();
    }

    if (selectedPeriod == 'all') return filteredMoments;

    final now = DateTime.now();
    DateTime cutoffDate;

    switch (selectedPeriod) {
      case 'last_week':
        cutoffDate = now.subtract(const Duration(days: 7));
        break;
      case 'last_month':
        cutoffDate = now.subtract(const Duration(days: 30));
        break;
      case 'last_3_months':
        cutoffDate = now.subtract(const Duration(days: 90));
        break;
      default:
        return filteredMoments;
    }

    return filteredMoments.where((moment) => moment.timestamp.isAfter(cutoffDate)).toList();
  }

  double _getYAxisMax(List<MomentData> filteredMoments, double currentValue) {
    final allValues = [
      1.0, // Life baseline
      currentValue, // Current slider value
      ...filteredMoments.map((m) => m.factorialMultiplier),
    ];

    final maxValue = allValues.reduce((a, b) => math.max(a, b));

    // Add some padding to the max value
    if (maxValue < 10) return 10.0;
    if (maxValue < 100) return (maxValue * 1.2).ceilToDouble();
    if (maxValue < 1000) return ((maxValue / 100).ceil() * 100).toDouble();
    return (maxValue * 1.1);
  }

  Color _getPointColor(double value, bool isImprovement) {
    // Life baseline is always blue
    if (value == 1.0) return Colors.blue[600]!;

    // Current preview point
    if (value == _calculateFactorial(currentSliderValue)) {
      return isImprovement ? Colors.green[800]! : Colors.red[800]!;
    }

    // Extreme values
    if (value >= 100) {
      return Colors.green[800]!;
    }

    // Normal values
    return isImprovement ? Colors.green[600]! : Colors.red[600]!;
  }

  double _getPointSize(double value) {
    // Life baseline is larger
    if (value == 1.0) return 8.0;

    // Current preview point is larger
    if (value == _calculateFactorial(currentSliderValue)) return 10.0;

    // Extreme values are larger
    if (value >= 100) return 8.0;

    return 6.0;
  }

  @override
  Widget build(BuildContext context) {
    final filteredMoments = _filterMomentsByPeriod();
    final currentFactorialValue = currentPreviewValue ?? _calculateFactorial(currentSliderValue);

    // Create data points: Life baseline + existing moments + current preview
    final dataPoints = <FlSpot>[];
    final pointColors = <Color>[];
    final pointSizes = <double>[];
    final labels = <String>[];

    if (filteredMoments.isEmpty) {
      // No moments yet - show life baseline and optionally current preview
      dataPoints.add(const FlSpot(0, 1.0));
      pointColors.add(Colors.blue[600]!);
      pointSizes.add(8.0);
      labels.add(AppLocalizations.of(context)!.lifeLabel);

      // Add current preview point only if showPreview is true
      if (showPreview) {
        dataPoints.add(FlSpot(1.0, currentFactorialValue));
        pointColors.add(_getPointColor(currentFactorialValue, isCurrentImprovement));
        pointSizes.add(_getPointSize(currentFactorialValue));
        labels.add(AppLocalizations.of(context)!.previewLabel);
      }
    } else {
      // Sort moments by timestamp to ensure chronological order
      final sortedMoments = List<MomentData>.from(filteredMoments);
      sortedMoments.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      // Always show life baseline as the first point (index 0)
      dataPoints.add(const FlSpot(0, 1.0));
      pointColors.add(Colors.blue[600]!);
      pointSizes.add(8.0);
      labels.add(AppLocalizations.of(context)!.lifeLabel);

      // Add existing moments starting from index 1, all showing their actual factorial values
      for (int i = 0; i < sortedMoments.length; i++) {
        final moment = sortedMoments[i];
        final xPosition = (i + 1).toDouble(); // Start from position 1

        // All moments should show their actual recorded factorial multiplier
        final yValue = moment.factorialMultiplier;

        dataPoints.add(FlSpot(xPosition, yValue));
        pointColors.add(_getPointColor(yValue, moment.isImprovementComparedToLife));
        pointSizes.add(_getPointSize(yValue));
        labels.add('Moment ${i + 1}');
      }

      // Add current preview point only if showPreview is true
      if (showPreview) {
        final previewIndex = (sortedMoments.length + 1).toDouble();
        dataPoints.add(FlSpot(previewIndex, currentFactorialValue));
        pointColors.add(_getPointColor(currentFactorialValue, isCurrentImprovement));
        pointSizes.add(_getPointSize(currentFactorialValue));
        labels.add(AppLocalizations.of(context)!.previewLabel);
      }
    }

    final yMax = _getYAxisMax(filteredMoments, currentFactorialValue);

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(12), // Reduced padding to save space
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with period selector
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    AppLocalizations.of(context)!.lifePossibilitiesChart,
                    style: const TextStyle(
                      fontSize: 18, // Reduced font size
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
                const SizedBox(width: 8),
                if (onPeriodChanged != null)
                  Flexible(
                    child: DropdownButton<String>(
                      value: selectedPeriod,
                      onChanged: (value) => onPeriodChanged!(value!),
                      isExpanded: false,
                      items: [
                        DropdownMenuItem(value: 'all', child: Text(AppLocalizations.of(context)!.allTimeFilter, overflow: TextOverflow.ellipsis)),
                        DropdownMenuItem(value: 'last_week', child: Text(AppLocalizations.of(context)!.lastWeekFilter, overflow: TextOverflow.ellipsis)),
                        DropdownMenuItem(value: 'last_month', child: Text(AppLocalizations.of(context)!.lastMonthFilter, overflow: TextOverflow.ellipsis)),
                        DropdownMenuItem(value: 'last_3_months', child: Text(AppLocalizations.of(context)!.last3MonthsFilter, overflow: TextOverflow.ellipsis)),
                      ],
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12), // Reduced spacing

            // Current value display - only show if showPreview is true
            if (showPreview) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isCurrentImprovement ? Colors.green[50] : Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isCurrentImprovement ? Colors.green[300]! : Colors.red[300]!,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.currentPreviewLabel,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isCurrentImprovement ? Colors.green[800] : Colors.red[800],
                      ),
                    ),
                    Text(
                      '${currentFactorialValue.toStringAsFixed(1)}x',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                        color: isCurrentImprovement ? Colors.green[800] : Colors.red[800],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12), // Reduced spacing
            ],

            // Chart
            SizedBox(
              height: 250, // Reduced internal chart height to prevent overflow
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: true,
                    horizontalInterval: yMax > 100 ? yMax / 5 : 10,
                    verticalInterval: 1,
                    getDrawingHorizontalLine: (value) {
                      // Highlight the baseline (1x)
                      if ((value - 1.0).abs() < 0.1) {
                        return FlLine(
                          color: Colors.blue,
                          strokeWidth: 3,
                          dashArray: [5, 5],
                        );
                      }
                      return FlLine(
                        color: Colors.grey[300]!,
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        interval: 1,
                        getTitlesWidget: (value, meta) {
                          final index = value.toInt();
                          if (index >= 0 && index < labels.length) {
                            String label;
                            Color color = Colors.grey;

                            if (filteredMoments.isEmpty) {
                              // No moments yet - show Life and optionally Preview
                              if (index == 0) {
                                label = AppLocalizations.of(context)!.lifeLabel;
                                color = Colors.blue;
                              } else {
                                label = AppLocalizations.of(context)!.previewLabel;
                                color = Colors.orange;
                              }
                            } else {
                              // With moments: Life (index 0), Moments (1 to n), optionally Preview (n+1)
                              if (index == 0) {
                                label = AppLocalizations.of(context)!.lifeLabel;
                                color = Colors.blue;
                              } else if (showPreview && index == labels.length - 1) {
                                label = AppLocalizations.of(context)!.previewLabel;
                                color = Colors.orange;
                              } else {
                                label = '$index'; // Show moment number (1, 2, 3, etc.)
                                color = Colors.grey;
                              }
                            }

                            return Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Text(
                                label,
                                style: TextStyle(
                                  color: color,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(color: const Color(0xff37434d)),
                  ),
                  minX: 0,
                  maxX: dataPoints.length > 1 ? (dataPoints.length - 1).toDouble() : 1,
                  minY: 0.1, // Allow values below life baseline for "fewer possibilities"
                  maxY: yMax,
                  lineBarsData: [
                    LineChartBarData(
                      spots: dataPoints,
                      isCurved: false, // Disable curve to prevent going below baseline
                      color: Colors.blue[600]!, // Single color instead of gradient
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          if (index < pointColors.length) {
                            return FlDotCirclePainter(
                              radius: pointSizes[index],
                              color: pointColors[index],
                              strokeWidth: 2,
                              strokeColor: Colors.white,
                            );
                          }
                          return FlDotCirclePainter(
                            radius: 6,
                            color: Colors.grey,
                            strokeWidth: 2,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                      belowBarData: BarAreaData(show: false),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 12), // Reduced spacing

            // Legend
            Wrap(
              spacing: 12, // Reduced spacing
              runSpacing: 6, // Reduced spacing
              children: [
                _buildLegendItem(Colors.blue[600]!, AppLocalizations.of(context)!.lifeBaselineLabel),
                _buildLegendItem(Colors.green[600]!, AppLocalizations.of(context)!.morePossibilitiesLabel),
                _buildLegendItem(Colors.red[600]!, AppLocalizations.of(context)!.fewerPossibilitiesLabel),
                if (showPreview)
                  _buildLegendItem(Colors.orange, AppLocalizations.of(context)!.currentPreviewLegend, isDashed: true),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(Color color, String label, {bool isDashed = false}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 3,
          decoration: BoxDecoration(
            color: isDashed ? null : color,
            border: isDashed ? Border.all(color: color) : null,
          ),
          child: isDashed
              ? CustomPaint(
                  painter: DashedLinePainter(color: color),
                )
              : null,
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }
}

class DashedLinePainter extends CustomPainter {
  final Color color;

  DashedLinePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2;

    const dashWidth = 3.0;
    const dashSpace = 2.0;
    double startX = 0;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(math.min(startX + dashWidth, size.width), size.height / 2),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
