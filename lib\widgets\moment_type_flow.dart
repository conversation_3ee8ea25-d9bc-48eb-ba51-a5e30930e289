import 'package:flutter/material.dart';
import 'package:estimat_keymoments/l10n/app_localizations.dart';
import '../models/moment_type.dart';
import '../models/moment_data.dart';
import 'proportion_slider.dart';
import 'proportion_pie_chart.dart';
import 'nested_bubble_chart.dart';

class MomentTypeFlow extends StatefulWidget {
  final MomentType momentType;
  final Function(MomentData) onComplete;
  final Function(MomentType)? onMomentTypeSelected;

  const MomentTypeFlow({
    super.key,
    required this.momentType,
    required this.onComplete,
    this.onMomentTypeSelected,
  });

  @override
  State<MomentTypeFlow> createState() => _MomentTypeFlowState();
}

// Add a mixin to expose the reset method and editing functionality
mixin MomentTypeFlowMixin {
  void resetForm();
  void loadMomentForEditing(MomentData moment);
}

class _MomentTypeFlowState extends State<MomentTypeFlow> with MomentTypeFlowMixin {
  int _currentStep = 0;
  int _currentLevelSubStep = 0; // Track which level comparison is active (0=Elemental vs Personal, 1=Personal vs Informational, 2=Informational vs Social)
  final _formKey = GlobalKey<FormState>();

  // Form values
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _elementalPersonalEvidenceController = TextEditingController();
  final _personalInformationalEvidenceController = TextEditingController();
  final _informationalSocialEvidenceController = TextEditingController();
  final _inwardOutwardEvidenceController = TextEditingController();

  // New state variables for moment type selection
  bool _isImproves = true; // Default to Improvements
  bool _isMotivation = true; // Default to Motivation
  bool _isSatisfaction = false; // Default to Motivation (exclusive choice handled below)

  // State variables for nested proportions (0-1)
  double _personalProportionOfElemental = 0.0;
  double _informationalProportionOfPersonal = 0.0;
  double _socialProportionOfInformational = 0.0;

  // Calculated absolute percentages (relative to Elemental)
  double _elementalPercentage = 100.0;
  double _personalPercentage = 0.0;
  double _informationalPercentage = 0.0;
  double _socialPercentage = 0.0;

  double _inwardPercentage = 50.0;
  double _outwardPercentage = 50.0;

  // Editing state
  bool _isEditingMode = false;
  String? _editingMomentId;

  // Evidence fields removed

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _elementalPersonalEvidenceController.dispose();
    _personalInformationalEvidenceController.dispose();
    _informationalSocialEvidenceController.dispose();
    _inwardOutwardEvidenceController.dispose();
    super.dispose();
  }

  @override
  void resetForm() {
    setState(() {
      // Reset step counter
      _currentStep = 0;
      _currentLevelSubStep = 0;

      // Clear text controllers
      _titleController.clear();
      _descriptionController.clear();
      _elementalPersonalEvidenceController.clear();
      _personalInformationalEvidenceController.clear();
      _informationalSocialEvidenceController.clear();
      _inwardOutwardEvidenceController.clear();

      // Reset moment type selection to defaults
      _isImproves = true;
      _isMotivation = true;
      _isSatisfaction = false;

      // Reset proportions to defaults
      _personalProportionOfElemental = 0.0;
      _informationalProportionOfPersonal = 0.0;
      _socialProportionOfInformational = 0.0;

      // Reset calculated percentages to defaults
      _elementalPercentage = 100.0;
      _personalPercentage = 0.0;
      _informationalPercentage = 0.0;
      _socialPercentage = 0.0;

      // Reset inward/outward percentages to defaults
      _inwardPercentage = 50.0;
      _outwardPercentage = 50.0;

      // Reset editing state
      _isEditingMode = false;
      _editingMomentId = null;
    });

    // Reset form validation state
    _formKey.currentState?.reset();
  }

  @override
  void loadMomentForEditing(MomentData moment) {
    setState(() {
      // Reset to first step for editing
      _currentStep = 0;

      // Load moment data into form controllers
      _titleController.text = moment.title;
      _descriptionController.text = moment.description;
      _elementalPersonalEvidenceController.text = moment.elementalPersonalEvidence;
      _personalInformationalEvidenceController.text = moment.personalInformationalEvidence;
      _informationalSocialEvidenceController.text = moment.informationalSocialEvidence;
      _inwardOutwardEvidenceController.text = moment.inwardOutwardEvidence;

      // Set moment type selection based on the moment's type
      _isImproves = moment.type.isImproves;
      _isMotivation = moment.type.isMotivation;
      _isSatisfaction = !moment.type.isMotivation;

      // Load the percentages
      _inwardPercentage = moment.inwardPercentage;
      _outwardPercentage = moment.outwardPercentage;

      // Load hierarchical percentages and calculate proportions
      _elementalPercentage = moment.elementalPercentage;
      _personalPercentage = moment.personalPercentage;
      _informationalPercentage = moment.informationalPercentage;
      _socialPercentage = moment.socialPercentage;

      // Calculate the proportions from the absolute percentages
      _calculateProportionsFromPercentages();

      // Set editing state
      _isEditingMode = true;
      _editingMomentId = moment.id;
    });
  }

  void _nextStep() {
    if (_currentStep == 0 && !_formKey.currentState!.validate()) {
      return;
    }

    // Determine the MomentType based on the selections in the first step
    MomentType selectedMomentType;
    if (_isImproves) {
      selectedMomentType = _isMotivation ? MomentType.improvesMotivation : MomentType.improvesSatisfaction;
    } else {
      selectedMomentType = _isMotivation ? MomentType.worsensMotivation : MomentType.worsensSatisfaction;
    }

    // Notify the parent widget about the selected moment type
    widget.onMomentTypeSelected?.call(selectedMomentType);

    // You might want to store the selectedMomentType in the state if needed for later steps
    // For now, we'll just use it when completing the moment.

    if (_currentStep < 2) { // Now we have 3 steps total (0: moment type, 1: orientation, 2: levels)
      setState(() {
        _currentStep++;
      });
    } else if (_currentStep == 2) {
      // Handle navigation within the levels step
      if (_currentLevelSubStep < 2) {
        setState(() {
          _currentLevelSubStep++;
        });
      } else {
        _completeMoment();
      }
    } else {
      _completeMoment();
    }
  }

  void _previousStep() {
    if (_currentStep == 2 && _currentLevelSubStep > 0) {
      // Navigate back within the levels step
      setState(() {
        _currentLevelSubStep--;
      });
    } else if (_currentStep > 0) {
      setState(() {
        _currentStep--;
        if (_currentStep == 2) {
          _currentLevelSubStep = 0; // Reset to first substep when entering levels step
        }
      });
    }
  }

  void _completeMoment() {
     // Determine the MomentType based on the selections in the first step
    MomentType selectedMomentType;
    if (_isImproves) {
      selectedMomentType = _isMotivation ? MomentType.improvesMotivation : MomentType.improvesSatisfaction;
    } else {
      selectedMomentType = _isMotivation ? MomentType.worsensMotivation : MomentType.worsensSatisfaction;
    }

    // Calculate the correct hierarchical percentages for data storage
    final hierarchicalPercentages = _calculateHierarchicalPercentages();

    final moment = MomentData(
      id: _isEditingMode ? _editingMomentId : null, // Preserve ID when editing
      type: selectedMomentType, // Use the determined moment type
      title: _titleController.text,
      description: _descriptionController.text,
      elementalPercentage: hierarchicalPercentages['elemental']!,
      personalPercentage: hierarchicalPercentages['personal']!,
      informationalPercentage: hierarchicalPercentages['informational']!,
      socialPercentage: hierarchicalPercentages['social']!,
      inwardPercentage: _inwardPercentage,
      outwardPercentage: _outwardPercentage,
      momentTypeEvidence: '', // Evidence fields removed
      elementalPersonalEvidence: _elementalPersonalEvidenceController.text,
      personalInformationalEvidence: _personalInformationalEvidenceController.text,
      informationalSocialEvidence: _informationalSocialEvidenceController.text,
      inwardOutwardEvidence: _inwardOutwardEvidenceController.text,
    );

    // Pass the moment to the parent widget
    widget.onComplete(moment);

    // Navigate to the comparison screen
    Navigator.pushNamed(
      context,
      '/improve_worse_comparison',
      arguments: {
        'momentType': widget.momentType,
        'currentMoment': moment,
      },
    );
  }

  void _updateElementalPersonal(double value) {
    setState(() {
      // value is the proportion of Elemental (outer bubble) within Elemental (0-1)
      // The proportion of Personal (inner bubble) is 1 - value
      _personalProportionOfElemental = 1.0 - value;
      // Recalculate absolute percentages
      _calculateAbsolutePercentages();
    });
  }

  void _updatePersonalInformational(double value) {
    setState(() {
      // value is the proportion of Personal (outer bubble) within Personal (0-1)
      // The proportion of Informational (inner bubble) is 1 - value
      _informationalProportionOfPersonal = 1.0 - value;
      // Recalculate absolute percentages
      _calculateAbsolutePercentages();
    });
  }

  void _updateInformationalSocial(double value) {
    setState(() {
      // value is the proportion of Informational (outer bubble) within Informational (0-1)
      // The proportion of Social (inner bubble) is 1 - value
      _socialProportionOfInformational = 1.0 - value;
      // Recalculate absolute percentages
      _calculateAbsolutePercentages();
    });
  }

  void _calculateAbsolutePercentages() {
    // Calculate absolute percentages based on nested proportions for UI display
    // This keeps the bubble chart working correctly
    _elementalPercentage = 100.0; // Elemental is always 100% of the total for display
    _personalPercentage = _elementalPercentage * _personalProportionOfElemental;
    _informationalPercentage = _personalPercentage * _informationalProportionOfPersonal;
    _socialPercentage = _informationalPercentage * _socialProportionOfInformational;
  }

  // Calculate the correct hierarchical percentages for CSV export
  Map<String, double> _calculateHierarchicalPercentages() {
    // Slider 1: Elemental percentage is set directly
    double elementalPercent = (1.0 - _personalProportionOfElemental) * 100.0;

    // Remaining percentage after Elemental is allocated
    double remainingAfterElemental = 100.0 - elementalPercent;

    // Slider 2: Personal gets a proportion of the remaining
    double personalPercent = remainingAfterElemental * (1.0 - _informationalProportionOfPersonal);

    // Remaining percentage after Personal is allocated
    double remainingAfterPersonal = remainingAfterElemental - personalPercent;

    // Slider 3: Informational gets a proportion of the remaining
    double informationalPercent = remainingAfterPersonal * (1.0 - _socialProportionOfInformational);

    // Social gets whatever is left
    double socialPercent = remainingAfterPersonal - informationalPercent;

    return {
      'elemental': elementalPercent,
      'personal': personalPercent,
      'informational': informationalPercent,
      'social': socialPercent,
    };
  }

  // Reverse calculation: from absolute percentages to proportions
  void _calculateProportionsFromPercentages() {
    // Calculate proportions from the loaded percentages
    // This reverses the hierarchical calculation logic

    double elementalPercent = _elementalPercentage;
    double personalPercent = _personalPercentage;
    double informationalPercent = _informationalPercentage;

    // Calculate remaining amounts at each level
    double remainingAfterElemental = 100.0 - elementalPercent;
    double remainingAfterPersonal = remainingAfterElemental - personalPercent;

    // Calculate proportions (reverse of the hierarchical calculation)
    if (remainingAfterElemental > 0) {
      _informationalProportionOfPersonal = 1.0 - (personalPercent / remainingAfterElemental);
    } else {
      _informationalProportionOfPersonal = 0.0;
    }

    if (remainingAfterPersonal > 0) {
      _socialProportionOfInformational = 1.0 - (informationalPercent / remainingAfterPersonal);
    } else {
      _socialProportionOfInformational = 0.0;
    }

    // Personal proportion of elemental
    _personalProportionOfElemental = 1.0 - (elementalPercent / 100.0);

    // Ensure proportions are within valid range [0, 1]
    _personalProportionOfElemental = _personalProportionOfElemental.clamp(0.0, 1.0);
    _informationalProportionOfPersonal = _informationalProportionOfPersonal.clamp(0.0, 1.0);
    _socialProportionOfInformational = _socialProportionOfInformational.clamp(0.0, 1.0);

    // Recalculate absolute percentages for UI consistency
    _calculateAbsolutePercentages();
  }

  void _updateInwardOutward(double value) {
    setState(() {
      _inwardPercentage = value * 100;
      _outwardPercentage = (1 - value) * 100;
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Stepper(
      currentStep: _currentStep,
      onStepTapped: (step) {
        setState(() {
          _currentStep = step;
        });
      },
      controlsBuilder: (context, details) {
        // For step 0 (moment type selection), we have custom buttons in the content
        if (_currentStep == 0) {
          return Container(); // Return empty container to hide default controls
        }

        return Padding(
          padding: const EdgeInsets.only(top: 16.0),
          child: Row(
            children: [
              ElevatedButton(
                onPressed: _nextStep,
                child: Text(_currentStep < 4
                  ? localizations.continueButton
                  : localizations.saveButton),
              ),
              if (_currentStep > 0)
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: TextButton(
                    onPressed: _previousStep,
                    child: Text(localizations.backButton),
                  ),
                ),
            ],
          ),
        );
      },
      steps: [
        Step(
          title: Row(
            children: [
              Text(localizations.recognizeYourMoment), // Use localization for title
              const SizedBox(width: 8),
              GestureDetector(
                onTap: () => _showWhyMomentsDialog(context),
                child: Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                  size: 20,
                ),
              ),
            ],
          ), // Use localization for title
          content: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 16),
                Text(localizations.selectImprovementWorsening), // Label for exclusive choice
                Row(
                  children: [
                    ChoiceChip(
                      label: Text(localizations.improvesLabel),
                      selected: _isImproves,
                      onSelected: (selected) {
                        setState(() {
                          _isImproves = selected;
                          // Determine and call the callback with the new MomentType
                          MomentType selectedMomentType;
                          if (_isImproves) {
                            selectedMomentType = _isMotivation ? MomentType.improvesMotivation : MomentType.improvesSatisfaction;
                          } else {
                            selectedMomentType = _isMotivation ? MomentType.worsensMotivation : MomentType.worsensSatisfaction;
                          }
                          widget.onMomentTypeSelected?.call(selectedMomentType);
                        });
                      },
                    ),
                    const SizedBox(width: 8),
                    ChoiceChip(
                      label: Text(localizations.worsensLabel),
                      selected: !_isImproves,
                      onSelected: (selected) {
                        setState(() {
                          _isImproves = !selected;
                           // Determine and call the callback with the new MomentType
                          MomentType selectedMomentType;
                          if (_isImproves) {
                            selectedMomentType = _isMotivation ? MomentType.improvesMotivation : MomentType.improvesSatisfaction;
                          } else {
                            selectedMomentType = _isMotivation ? MomentType.worsensMotivation : MomentType.worsensSatisfaction;
                          }
                          widget.onMomentTypeSelected?.call(selectedMomentType);
                        });
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(localizations.selectMotivationSatisfaction), // Label for inclusive choice
                Row(
                  children: [
                    ChoiceChip(
                      label: Text(localizations.motivationLabel),
                      selected: _isMotivation,
                      onSelected: (selected) {
                        setState(() {
                          _isMotivation = selected;
                          // If Motivation is selected, Satisfaction must be deselected for exclusive choice
                          if (selected) _isSatisfaction = false;
                           // Determine and call the callback with the new MomentType
                          MomentType selectedMomentType;
                          if (_isImproves) {
                            selectedMomentType = _isMotivation ? MomentType.improvesMotivation : MomentType.improvesSatisfaction;
                          } else {
                            selectedMomentType = _isMotivation ? MomentType.worsensMotivation : MomentType.worsensSatisfaction;
                          }
                          widget.onMomentTypeSelected?.call(selectedMomentType);
                        });
                      },
                    ),
                    const SizedBox(width: 8),
                     ChoiceChip(
                      label: Text(localizations.satisfactionLabel),
                      selected: _isSatisfaction,
                      onSelected: (selected) {
                        setState(() {
                          _isSatisfaction = selected;
                           // If Satisfaction is selected, Motivation must be deselected for exclusive choice
                          if (selected) _isMotivation = false;
                           // Determine and call the callback with the new MomentType
                          MomentType selectedMomentType;
                          if (_isImproves) {
                            selectedMomentType = _isMotivation ? MomentType.improvesMotivation : MomentType.improvesSatisfaction;
                          } else {
                            selectedMomentType = _isMotivation ? MomentType.worsensMotivation : MomentType.worsensSatisfaction;
                          }
                          widget.onMomentTypeSelected?.call(selectedMomentType);
                        });
                      },
                    ),
                  ],
                ),
                 const SizedBox(height: 16),
                TextFormField(
                  controller: _titleController,
                  decoration: InputDecoration(
                    labelText: localizations.titleInputHint,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return localizations.pleaseEnterTitle;
                    }
                    final words = value.trim().split(' ');
                    if (words.length != 2) {
                      return localizations.titleMustBeTwoWords;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _descriptionController,
                  decoration: InputDecoration(
                    labelText: localizations.descriptionInputHint,
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 24),

                // Instructional text with clickable "Why prioritize" at the end
                RichText(
                  text: TextSpan(
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.normal,
                      color: Colors.grey[600],
                    ),
                    children: [
                      TextSpan(
                        text: localizations.organizeTheMomentAndLookForEvidence,
                      ),
                      TextSpan(text: ' '),
                      WidgetSpan(
                        child: GestureDetector(
                          onTap: () => _showWhyHierarchicalDialog(context),
                          child: Icon(
                            Icons.info_outline,
                            color: Colors.blue,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Continue and Skip for now buttons
                Row(
                  children: [
                    ElevatedButton(
                      onPressed: _nextStep,
                      child: Text(localizations.continueButton),
                    ),
                    const SizedBox(width: 16),
                    TextButton(
                      onPressed: () => _skipToLifePossibilities(context),
                      child: Text(
                        localizations.skipForNowButton,
                        style: TextStyle(
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ],
                ),

              ],
            ),
          ),
          isActive: _currentStep >= 0,
        ),
        Step(
          title: Text(
            localizations.orientationTitle,
            style: const TextStyle(fontSize: 14),
            overflow: TextOverflow.visible,
            softWrap: true,
            maxLines: 3,
          ),
          content: Column(
            children: [
              Text(
                localizations.orientationQuestion,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.normal,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 16),
              ProportionSlider(
                leftLabel: localizations.inwardLabel,
                rightLabel: localizations.outwardLabel,
                value: _inwardPercentage / 100,
                onChanged: _updateInwardOutward,
                leftColor: Colors.black,
                rightColor: Colors.white,
              ),
              const SizedBox(height: 16),
              // Clickable icons for explanations
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildExplanationIcon(
                    context,
                    localizations.inwardLabel,
                    localizations.inwardExplanation,
                    Colors.black,
                    Icons.wifi,
                    isInward: true,
                  ),
                  _buildExplanationIcon(
                    context,
                    localizations.outwardLabel,
                    localizations.outwardExplanation,
                    Colors.white,
                    Icons.wifi,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              ProportionPieChart(
                inwardPercentage: _inwardPercentage,
                outwardPercentage: _outwardPercentage,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _inwardOutwardEvidenceController,
                decoration: InputDecoration(
                  labelText: localizations.pieStepEvidenceQuestion,
                  hintText: localizations.describeDistributionHint,
                ),
                maxLines: 2,
              ),

            ],
          ),
          isActive: _currentStep >= 1,
        ),
        Step(
          title: Text(
            localizations.threeLevelsTitle,
            style: const TextStyle(fontSize: 14),
            overflow: TextOverflow.visible,
            softWrap: true,
          ),
          content: _buildLevelsContent(context, localizations),
          isActive: _currentStep >= 2,
        ),
      ],
    );
  }

  Widget _buildLevelsContent(BuildContext context, AppLocalizations localizations) {
    // Define the three level comparisons
    final levelComparisons = [
      {
        'title': localizations.pieStepElementalPersonalTitle,
        'description': localizations.pieStepElementalPersonalDescription,
        'leftLabel': localizations.elementalLabel,
        'rightLabel': localizations.personalLabel,
        'leftColor': const Color(0xFF404040),
        'rightColor': const Color(0xFF595959),
        'leftIcon': Icons.bubble_chart,
        'rightIcon': Icons.favorite,
        'leftExplanation': localizations.elementalExplanation,
        'rightExplanation': localizations.personalExplanation,
        'value': 1.0 - _personalProportionOfElemental,
        'onChanged': _updateElementalPersonal,
        'controller': _elementalPersonalEvidenceController,
      },
      {
        'title': localizations.pieStepPersonalInformationalTitle,
        'description': localizations.pieStepPersonalInformationalDescription,
        'leftLabel': localizations.personalLabel,
        'rightLabel': localizations.informationalLabel,
        'leftColor': const Color(0xFF595959),
        'rightColor': const Color(0xFF808080),
        'leftIcon': Icons.favorite,
        'rightIcon': Icons.lightbulb,
        'leftExplanation': localizations.personalExplanation,
        'rightExplanation': localizations.informationalExplanation,
        'value': 1.0 - _informationalProportionOfPersonal,
        'onChanged': _updatePersonalInformational,
        'controller': _personalInformationalEvidenceController,
      },
      {
        'title': localizations.pieStepInformationalSocialTitle,
        'description': localizations.pieStepInformationalSocialDescription,
        'leftLabel': localizations.informationalLabel,
        'rightLabel': localizations.socialLabel,
        'leftColor': const Color(0xFF808080),
        'rightColor': const Color(0xFF9F9F9F),
        'leftIcon': Icons.lightbulb,
        'rightIcon': Icons.handshake,
        'leftExplanation': localizations.informationalExplanation,
        'rightExplanation': localizations.socialExplanation,
        'value': 1.0 - _socialProportionOfInformational,
        'onChanged': _updateInformationalSocial,
        'controller': _informationalSocialEvidenceController,
      },
    ];

    final currentComparison = levelComparisons[_currentLevelSubStep];

    return Column(
      children: [
        // Progress indicator for substeps
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(3, (index) {
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: index == _currentLevelSubStep
                    ? Colors.blue
                    : Colors.grey[300],
              ),
            );
          }),
        ),
        const SizedBox(height: 16),

        // Current comparison title
        Text(
          currentComparison['title'] as String,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),

        // Current comparison description
        Text(
          currentComparison['description'] as String,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.normal,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),

        // Slider
        ProportionSlider(
          leftLabel: currentComparison['leftLabel'] as String,
          rightLabel: currentComparison['rightLabel'] as String,
          value: currentComparison['value'] as double,
          onChanged: currentComparison['onChanged'] as Function(double),
          leftColor: currentComparison['leftColor'] as Color,
          rightColor: currentComparison['rightColor'] as Color,
        ),
        const SizedBox(height: 16),

        // Explanation icons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildExplanationIcon(
              context,
              currentComparison['leftLabel'] as String,
              currentComparison['leftExplanation'] as String,
              currentComparison['leftColor'] as Color,
              currentComparison['leftIcon'] as IconData,
            ),
            _buildExplanationIcon(
              context,
              currentComparison['rightLabel'] as String,
              currentComparison['rightExplanation'] as String,
              currentComparison['rightColor'] as Color,
              currentComparison['rightIcon'] as IconData,
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Bubble chart
        NestedBubbleChart(
          elementalPercentage: _elementalPercentage,
          personalPercentage: _personalPercentage,
          informationalPercentage: _informationalPercentage,
          socialPercentage: _socialPercentage,
          showElemental: true,
          showPersonal: true,
          showInformational: true,
          showSocial: true,
          hierarchicalPercentages: _calculateHierarchicalPercentages(),
        ),
        const SizedBox(height: 16),

        // Evidence text field
        TextFormField(
          controller: currentComparison['controller'] as TextEditingController,
          decoration: InputDecoration(
            labelText: localizations.pieStepEvidenceQuestion,
            hintText: localizations.describeDistributionHint,
          ),
          maxLines: 2,
        ),
        const SizedBox(height: 16),

        // Navigation buttons for substeps
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (_currentLevelSubStep > 0)
              TextButton(
                onPressed: () {
                  setState(() {
                    _currentLevelSubStep--;
                  });
                },
                child: Text(localizations.backButton),
              )
            else
              const SizedBox.shrink(),

            if (_currentLevelSubStep < 2)
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _currentLevelSubStep++;
                  });
                },
                child: Text(localizations.continueButton),
              )
            else
              ElevatedButton(
                onPressed: _nextStep,
                child: Text(localizations.continueButton),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildExplanationIcon(
    BuildContext context,
    String title,
    String explanation,
    Color color,
    IconData icon, {
    bool isInward = false,
  }) {
    return GestureDetector(
      onTap: () {
        _showExplanationDialog(context, title, explanation);
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: isInward
                ? Transform.rotate(
                    angle: 3.14159, // Rotate 180 degrees (pi radians)
                    child: Icon(
                      icon,
                      color: color == Colors.white ? Colors.black : Colors.white,
                      size: 28,
                    ),
                  )
                : Icon(
                    icon,
                    color: color == Colors.white ? Colors.black : Colors.white,
                    size: 28,
                  ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.underline,
              decorationColor: Colors.blue,
              color: Colors.blue,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.visible,
            softWrap: true,
          ),
        ],
      ),
    );
  }

  void _showExplanationDialog(BuildContext context, String title, String explanation) {
    final localizations = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(explanation),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text(localizations.closeButton),
          ),
        ],
      ),
    );
  }

  void _showWhyMomentsDialog(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localizations.whyMomentsTitle,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              Divider(),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildWhyMomentsContent(context, localizations),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWhyMomentsContent(BuildContext context, AppLocalizations localizations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Center(
          child: Column(
            children: [
              ShaderMask(
                shaderCallback: (bounds) => LinearGradient(
                  colors: [Color(0xFF4F46E5), Color(0xFF7C3AED)],
                ).createShader(bounds),
                child: Text(
                  'ESTIMAT',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              SizedBox(height: 8),
              Text(
                localizations.whyMomentsSubtitle,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 24),

        // Introduction
        _buildInfoSection(
          localizations.whyRegisterMomentsTitle,
          localizations.whyRegisterMomentsContent,
          [Color(0xFF6366F1), Color(0xFF7C3AED)],
          Icons.psychology,
        ),
        SizedBox(height: 16),

        // High-High
        _buildInfoSection(
          localizations.highMotivationHighSatisfactionTitle,
          localizations.highMotivationHighSatisfactionContent,
          [Color(0xFF10B981), Color(0xFF059669)],
          Icons.trending_up,
        ),
        SizedBox(height: 16),

        // High-Low
        _buildInfoSection(
          localizations.highMotivationLowSatisfactionTitle,
          localizations.highMotivationLowSatisfactionContent,
          [Color(0xFFF97316), Color(0xFFEF4444)],
          Icons.gps_fixed,
        ),
        SizedBox(height: 16),

        // Low-High
        _buildInfoSection(
          localizations.lowMotivationHighSatisfactionTitle,
          localizations.lowMotivationHighSatisfactionContent,
          [Color(0xFFEC4899), Color(0xFFE11D48)],
          Icons.favorite,
        ),
        SizedBox(height: 16),

        // Low-Low
        _buildInfoSection(
          localizations.lowMotivationLowSatisfactionTitle,
          localizations.lowMotivationLowSatisfactionContent,
          [Color(0xFF6B7280), Color(0xFF475569)],
          Icons.lightbulb,
        ),
        SizedBox(height: 16),

        // Footer
        Center(
          child: Text(
            localizations.scientificFooter,
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoSection(String title, String content, List<Color> gradient, IconData icon) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: gradient),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: gradient[0].withValues(alpha: 0.3),
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Icon(icon, color: Colors.white),
        title: Text(
          title,
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        collapsedIconColor: Colors.white,
        iconColor: Colors.white,
        children: [
          Container(
            padding: EdgeInsets.all(16),
            color: Colors.white,
            child: Text(
              content,
              style: TextStyle(
                color: Colors.grey[800],
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showWhyHierarchicalDialog(BuildContext context) {
    // final localizations = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Why Hierarchical Organization',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              Divider(),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildWhyHierarchicalContent(context),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWhyHierarchicalContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Center(
          child: Column(
            children: [
              ShaderMask(
                shaderCallback: (bounds) => LinearGradient(
                  colors: [Color(0xFF4F46E5), Color(0xFF7C3AED)],
                ).createShader(bounds),
                child: Text(
                  'ESTIMAT',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Why Hierarchical Organization of Life Moments Might Matter',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 24),

        // Introduction
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.yellow[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.yellow[200]!),
          ),
          child: Text(
            'Important: If you\'re already experiencing consistent well-being and deep satisfaction, additional organization is likely unnecessary. This approach seems most relevant during transitions, complex decisions, or persistent dissatisfaction.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.yellow[800],
            ),
          ),
        ),
        SizedBox(height: 16),

        // Key sections
        _buildInfoSection(
          'Data Visualization & Bias Reduction',
          'Hierarchical moment visualization might counteract availability bias by providing a more objective representation of temporal patterns. Visual perception of proportions appears to be significantly more accurate than narrative memories.',
          [Color(0xFF10B981), Color(0xFF059669)],
          Icons.bar_chart,
        ),
        SizedBox(height: 16),

        _buildInfoSection(
          'Experimental Evidence',
          'Hierarchical organization might improve recall by approximately 200% compared to random presentation. After repeated unstructured decisions, decision quality likely declines significantly.',
          [Color(0xFF3B82F6), Color(0xFF6366F1)],
          Icons.emoji_events,
        ),
        SizedBox(height: 16),

        _buildInfoSection(
          'Evolutionary Psychology Perspective',
          'Modern humans possibly face approximately 35,000 daily decisions, while our ancestors probably encountered 70-100 structured decisions in predictable social hierarchies.',
          [Color(0xFF8B5CF6), Color(0xFF6366F1)],
          Icons.psychology,
        ),
        SizedBox(height: 16),

        _buildInfoSection(
          'Information Theory Perspective',
          'Hierarchical organization probably achieves optimal data compression. Applied to life experiences, this could allow processing exponentially more information.',
          [Color(0xFFF97316), Color(0xFFEF4444)],
          Icons.flash_on,
        ),
        SizedBox(height: 16),

        // Footer
        Center(
          child: Text(
            'Based on scientific research • Designed for your personal growth',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  void _skipToLifePossibilities(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    // Create a minimal moment with default values to skip the organization steps
    MomentType selectedMomentType;
    if (_isImproves) {
      selectedMomentType = _isMotivation ? MomentType.improvesMotivation : MomentType.improvesSatisfaction;
    } else {
      selectedMomentType = _isMotivation ? MomentType.worsensMotivation : MomentType.worsensSatisfaction;
    }

    final moment = MomentData(
      type: selectedMomentType,
      title: _titleController.text.isNotEmpty ? _titleController.text : localizations.untitledMoment,
      description: _descriptionController.text,
      // Set default equal percentages for all levels (25% each)
      elementalPercentage: 25.0,
      personalPercentage: 25.0,
      informationalPercentage: 25.0,
      socialPercentage: 25.0,
      // Set default inward/outward split (50/50)
      inwardPercentage: 50.0,
      outwardPercentage: 50.0,
      momentTypeEvidence: '',
      elementalPersonalEvidence: '',
      personalInformationalEvidence: '',
      informationalSocialEvidence: '',
      inwardOutwardEvidence: '',
    );

    // Pass the moment to the parent widget
    widget.onComplete(moment);

    // Navigate directly to the life possibilities factor screen
    Navigator.pushNamed(
      context,
      '/improve_worse_comparison',
      arguments: {
        'momentType': widget.momentType,
        'currentMoment': moment,
      },
    );
  }
}
