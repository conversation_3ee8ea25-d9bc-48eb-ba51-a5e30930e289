import 'package:flutter/material.dart';
import 'package:estimat_keymoments/l10n/app_localizations.dart';
import '../widgets/app_menu.dart';

class WhyMomentsScreen extends StatefulWidget {
  const WhyMomentsScreen({super.key});

  @override
  State<WhyMomentsScreen> createState() => _WhyMomentsScreenState();
}

class _WhyMomentsScreenState extends State<WhyMomentsScreen> {
  Map<String, bool> expandedSections = {};

  void toggleSection(String sectionId) {
    setState(() {
      expandedSections[sectionId] = !(expandedSections[sectionId] ?? false);
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.whyMomentsScreenTitle),
        backgroundColor: const Color(0xFF2A6BAA),
        foregroundColor: Colors.white,
        actions: const [
          AppMenu(),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF8FAFC),
              Color(0xFFF1F5F9),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Header
                _buildHeader(localizations),
                SizedBox(height: 32),

                // Sections
                ..._buildSections(localizations),

                SizedBox(height: 48),

                // Footer
                _buildFooter(),
              ],
            ),
          ),
        )
      ),
    );
  }

  Widget _buildHeader(AppLocalizations localizations) {
    return Column(
      children: [
        ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: [Color(0xFF4F46E5), Color(0xFF7C3AED)],
          ).createShader(bounds),
          child: Text(
            'ESTIMAT',
            style: TextStyle(
              fontSize: 36,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        SizedBox(height: 8),
        Text(
          localizations.whyMomentsHeaderSubtitle,
          style: TextStyle(
            fontSize: 18,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  List<Widget> _buildSections(AppLocalizations localizations) {
    final sections = [
      EmotionSection(
        id: 'intro',
        title: localizations.whyRegisterMomentsObjectivelyTitle,
        icon: Icons.psychology,
        gradient: [Color(0xFF6366F1), Color(0xFF7C3AED)],
        content: _buildIntroContent(localizations),
      ),
      EmotionSection(
        id: 'high-high',
        title: localizations.highMotivationHighSatisfactionTitle,
        icon: Icons.trending_up,
        gradient: [Color(0xFF10B981), Color(0xFF059669)],
        content: _buildHighHighContent(localizations),
      ),
      EmotionSection(
        id: 'high-low',
        title: localizations.highMotivationLowSatisfactionTitle,
        icon: Icons.gps_fixed,
        gradient: [Color(0xFFF97316), Color(0xFFEF4444)],
        content: _buildHighLowContent(localizations),
      ),
      EmotionSection(
        id: 'low-high',
        title: localizations.lowMotivationHighSatisfactionTitle,
        icon: Icons.favorite,
        gradient: [Color(0xFFEC4899), Color(0xFFE11D48)],
        content: _buildLowHighContent(localizations),
      ),
      EmotionSection(
        id: 'low-low',
        title: localizations.lowMotivationLowSatisfactionTitle,
        icon: Icons.lightbulb,
        gradient: [Color(0xFF6B7280), Color(0xFF475569)],
        content: _buildLowLowContent(localizations),
      ),
      EmotionSection(
        id: 'summary',
        title: localizations.generalOverviewTitle,
        icon: Icons.bar_chart,
        gradient: [Color(0xFF8B5CF6), Color(0xFF6366F1)],
        content: _buildSummaryContent(localizations),
      ),
    ];

    return sections.map((section) => Padding(
      padding: EdgeInsets.only(bottom: 16),
      child: _buildSectionWidget(section),
    )).toList();
  }

  Widget _buildSectionWidget(EmotionSection section) {
    bool isExpanded = expandedSections[section.id] ?? false;

    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      child: Column(
        children: [
          GestureDetector(
            onTap: () => toggleSection(section.id),
            child: Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: section.gradient),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: section.gradient[0].withOpacity(0.3),
                    blurRadius: 8,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(section.icon, color: Colors.white, size: 24),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      section.title,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.keyboard_arrow_down : Icons.keyboard_arrow_right,
                    color: Colors.white,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
          AnimatedCrossFade(
            firstChild: Container(),
            secondChild: Container(
              margin: EdgeInsets.only(top: 8),
              child: section.content,
            ),
            crossFadeState: isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
            duration: Duration(milliseconds: 300),
          ),
        ],
      ),
    );
  }

  Widget _buildStatHighlight(String number, String text, List<Color> colors) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: colors),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: colors[0].withOpacity(0.3),
            blurRadius: 6,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        number,
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildContentCard(Widget child, {Color? borderColor}) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: borderColor != null ? Border.all(color: borderColor, width: 2) : null,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }

  Widget _buildIntroContent(AppLocalizations localizations) {
    return _buildContentCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localizations.whyRegisterMomentsObjectivelyContent,
            style: TextStyle(
              color: Colors.grey[700],
              height: 1.6,
              fontSize: 15,
            ),
          ),
          SizedBox(height: 16),
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFFDEF7FF), Color(0xFFF3E8FF)],
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              localizations.whyRegisterMomentsObjectivelyHighlight,
              style: TextStyle(
                color: Colors.grey[800],
                fontWeight: FontWeight.w500,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHighHighContent(AppLocalizations localizations) {
    return _buildContentCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoBox(
            localizations.researchInfoBoxTitle,
            localizations.researchInfoBoxContent,
            Color(0xFFF0FDF4),
            Color(0xFF16A34A),
          ),
          SizedBox(height: 12),
          _buildInfoBox(
            localizations.evolutionaryViewInfoBoxTitle,
            localizations.evolutionaryViewInfoBoxContent,
            Color(0xFFFEFCE8),
            Color(0xFFCA8A04),
          ),
          SizedBox(height: 12),
          _buildInfoBox(
            localizations.practiceEstimatInfoBoxTitle,
            localizations.practiceEstimatInfoBoxContent,
            Color(0xFFEFF6FF),
            Color(0xFF2563EB),
          ),
        ],
      ),
    );
  }

  Widget _buildHighLowContent(AppLocalizations localizations) {
    return _buildContentCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localizations.highMotivationLowSatisfactionIntro,
            style: TextStyle(color: Colors.grey[700], fontSize: 15),
          ),
          SizedBox(height: 16),
          _buildInfoBox(
            localizations.impactBiasInfoBoxTitle,
            localizations.impactBiasInfoBoxContent,
            Color(0xFFFFF7ED),
            Color(0xFFEA580C),
            extraWidget: _buildStatHighlight('40-60%', '', [Color(0xFFF97316), Color(0xFFEF4444)]),
          ),
          SizedBox(height: 12),
          _buildInfoBox(
            localizations.practiceInfoBoxTitle,
            localizations.practiceInfoBoxContent,
            Color(0xFFFAF5FF),
            Color(0xFF9333EA),
          ),
        ],
      ),
    );
  }

  Widget _buildLowHighContent(AppLocalizations localizations) {
    return _buildContentCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localizations.lowMotivationHighSatisfactionIntro,
            style: TextStyle(color: Colors.grey[700], fontSize: 15),
          ),
          SizedBox(height: 16),
          _buildInfoBox(
            localizations.pleasureUnderestimationInfoBoxTitle,
            localizations.pleasureUnderestimationInfoBoxContent,
            Color(0xFFFDF2F8),
            Color(0xFFEC4899),
            extraWidget: _buildStatHighlight('20-30%', '', [Color(0xFFEC4899), Color(0xFFE11D48)]),
          ),
          SizedBox(height: 12),
          _buildInfoBox(
            localizations.effortParadoxInfoBoxTitle,
            localizations.effortParadoxInfoBoxContent,
            Color(0xFFF0FDF4),
            Color(0xFF16A34A),
          ),
        ],
      ),
    );
  }

  Widget _buildLowLowContent(AppLocalizations localizations) {
    return _buildContentCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localizations.lowMotivationLowSatisfactionIntro,
            style: TextStyle(color: Colors.grey[700], fontSize: 15),
          ),
          SizedBox(height: 16),
          _buildInfoBox(
            localizations.reflectionPowerInfoBoxTitle,
            localizations.reflectionPowerInfoBoxContent,
            Color(0xFFEFF6FF),
            Color(0xFF2563EB),
            extraWidget: _buildStatHighlight('20%', '', [Color(0xFF3B82F6), Color(0xFF6366F1)]),
          ),
          SizedBox(height: 12),
          _buildInfoBox(
            localizations.practiceEstimatLowInfoBoxTitle,
            localizations.practiceEstimatLowInfoBoxContent,
            Color(0xFFFEFCE8),
            Color(0xFFCA8A04),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryContent(AppLocalizations localizations) {
    return _buildContentCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localizations.generalOverviewIntro,
            style: TextStyle(color: Colors.grey[700], fontSize: 15),
          ),
          SizedBox(height: 20),
          GridView.count(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              _buildStatCard(localizations.memoryBiasStatTitle, localizations.memoryBiasStatContent, [Color(0xFFFEF2F2), Color(0xFFFED7AA)]),
              _buildStatCard(localizations.impactBiasStatTitle, localizations.impactBiasStatContent, [Color(0xFFEFF6FF), Color(0xFFE0F2FE)], stat: '40-60%'),
              _buildStatCard(localizations.underestimationStatTitle, localizations.underestimationStatContent, [Color(0xFFF0FDF4), Color(0xFFDCFCE7)], stat: '20-30%'),
              _buildStatCard(localizations.recoveryStatTitle, localizations.recoveryStatContent, [Color(0xFFFAF5FF), Color(0xFFF3E8FF)], stat: '20%'),
            ],
          ),
          SizedBox(height: 20),
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFFEEF2FF), Color(0xFFF3E8FF)],
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              localizations.generalOverviewConclusion,
              style: TextStyle(
                color: Colors.grey[800],
                fontWeight: FontWeight.w500,
                height: 1.5,
                fontSize: 15,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoBox(String title, String content, Color bgColor, Color titleColor, {Widget? extraWidget}) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border(left: BorderSide(color: titleColor, width: 4)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: titleColor,
              fontSize: 14,
            ),
          ),
          SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Text(
                  content,
                  style: TextStyle(
                    color: Colors.grey[700],
                    height: 1.5,
                    fontSize: 14,
                  ),
                ),
              ),
              if (extraWidget != null) extraWidget,
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String subtitle, List<Color> colors, {String? stat}) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: colors),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
              fontSize: 12,
            ),
          ),
          SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              color: Colors.grey[700],
              fontSize: 11,
              height: 1.3,
            ),
          ),
          if (stat != null) ...[
            SizedBox(height: 8),
            _buildStatHighlight(stat, '', [Color(0xFF6366F1), Color(0xFF8B5CF6)]),
          ],
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Text(
      'Basado en investigación científica • Diseñado para tu crecimiento personal',
      style: TextStyle(
        color: Colors.grey[500],
        fontSize: 12,
      ),
      textAlign: TextAlign.center,
    );
  }
}

class EmotionSection {
  final String id;
  final String title;
  final IconData icon;
  final List<Color> gradient;
  final Widget content;

  EmotionSection({
    required this.id,
    required this.title,
    required this.icon,
    required this.gradient,
    required this.content,
  });
}
