import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class UserPreferencesProvider extends ChangeNotifier {
  bool _showOnboarding = true;
  bool _isFirstTimeUser = true;
  
  bool get showOnboarding => _showOnboarding;
  bool get isFirstTimeUser => _isFirstTimeUser;
  
  UserPreferencesProvider() {
    loadPreferences();
  }
  
  Future<void> loadPreferences() async {
    final prefs = await SharedPreferences.getInstance();

    // Check if this is truly the first time by looking for any saved preference
    final hasAnyPreference = prefs.getKeys().isNotEmpty;

    _showOnboarding = prefs.getBool('showOnboarding') ?? true;
    _isFirstTimeUser = prefs.getBool('isFirstTimeUser') ?? !hasAnyPreference;

    // If no preferences exist at all, this is definitely a first-time user
    if (!hasAnyPreference) {
      _isFirstTimeUser = true;
      _showOnboarding = true;
    }

    notifyListeners();
  }
  
  Future<void> setShowOnboarding(bool value) async {
    if (_showOnboarding == value) return;
    
    _showOnboarding = value;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('showOnboarding', value);
    
    notifyListeners();
  }
  
  Future<void> setFirstTimeUser(bool value) async {
    if (_isFirstTimeUser == value) return;
    
    _isFirstTimeUser = value;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isFirstTimeUser', value);
    
    notifyListeners();
  }
  
  Future<void> markOnboardingCompleted() async {
    await setFirstTimeUser(false);
    // Don't automatically disable onboarding - let user choose
  }
  
  Future<void> resetToFirstTime() async {
    await setFirstTimeUser(true);
    await setShowOnboarding(true);
  }
}
