import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../models/moment_data.dart';
import '../models/moment_type.dart';
import '../providers/moments_provider.dart';
import '../widgets/app_menu.dart';
import '../widgets/factorial_slider.dart';
import '../widgets/dynamic_factorial_chart.dart';

class ImproveWorseComparisonScreen extends StatefulWidget {
  final MomentType momentType;
  final MomentData currentMoment;

  const ImproveWorseComparisonScreen({
    super.key,
    required this.momentType,
    required this.currentMoment,
  });

  @override
  State<ImproveWorseComparisonScreen> createState() => _ImproveWorseComparisonScreenState();
}

class _ImproveWorseComparisonScreenState extends State<ImproveWorseComparisonScreen> {
  // New factorial-based system
  bool _isImprovementComparedToLife = true; // Default to improvement
  double _factorialSliderValue = 1.0; // Default to life baseline (1x)
  final TextEditingController _evidenceController = TextEditingController();
  String _selectedPeriod = 'all'; // For chart period selection
  bool _isFirstMoment = true; // Track if this is the first moment

  @override
  void initState() {
    super.initState();
    _checkIfFirstMoment();
  }

  @override
  void dispose() {
    _evidenceController.dispose();
    super.dispose();
  }

  void _checkIfFirstMoment() {
    final momentsProvider = Provider.of<MomentsProvider>(context, listen: false);
    _isFirstMoment = momentsProvider.moments.isEmpty;

    // If this is the first moment, it must be improvement (more possibilities than life)
    // For subsequent moments, keep the default slider value or user's choice
    if (_isFirstMoment) {
      _isImprovementComparedToLife = true; // First moment is always improvement
      _factorialSliderValue = 2.0; // Start at 2x (above life baseline)
    } else {
      // For subsequent moments, start with a reasonable default
      if (_factorialSliderValue <= 1.0) {
        _factorialSliderValue = 2.0; // Start at 2x for subsequent moments
      }
    }
  }

  void _updateImprovementChoice(bool isImprovement) {
    setState(() {
      _isImprovementComparedToLife = isImprovement;
      // Reset slider value when switching modes
      if (isImprovement) {
        // More possibilities: start at 2x
        _factorialSliderValue = 2.0;
      } else {
        // Fewer possibilities: start at 2x (will subtract from previous moment)
        _factorialSliderValue = 2.0;
      }
    });
  }

  void _updateFactorialSlider(double value) {
    setState(() {
      // For first moment, ensure value is never below 2.0 (must be positive)
      if (_isFirstMoment && value < 2.0) {
        _factorialSliderValue = 2.0;
      } else {
        _factorialSliderValue = value;
      }
    });
  }



  void _updatePeriod(String period) {
    setState(() {
      _selectedPeriod = period;
    });
  }

  double? _getPreviousMomentValue(List<MomentData> moments) {
    if (moments.isEmpty) return null;

    // Get all saved moments excluding the current one being edited
    final savedMoments = moments.where((moment) => moment.id != widget.currentMoment.id).toList();
    if (savedMoments.isEmpty) return null;

    // Sort by timestamp and get the most recent saved moment
    savedMoments.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    return savedMoments.last.factorialMultiplier;
  }

  double _calculateFinalFactorialValue() {
    // Calculate factorial value from slider
    final factorialValue = _calculateFactorial(_factorialSliderValue);

    if (_isFirstMoment) {
      // For the first moment, return factorial value directly (compares to life baseline of 1x)
      return factorialValue;
    } else {
      // For subsequent moments, add to the previous moment's value (cumulative)
      final previousValue = _getPreviousMomentValue(Provider.of<MomentsProvider>(context, listen: false).moments);
      if (previousValue != null) {
        if (_isImprovementComparedToLife) {
          // More possibilities: add to previous moment
          return previousValue + factorialValue;
        } else {
          // Fewer possibilities: subtract from previous moment, but never go below 1x (life baseline)
          final result = previousValue - factorialValue;
          return math.max(1.0, result);
        }
      }
      // Fallback: if no previous moment found, treat as first moment
      return factorialValue;
    }
  }

  double _calculateFactorial(double sliderValue) {
    final clampedValue = sliderValue.clamp(1.0, 100.0);

    if (clampedValue <= 20.0) {
      int intValue = clampedValue.round();
      double result = 1.0;
      for (int i = 1; i <= intValue; i++) {
        result *= i;
      }
      return result;
    }

    final n = clampedValue;
    return math.sqrt(2 * math.pi * n) * math.pow(n / math.e, n);
  }

  void _saveMoment() {
    final momentsProvider = Provider.of<MomentsProvider>(context, listen: false);

    // Debug: Print the calculated values
    final finalFactorialValue = _calculateFinalFactorialValue();
    print('DEBUG: Saving moment with:');
    print('  - Slider value: $_factorialSliderValue');
    print('  - Is first moment: $_isFirstMoment');
    print('  - Is improvement: $_isImprovementComparedToLife');
    print('  - Final factorial value: $finalFactorialValue');

    // Create a new moment with the updated factorial comparison data
    final updatedMoment = MomentData(
      id: widget.currentMoment.id,
      timestamp: widget.currentMoment.timestamp,
      type: widget.currentMoment.type,
      title: widget.currentMoment.title,
      description: widget.currentMoment.description,
      elementalPercentage: widget.currentMoment.elementalPercentage,
      personalPercentage: widget.currentMoment.personalPercentage,
      informationalPercentage: widget.currentMoment.informationalPercentage,
      socialPercentage: widget.currentMoment.socialPercentage,
      inwardPercentage: widget.currentMoment.inwardPercentage,
      outwardPercentage: widget.currentMoment.outwardPercentage,
      elementalPersonalEvidence: widget.currentMoment.elementalPersonalEvidence,
      personalInformationalEvidence: widget.currentMoment.personalInformationalEvidence,
      informationalSocialEvidence: widget.currentMoment.informationalSocialEvidence,
      inwardOutwardEvidence: widget.currentMoment.inwardOutwardEvidence,
      momentTypeEvidence: _evidenceController.text,
      // New factorial-based fields - first moment is always improvement, others use user's choice
      isImprovementComparedToLife: _isFirstMoment ? true : _isImprovementComparedToLife,
      factorialSliderValue: _factorialSliderValue, // Store the slider value
      customFactorialMultiplier: _calculateFinalFactorialValue(), // Use our custom calculation
      // Keep old field for backward compatibility
      improvesWorsensRatio: 0.5,
    );

    // Update the moment in the provider
    momentsProvider.updateMoment(updatedMoment);

    // Navigate to the summary screen (Screen 3)
    Navigator.pushReplacementNamed(context, '/summary');
  }



  @override
  Widget build(BuildContext context) {
    final momentsProvider = Provider.of<MomentsProvider>(context);
    final allMoments = momentsProvider.moments;

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.lifePossibilitiesFactorTitle),
        actions: const [
          AppMenu(),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Highlight section (moved to first position)
            _buildHighlightSection(context),
            const SizedBox(height: 32),

            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    AppLocalizations.of(context)!.howManyPossibilitiesTitle,
                    style: const TextStyle(
                      fontSize: 16, // Further reduced font size
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.visible,
                    maxLines: 3,
                  ),
                ),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () => _showPossibilitiesExplanation(context),
                  child: const Icon(
                    Icons.info_outline,
                    color: Colors.blue,
                    size: 20,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),

            // Possibilities choice for subsequent moments
            if (!_isFirstMoment) ...[
              Card(
                color: Colors.grey[50],
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        AppLocalizations.of(context)!.comparedToLifeBaseline,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () => _updateImprovementChoice(true),
                              icon: const Icon(Icons.trending_up),
                              label: Text(AppLocalizations.of(context)!.morePossibilitiesButton),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: _isImprovementComparedToLife ? Colors.green[300] : Colors.grey[200],
                                foregroundColor: _isImprovementComparedToLife ? Colors.white : Colors.black,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () => _updateImprovementChoice(false),
                              icon: const Icon(Icons.trending_down),
                              label: Text(AppLocalizations.of(context)!.fewerPossibilitiesButton),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: !_isImprovementComparedToLife ? Colors.red[300] : Colors.grey[200],
                                foregroundColor: !_isImprovementComparedToLife ? Colors.white : Colors.black,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 32),
            ],

            // Factorial Slider
            FactorialSlider(
              value: _factorialSliderValue,
              onChanged: _updateFactorialSlider, // Always enabled
              isImprovement: _isFirstMoment ? true : _isImprovementComparedToLife, // Dynamic based on user choice
              title: _isFirstMoment ? AppLocalizations.of(context)!.vsLifeBaseline : '',
              minValue: _isFirstMoment ? 2.0 : 1.0, // First moment: 2x+, Others: 1x+
              previousMomentValue: _isFirstMoment ? null : _getPreviousMomentValue(allMoments),
            ),
            const SizedBox(height: 32),

            // Dynamic Chart showing all moments with current preview
            DynamicFactorialChart(
              moments: allMoments,
              currentSliderValue: _factorialSliderValue,
              isCurrentImprovement: _isFirstMoment ? true : _isImprovementComparedToLife, // Dynamic based on user choice
              selectedPeriod: _selectedPeriod,
              onPeriodChanged: _updatePeriod,
              currentPreviewValue: _calculateFinalFactorialValue(), // Use custom calculation for preview
              excludeMomentId: widget.currentMoment.id, // Exclude the current moment being edited
              showPreview: true, // Show preview in comparison screen
            ),
            const SizedBox(height: 32),

            // Evidence input
            Card(
              color: Colors.grey[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _isFirstMoment
                        ? AppLocalizations.of(context)!.explainWhyFirstMomentTitle
                        : AppLocalizations.of(context)!.provideEvidenceTitle,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _evidenceController,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context)!.pieStepEvidenceQuestion,
                        hintText: _isFirstMoment
                          ? AppLocalizations.of(context)!.firstMomentEvidenceHint
                          : AppLocalizations.of(context)!.comparisonEvidenceHint,
                        border: const OutlineInputBorder(),
                        fillColor: Colors.white,
                        filled: true,
                      ),
                      maxLines: 4,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 32),

            // Save button
            ElevatedButton(
              onPressed: _saveMoment,
              child: Text(AppLocalizations.of(context)!.continueButton),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildHighlightSection(BuildContext context) {
    final dominantValue = widget.currentMoment.highlightValue;
    final dominantColor = _getColorForLatentValue(dominantValue);

    return Card(
      color: dominantColor,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: Column(
            children: [
              // Hierarchical value names (smaller above, larger below)
              Text(
                _getDisplayName(context, dominantValue, isSmall: true),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _getDisplayName(context, dominantValue, isSmall: false),
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: () => _showValueDescription(context, dominantValue),
                    child: const Icon(
                      Icons.info_outline,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // User-given title
              Text(
                widget.currentMoment.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }



  String _getDisplayName(BuildContext context, String value, {required bool isSmall}) {
    final localizations = AppLocalizations.of(context)!;

    // Use localized display names and subtitles
    switch (value) {
      case 'Guardian':
        return isSmall ? localizations.nurturerSubtitle : localizations.guardianValue;
      case 'Warrior':
        return isSmall ? localizations.releaserSubtitle : localizations.warriorValue;
      case 'Versatile':
        return isSmall ? localizations.selfSeerSubtitle : localizations.versatileValue;
      case 'Fun':
        return isSmall ? localizations.enthusiasticSubtitle : localizations.funnyValue;
      case 'Strategist':
        return isSmall ? localizations.analystSubtitle : localizations.strategistValue;
      case 'Tactical':
        return isSmall ? localizations.synthesizerSubtitle : localizations.tacticianValue;
      case 'Altruist':
        return isSmall ? localizations.empatheticSubtitle : localizations.altruisticValue;
      case 'Collaborator':
        return isSmall ? localizations.diplomatSubtitle : localizations.collaboratorValue;
      default:
        return value;
    }
  }

  void _showPossibilitiesExplanation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context)!.lifePossibilitiesFactorTitle),
          content: SingleChildScrollView(
            child: Text(
              AppLocalizations.of(context)!.lifePossibilitiesExplanation.replaceAll('\\n', '\n'),
              style: const TextStyle(fontSize: 16, height: 1.5),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(AppLocalizations.of(context)!.closeButton),
            ),
          ],
        );
      },
    );
  }

  void _showValueDescription(BuildContext context, String latentValue) {
    final description = _getValueDescription(context, latentValue);
    final subtitle = _getValueSubtitle(context, latentValue);
    final localizedName = _getDisplayName(context, latentValue, isSmall: false);
    final localizations = AppLocalizations.of(context)!;
    final valueColor = _getColorForLatentValue(latentValue);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: valueColor,
          title: Text(
            subtitle.isNotEmpty
              ? '$subtitle $localizedName'.toUpperCase()
              : localizedName.toUpperCase(),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: Colors.white,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Show the explanation/description below the dual title
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                localizations.closeButton,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  String _getValueSubtitle(BuildContext context, String value) {
    final localizations = AppLocalizations.of(context)!;

    // Get the subtitle (small name) for each latent value
    switch (value) {
      case 'Guardian':
        return localizations.subtitleNurturer;
      case 'Warrior':
        return localizations.subtitleReleaser;
      case 'Versatile':
        return localizations.subtitleSelfSeer;
      case 'Fun':
        return localizations.subtitleEnthusiastic;
      case 'Strategist':
        return localizations.subtitleAnalyst;
      case 'Tactical':
        return localizations.subtitleSynthesizer;
      case 'Altruist':
        return localizations.subtitleEmpathetic;
      case 'Collaborator':
        return localizations.subtitleDiplomat;
      default:
        return '';
    }
  }

  String _getValueDescription(BuildContext context, String value) {
    final localizations = AppLocalizations.of(context)!;

    // Match exactly the descriptions from latent_values_screen.dart
    switch (value) {
      case 'Guardian':
        return localizations.guardianValueDescription.replaceAll('\\n', '\n');
      case 'Warrior':
        return localizations.warriorValueDescription.replaceAll('\\n', '\n');
      case 'Versatile':
        return localizations.versatileValueDescription.replaceAll('\\n', '\n');
      case 'Fun':
        return localizations.funValueDescription.replaceAll('\\n', '\n');
      case 'Strategist':
        return localizations.strategistValueDescription.replaceAll('\\n', '\n');
      case 'Tactical':
        return localizations.tacticalValueDescription.replaceAll('\\n', '\n');
      case 'Altruist':
        return localizations.altruistValueDescription.replaceAll('\\n', '\n');
      case 'Collaborator':
        return localizations.collaboratorValueDescription.replaceAll('\\n', '\n');
      default:
        return localizations.descriptionNotAvailable;
    }
  }

  Color _getColorForLatentValue(String value) {
    // Match exactly the colors from latent_values_screen.dart and summary_screen.dart
    switch (value) {
      case 'Guardian':
        return const Color(0xFF6A0F36);
      case 'Warrior':
        return const Color(0xFF5B1106);
      case 'Versatile':
        return const Color(0xFF332444);
      case 'Fun':
        return const Color(0xFF794B19);
      case 'Strategist':
        return const Color(0xFF1E293B);
      case 'Tactical':
        return const Color(0xFF5E4E0F);
      case 'Altruist':
        return const Color(0xFF0F5040);
      case 'Collaborator':
        return const Color(0xFF254110);
      default:
        return Colors.grey;
    }
  }
}
