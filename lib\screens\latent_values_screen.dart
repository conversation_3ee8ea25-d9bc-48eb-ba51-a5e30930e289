import 'package:flutter/material.dart';
import '../widgets/app_menu.dart';
import '../l10n/app_localizations.dart';

class LatentValuesScreen extends StatelessWidget {
  const LatentValuesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final locale = Localizations.localeOf(context);
    final languageCode = locale.languageCode;

    // Use proper localization instead of hardcoded translations

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.viewLatentValuesMenu),
        actions: const [
          AppMenu(),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Understanding explanation at the top
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20.0),
              margin: const EdgeInsets.only(bottom: 24.0),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    localizations.understandingLatentValuesTitle,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    localizations.understandingLatentValuesContent,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),

            // Header section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20.0),
              margin: const EdgeInsets.only(bottom: 24.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.deepPurple.shade100, Colors.deepPurple.shade50],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.deepPurple.shade200),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Text(
                      localizations.evolutiveFunctionsHeader,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      localizations.latentValuesSectionTitle,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Social Level
            _buildLevelSection(
              context,
              '4. ${localizations.socialLabel}',
              const Color(0xFF9F9F9F), // Social level color
              [
                _buildValueRow(
                  context,
                  '8',
                  localizations.functionCollaborate,
                  localizations.functionNegotiate,
                  localizations.valueCollaborator,
                  localizations.subtitleDiplomat,
                  const Color(0xFF254110),
                ),
                _buildValueRow(
                  context,
                  '7',
                  localizations.functionDeliberate,
                  localizations.functionEmpathize,
                  localizations.valueAltruistic,
                  localizations.subtitleEmpathetic,
                  const Color(0xFF0F5040),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Informational Level
            _buildLevelSection(
              context,
              '3. ${localizations.informationalLabel}',
              const Color(0xFF808080), // Informational level color
              [
                _buildValueRow(
                  context,
                  '6',
                  localizations.functionPredict,
                  localizations.functionAnalyze,
                  localizations.valueStrategist,
                  localizations.subtitleAnalyst,
                  const Color(0xFF1E293B),
                ),
                _buildValueRow(
                  context,
                  '5',
                  localizations.functionTrack,
                  localizations.functionSimplify,
                  localizations.valueTactician,
                  localizations.subtitleSynthesizer,
                  const Color(0xFF5E4E0F),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Personal Level
            _buildLevelSection(
              context,
              '2. ${localizations.personalLabel}',
              const Color(0xFF595959), // Personal level color
              [
                _buildValueRow(
                  context,
                  '4',
                  localizations.functionSelfEnjoy,
                  localizations.functionSelfMotivate,
                  localizations.valueFunny,
                  localizations.subtitleEnthusiastic,
                  const Color(0xFF794B19),
                ),
                _buildValueRow(
                  context,
                  '3',
                  localizations.functionSelfTransform,
                  localizations.functionSelfObserve,
                  localizations.valueVersatile,
                  localizations.subtitleSelfSeer,
                  const Color(0xFF332444),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Elemental Level
            _buildLevelSection(
              context,
              '1. ${localizations.elementalLabel}',
              const Color(0xFF404040), // Elemental level color
              [
                _buildValueRow(
                  context,
                  '2',
                  localizations.functionExecute,
                  localizations.functionDiscard,
                  localizations.valueWarrior,
                  localizations.subtitleReleaser,
                  const Color(0xFF5B1106),
                ),
                _buildValueRow(
                  context,
                  '1',
                  localizations.functionStore,
                  localizations.functionRecognize,
                  localizations.valueGuardian,
                  localizations.subtitleNurturer,
                  const Color(0xFF6A0F36),
                ),
              ],
            ),

          ],
        ),
      ),
    );
  }



  Widget _buildLevelSection(
    BuildContext context,
    String levelTitle,
    Color levelColor,
    List<Widget> valueRows,
  ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // Level header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: levelColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Text(
              levelTitle,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // Value rows
          ...valueRows,
        ],
      ),
    );
  }

  Widget _buildValueRow(
    BuildContext context,
    String number,
    String function,
    String subFunction,
    String latentValue,
    String latentSubtitle,
    Color latentColor,
  ) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Left side - Evolutive Functions
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border(
                  right: BorderSide(color: Colors.grey.shade300, width: 1),
                ),
              ),
              child: GestureDetector(
                onTap: () => _showValueDescription(context, latentValue),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: latentColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Text(
                          number,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            subFunction,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            function,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: latentColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.info_outline,
                      color: latentColor, // Use the same color as the latent value
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Right side - Latent Value
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: latentColor,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    latentSubtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.8),
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    latentValue,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16, // Reduced font size
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.8, // Reduced letter spacing
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2, // Allow text to wrap to 2 lines
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showValueDescription(BuildContext context, String latentValue) {
    final localizations = AppLocalizations.of(context)!;
    String description;
    Color valueColor;

    // Use localized value names to match against and get both description and color
    if (latentValue == localizations.valueGuardian) {
      description = localizations.descriptionGuardian;
      valueColor = const Color(0xFF6A0F36);
    } else if (latentValue == localizations.valueWarrior) {
      description = localizations.descriptionWarrior;
      valueColor = const Color(0xFF5B1106);
    } else if (latentValue == localizations.valueVersatile) {
      description = localizations.descriptionVersatile;
      valueColor = const Color(0xFF332444);
    } else if (latentValue == localizations.valueFunny) {
      description = localizations.descriptionFunny;
      valueColor = const Color(0xFF794B19);
    } else if (latentValue == localizations.valueStrategist) {
      description = localizations.descriptionStrategist;
      valueColor = const Color(0xFF1E293B);
    } else if (latentValue == localizations.valueTactician) {
      description = localizations.descriptionTactician;
      valueColor = const Color(0xFF5E4E0F);
    } else if (latentValue == localizations.valueAltruistic) {
      description = localizations.descriptionAltruistic;
      valueColor = const Color(0xFF0F5040);
    } else if (latentValue == localizations.valueCollaborator) {
      description = localizations.descriptionCollaborator;
      valueColor = const Color(0xFF254110);
    } else {
      description = localizations.descriptionNotAvailable;
      valueColor = Colors.grey;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: valueColor,
          title: Text(
            latentValue,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: Colors.white,
            ),
          ),
          content: SingleChildScrollView(
            child: Text(
              description,
              style: const TextStyle(
                fontSize: 16,
                height: 1.5,
                color: Colors.white,
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                localizations.closeButton,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }
}
